#!/bin/bash

# Ideary前端应用启动脚本

set -e

echo "=========================================="
echo "  Ideary Frontend Application 启动脚本"
echo "=========================================="

# 检查Node.js环境
if ! command -v node > /dev/null 2>&1; then
    echo "❌ Node.js未安装，请先安装Node.js 18+"
    exit 1
fi

# 检查Node.js版本
NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 18 ]; then
    echo "❌ Node.js版本过低，需要Node.js 18+，当前版本: $(node -v)"
    exit 1
fi

# 检查npm
if ! command -v npm > /dev/null 2>&1; then
    echo "❌ npm未安装，请先安装npm"
    exit 1
fi

echo "✅ Node.js环境检查通过，版本: $(node -v)"
echo "✅ npm版本: $(npm -v)"

# 检查是否存在package.json
if [ ! -f "package.json" ]; then
    echo "❌ 未找到package.json文件，请确保在正确的目录下运行此脚本"
    exit 1
fi

# 检查是否已安装依赖
if [ ! -d "node_modules" ]; then
    echo "📦 首次运行，正在安装依赖..."
    npm install
else
    echo "📦 检查依赖更新..."
    npm ci
fi

# 检查环境变量文件
if [ ! -f ".env.local" ]; then
    echo "⚙️  创建环境变量文件..."
    cat > .env.local << EOF
# Ideary Frontend Environment Variables

# API配置
NEXT_PUBLIC_API_URL=http://localhost:9080/api/v1
NEXT_PUBLIC_WS_URL=ws://localhost:9080/ws

# 应用配置
NEXT_PUBLIC_APP_NAME=Ideary
NEXT_PUBLIC_APP_VERSION=1.0.0
NEXT_PUBLIC_APP_ENV=development

# 第三方服务配置
NEXT_PUBLIC_GOOGLE_ANALYTICS_ID=
NEXT_PUBLIC_SENTRY_DSN=

# 功能开关
NEXT_PUBLIC_ENABLE_PWA=true
NEXT_PUBLIC_ENABLE_ANALYTICS=false
NEXT_PUBLIC_ENABLE_ERROR_REPORTING=false

# 开发配置
NEXT_PUBLIC_DEBUG=true
NEXT_PUBLIC_LOG_LEVEL=debug
EOF
    echo "✅ 已创建 .env.local 文件"
fi

# 类型检查
echo "🔍 执行TypeScript类型检查..."
npm run type-check

# 代码检查
echo "🔍 执行ESLint代码检查..."
npm run lint

# 构建检查（可选）
if [ "$1" = "--build" ]; then
    echo "🔨 执行构建检查..."
    npm run build
    echo "✅ 构建检查通过"
    exit 0
fi

# 启动开发服务器
echo "🚀 启动开发服务器..."
echo ""
echo "📋 应用信息："
echo "   应用名称: Ideary Frontend"
echo "   开发地址: http://localhost:3000"
echo "   API地址: http://localhost:9080/api/v1"
echo ""
echo "🔧 可用命令："
echo "   npm run dev        - 启动开发服务器"
echo "   npm run build      - 构建生产版本"
echo "   npm run start      - 启动生产服务器"
echo "   npm run lint       - 代码检查"
echo "   npm run type-check - 类型检查"
echo "   npm run test       - 运行测试"
echo ""
echo "📝 开发提示："
echo "   - 修改代码后会自动热重载"
echo "   - 按 Ctrl+C 停止服务器"
echo "   - 查看 README.md 了解更多信息"
echo ""

# 启动开发服务器
npm run dev
