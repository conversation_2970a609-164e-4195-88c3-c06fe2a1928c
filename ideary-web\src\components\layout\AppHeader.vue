<template>
  <header class="app-header">
    <div class="header-container">
      <!-- Logo -->
      <div class="header-logo">
        <router-link to="/" class="logo-link">
          <div class="logo-icon">
            <span>I</span>
          </div>
          <span class="logo-text">Ideary</span>
        </router-link>
      </div>

      <!-- 桌面端导航 -->
      <nav class="header-nav">
        <router-link
          v-for="item in navigation"
          :key="item.name"
          :to="item.path"
          class="nav-item"
          :class="{ active: $route.path === item.path }"
        >
          {{ item.name }}
        </router-link>
      </nav>

      <!-- 右侧操作区 -->
      <div class="header-actions">
        <!-- 搜索按钮 -->
        <el-button
          type="text"
          @click="openSearch"
          class="search-btn"
        >
          <el-icon><Search /></el-icon>
          <span class="search-text">搜索...</span>
          <kbd class="search-kbd">⌘K</kbd>
        </el-button>

        <!-- 主题切换 -->
        <el-button
          type="text"
          @click="toggleTheme"
          class="theme-btn"
        >
          <el-icon>
            <Sunny v-if="isDark" />
            <Moon v-else />
          </el-icon>
        </el-button>

        <!-- 用户菜单或登录按钮 -->
        <div v-if="userStore.isAuthenticated" class="user-menu">
          <el-button
            type="primary"
            @click="$router.push('/write')"
            class="write-btn"
          >
            <el-icon><Edit /></el-icon>
            <span>写文章</span>
          </el-button>
          
          <el-dropdown @command="handleUserCommand">
            <div class="user-avatar">
              <el-avatar
                :src="userStore.userInfo?.avatar"
                :size="32"
              >
                {{ userStore.userInfo?.nickname?.[0] || userStore.userInfo?.username?.[0] }}
              </el-avatar>
            </div>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="profile">
                  <el-icon><User /></el-icon>
                  个人资料
                </el-dropdown-item>
                <el-dropdown-item command="settings">
                  <el-icon><Setting /></el-icon>
                  设置
                </el-dropdown-item>
                <el-dropdown-item divided command="logout">
                  <el-icon><SwitchButton /></el-icon>
                  登出
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>

        <div v-else class="auth-buttons">
          <el-button @click="$router.push('/auth/login')">
            登录
          </el-button>
          <el-button type="primary" @click="$router.push('/auth/register')">
            注册
          </el-button>
        </div>

        <!-- 移动端菜单按钮 -->
        <el-button
          type="text"
          @click="toggleMobileMenu"
          class="mobile-menu-btn"
        >
          <el-icon>
            <Close v-if="showMobileMenu" />
            <Menu v-else />
          </el-icon>
        </el-button>
      </div>
    </div>

    <!-- 移动端菜单 -->
    <transition name="slide-down">
      <div v-if="showMobileMenu" class="mobile-menu">
        <nav class="mobile-nav">
          <router-link
            v-for="item in navigation"
            :key="item.name"
            :to="item.path"
            class="mobile-nav-item"
            @click="closeMobileMenu"
          >
            {{ item.name }}
          </router-link>
        </nav>
        
        <div class="mobile-actions">
          <el-button @click="openSearch" class="mobile-search">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          
          <div v-if="userStore.isAuthenticated" class="mobile-user-actions">
            <el-button type="primary" @click="$router.push('/write')">
              <el-icon><Edit /></el-icon>
              写文章
            </el-button>
          </div>
        </div>
      </div>
    </transition>

    <!-- 搜索模态框 -->
    <SearchModal v-model="searchVisible" />
  </header>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { useDark, useToggle } from '@vueuse/core'
import {
  Search,
  Sunny,
  Moon,
  Edit,
  User,
  Setting,
  SwitchButton,
  Menu,
  Close
} from '@element-plus/icons-vue'
import { useUserStore } from '@/stores/user'
import SearchModal from '@/components/common/SearchModal.vue'

const router = useRouter()
const userStore = useUserStore()

// 主题切换
const isDark = useDark()
const toggleTheme = useToggle(isDark)

// 移动端菜单
const showMobileMenu = ref(false)

// 搜索模态框
const searchVisible = ref(false)

// 导航菜单
const navigation = [
  { name: '首页', path: '/' },
  { name: '文章', path: '/articles' },
  { name: '话题', path: '/topics' },
  { name: '作者', path: '/authors' },
  { name: '关于', path: '/about' },
]

// 打开搜索
const openSearch = () => {
  searchVisible.value = true
  closeMobileMenu()
}

// 切换移动端菜单
const toggleMobileMenu = () => {
  showMobileMenu.value = !showMobileMenu.value
}

// 关闭移动端菜单
const closeMobileMenu = () => {
  showMobileMenu.value = false
}

// 处理用户菜单命令
const handleUserCommand = (command: string) => {
  switch (command) {
    case 'profile':
      router.push('/profile')
      break
    case 'settings':
      router.push('/settings')
      break
    case 'logout':
      userStore.logout()
      router.push('/')
      break
  }
}

// 键盘快捷键
onMounted(() => {
  const handleKeydown = (e: KeyboardEvent) => {
    if ((e.metaKey || e.ctrlKey) && e.key === 'k') {
      e.preventDefault()
      openSearch()
    }
  }
  
  document.addEventListener('keydown', handleKeydown)
  
  onUnmounted(() => {
    document.removeEventListener('keydown', handleKeydown)
  })
})
</script>

<style lang="scss" scoped>
.app-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid var(--el-border-color-light);
  transition: all 0.3s ease;
}

.header-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.header-logo {
  .logo-link {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    text-decoration: none;
    
    .logo-icon {
      width: 2rem;
      height: 2rem;
      background: linear-gradient(135deg, #3b82f6, #1d4ed8);
      border-radius: 0.5rem;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-weight: bold;
      font-size: 1.125rem;
    }
    
    .logo-text {
      font-size: 1.25rem;
      font-weight: bold;
      color: var(--el-text-color-primary);
    }
  }
}

.header-nav {
  display: flex;
  gap: 2rem;
  
  @media (max-width: 768px) {
    display: none;
  }
  
  .nav-item {
    color: var(--el-text-color-regular);
    text-decoration: none;
    font-weight: 500;
    transition: color 0.2s ease;
    
    &:hover,
    &.active {
      color: var(--el-color-primary);
    }
  }
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  
  .search-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    border: 1px solid var(--el-border-color);
    border-radius: 0.5rem;
    background: var(--el-bg-color);
    
    @media (max-width: 640px) {
      display: none;
    }
    
    .search-text {
      color: var(--el-text-color-placeholder);
    }
    
    .search-kbd {
      padding: 0.125rem 0.25rem;
      background: var(--el-fill-color-light);
      border-radius: 0.25rem;
      font-size: 0.75rem;
      color: var(--el-text-color-regular);
    }
  }
  
  .theme-btn {
    padding: 0.5rem;
  }
  
  .user-menu {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    
    .write-btn {
      @media (max-width: 640px) {
        display: none;
      }
    }
    
    .user-avatar {
      cursor: pointer;
    }
  }
  
  .auth-buttons {
    display: flex;
    gap: 0.5rem;
    
    @media (max-width: 640px) {
      display: none;
    }
  }
  
  .mobile-menu-btn {
    display: none;
    padding: 0.5rem;
    
    @media (max-width: 768px) {
      display: flex;
    }
  }
}

.mobile-menu {
  background: var(--el-bg-color);
  border-bottom: 1px solid var(--el-border-color-light);
  padding: 1rem;
  
  .mobile-nav {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin-bottom: 1rem;
    
    .mobile-nav-item {
      color: var(--el-text-color-regular);
      text-decoration: none;
      font-weight: 500;
      padding: 0.5rem 0;
      
      &:hover {
        color: var(--el-color-primary);
      }
    }
  }
  
  .mobile-actions {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    
    .mobile-search {
      width: 100%;
      justify-content: flex-start;
    }
    
    .mobile-user-actions {
      display: flex;
      gap: 0.5rem;
    }
  }
}

// 动画
.slide-down-enter-active,
.slide-down-leave-active {
  transition: all 0.3s ease;
}

.slide-down-enter-from,
.slide-down-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}

// 暗色模式
.dark .app-header {
  background: rgba(0, 0, 0, 0.8);
}
</style>
