#!/bin/bash

# Ideary Vue 3 前端应用启动脚本

set -e

echo "=========================================="
echo "  🚀 Ideary Vue 3 Frontend Application"
echo "  启动脚本"
echo "=========================================="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查命令是否存在
check_command() {
    if ! command -v $1 > /dev/null 2>&1; then
        log_error "$1 未安装，请先安装 $1"
        return 1
    fi
    return 0
}

# 检查端口是否被占用
check_port() {
    if netstat -tuln 2>/dev/null | grep -q ":$1 " || lsof -ti:$1 2>/dev/null; then
        log_warning "端口 $1 已被占用"
        return 1
    fi
    return 0
}

# 显示帮助信息
show_help() {
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  --help, -h          显示帮助信息"
    echo "  --check             仅检查环境，不启动服务"
    echo "  --install           安装依赖"
    echo "  --build             构建生产版本"
    echo "  --preview           预览构建结果"
    echo "  --clean             清理依赖和构建文件"
    echo ""
    echo "示例:"
    echo "  $0                  启动开发服务器"
    echo "  $0 --check          检查环境"
    echo "  $0 --build          构建生产版本"
}

# 环境检查
check_environment() {
    log_info "检查运行环境..."
    
    # 检查Node.js
    if ! check_command "node"; then
        log_error "请安装 Node.js 18+"
        exit 1
    fi
    
    # 检查Node.js版本
    NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
    if [ "$NODE_VERSION" -lt 18 ]; then
        log_error "Node.js版本过低，需要Node.js 18+，当前版本: $(node -v)"
        exit 1
    fi
    
    # 检查npm
    if ! check_command "npm"; then
        log_error "请安装 npm"
        exit 1
    fi
    
    log_success "环境检查通过"
    log_info "Node.js版本: $(node -v)"
    log_info "npm版本: $(npm -v)"
}

# 安装依赖
install_dependencies() {
    log_info "安装项目依赖..."
    
    if [ ! -f "package.json" ]; then
        log_error "未找到 package.json 文件"
        exit 1
    fi
    
    # 检查是否需要安装依赖
    if [ ! -d "node_modules" ] || [ "package.json" -nt "node_modules" ]; then
        log_info "正在安装依赖..."
        npm install
        log_success "依赖安装完成"
    else
        log_info "依赖已是最新版本"
    fi
}

# 类型检查
type_check() {
    log_info "执行 TypeScript 类型检查..."
    npm run type-check
    log_success "类型检查通过"
}

# 代码检查
lint_check() {
    log_info "执行 ESLint 代码检查..."
    npm run lint
    log_success "代码检查通过"
}

# 构建项目
build_project() {
    log_info "构建生产版本..."
    npm run build
    log_success "构建完成"
}

# 预览构建结果
preview_build() {
    log_info "预览构建结果..."
    if [ ! -d "dist" ]; then
        log_error "未找到构建文件，请先运行构建"
        exit 1
    fi
    npm run preview
}

# 清理项目
clean_project() {
    log_info "清理项目文件..."
    
    if [ -d "node_modules" ]; then
        rm -rf node_modules
        log_info "已删除 node_modules"
    fi
    
    if [ -d "dist" ]; then
        rm -rf dist
        log_info "已删除 dist"
    fi
    
    if [ -f "package-lock.json" ]; then
        rm -f package-lock.json
        log_info "已删除 package-lock.json"
    fi
    
    log_success "清理完成"
}

# 启动开发服务器
start_dev_server() {
    log_info "启动开发服务器..."
    
    # 检查端口
    if ! check_port 3000; then
        log_warning "端口 3000 被占用，将尝试使用其他端口"
    fi
    
    echo ""
    echo "📋 应用信息："
    echo "   应用名称: Ideary Vue 3 Frontend"
    echo "   开发地址: http://localhost:3000"
    echo "   API地址: http://localhost:9080/api/v1"
    echo ""
    echo "🔧 可用命令："
    echo "   npm run dev        - 启动开发服务器"
    echo "   npm run build      - 构建生产版本"
    echo "   npm run preview    - 预览构建结果"
    echo "   npm run lint       - 代码检查"
    echo "   npm run type-check - 类型检查"
    echo ""
    echo "📝 开发提示："
    echo "   - 修改代码后会自动热重载"
    echo "   - 按 Ctrl+C 停止服务器"
    echo "   - 查看 README.md 了解更多信息"
    echo ""
    
    # 启动开发服务器
    npm run dev
}

# 解析命令行参数
case "${1:-}" in
    --help|-h)
        show_help
        exit 0
        ;;
    --check)
        check_environment
        exit 0
        ;;
    --install)
        check_environment
        install_dependencies
        exit 0
        ;;
    --build)
        check_environment
        install_dependencies
        type_check
        lint_check
        build_project
        exit 0
        ;;
    --preview)
        check_environment
        preview_build
        exit 0
        ;;
    --clean)
        clean_project
        exit 0
        ;;
    "")
        # 默认启动开发服务器
        check_environment
        install_dependencies
        type_check
        lint_check
        start_dev_server
        ;;
    *)
        log_error "未知选项: $1"
        show_help
        exit 1
        ;;
esac
