/**
 * 用户认证相关类型定义
 */

export interface User {
  id: number
  username: string
  email: string
  nickname?: string
  avatar?: string
  roles: string[]
  emailVerified?: boolean
  phoneVerified?: boolean
  status?: number
  createdAt?: string
  updatedAt?: string
}

export interface LoginRequest {
  account: string
  password: string
  captcha?: string
  captchaKey?: string
  rememberMe?: boolean
}

export interface LoginResponse {
  userId: number
  username: string
  email: string
  nickname?: string
  avatar?: string
  roles: string[]
  accessToken: string
  refreshToken: string
  tokenType: string
  expiresIn: number
  loginTime: string
  firstLogin?: boolean
}

export interface RegisterRequest {
  username: string
  email: string
  password: string
  confirmPassword: string
  emailCode: string
  nickname?: string
  phone?: string
  agreeTerms: boolean
}

export interface PasswordChangeRequest {
  oldPassword: string
  newPassword: string
  confirmPassword: string
}

export interface PasswordResetRequest {
  email: string
  code: string
  newPassword: string
  confirmPassword: string
}

export interface EmailVerificationRequest {
  email: string
  type: 'register' | 'reset_password' | 'verify_email' | 'change_email'
}

export interface UserProfile {
  id: number
  username: string
  email: string
  nickname?: string
  avatar?: string
  realName?: string
  gender?: number
  birthday?: string
  bio?: string
  company?: string
  position?: string
  location?: string
  website?: string
  github?: string
  weibo?: string
  wechat?: string
  followingCount: number
  followersCount: number
  articlesCount: number
  likesCount: number
  emailVerified: boolean
  phoneVerified: boolean
  status: number
  createdAt: string
  updatedAt: string
}

export interface UserUpdateRequest {
  nickname?: string
  avatar?: string
}

export interface UserProfileUpdateRequest {
  realName?: string
  gender?: number
  birthday?: string
  bio?: string
  company?: string
  position?: string
  location?: string
  website?: string
  github?: string
  weibo?: string
  wechat?: string
}

export interface UserStats {
  userId: number
  followingCount: number
  followersCount: number
  mutualFollowsCount: number
  articlesCount: number
  publishedArticlesCount: number
  draftArticlesCount: number
  totalLikesCount: number
  totalFavoritesCount: number
  totalCommentsCount: number
  totalViewsCount: number
  todayFollowersCount: number
  todayViewsCount: number
  todayLikesCount: number
  weekFollowersCount: number
  weekViewsCount: number
  monthFollowersCount: number
  monthViewsCount: number
}
