<template>
  <el-dialog
    v-model="visible"
    :show-close="false"
    :close-on-click-modal="true"
    :close-on-press-escape="true"
    width="600px"
    top="10vh"
    class="search-modal"
  >
    <template #header>
      <div class="search-header">
        <div class="search-input-wrapper">
          <el-icon class="search-icon"><Search /></el-icon>
          <input
            ref="searchInput"
            v-model="searchQuery"
            type="text"
            placeholder="搜索文章、作者或话题..."
            class="search-input"
            @input="handleSearch"
            @keydown="handleKeydown"
          />
          <el-button
            type="text"
            @click="closeModal"
            class="close-btn"
          >
            <el-icon><Close /></el-icon>
          </el-button>
        </div>
      </div>
    </template>

    <div class="search-content">
      <!-- 加载状态 -->
      <div v-if="loading" class="search-loading">
        <el-icon class="is-loading"><Loading /></el-icon>
        <span>搜索中...</span>
      </div>

      <!-- 搜索结果 -->
      <div v-else-if="searchQuery && results.length > 0" class="search-results">
        <div
          v-for="(result, index) in results"
          :key="`${result.type}-${result.id}`"
          :class="['result-item', { active: selectedIndex === index }]"
          @click="selectResult(result)"
          @mouseenter="selectedIndex = index"
        >
          <div class="result-icon">
            <el-icon v-if="result.type === 'article'"><Document /></el-icon>
            <el-icon v-else-if="result.type === 'author'"><User /></el-icon>
            <el-icon v-else-if="result.type === 'topic'"><Collection /></el-icon>
          </div>
          
          <div class="result-content">
            <div class="result-title">{{ result.title }}</div>
            <div v-if="result.subtitle" class="result-subtitle">
              {{ result.subtitle }}
            </div>
          </div>
          
          <div class="result-type">
            {{ getTypeLabel(result.type) }}
          </div>
        </div>
      </div>

      <!-- 无结果 -->
      <div v-else-if="searchQuery && !loading" class="no-results">
        <el-icon><Search /></el-icon>
        <p>没有找到相关结果</p>
      </div>

      <!-- 最近搜索 -->
      <div v-else-if="recentSearches.length > 0" class="recent-searches">
        <div class="section-title">最近搜索</div>
        <div
          v-for="(search, index) in recentSearches"
          :key="index"
          class="recent-item"
          @click="searchQuery = search"
        >
          <el-icon><Clock /></el-icon>
          <span>{{ search }}</span>
        </div>
      </div>

      <!-- 空状态 */
      <div v-else class="empty-state">
        <el-icon><Search /></el-icon>
        <p>开始搜索文章、作者或话题</p>
      </div>
    </div>

    <template #footer>
      <div class="search-footer">
        <div class="search-tips">
          <span>按 Enter 搜索</span>
          <span>按 Esc 关闭</span>
        </div>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { debounce } from 'lodash-es'
import {
  Search,
  Close,
  Loading,
  Document,
  User,
  Collection,
  Clock
} from '@element-plus/icons-vue'

interface SearchResult {
  type: 'article' | 'author' | 'topic'
  id: number
  title: string
  subtitle?: string
  url: string
}

const props = defineProps<{
  modelValue: boolean
}>()

const emit = defineEmits<{
  'update:modelValue': [value: boolean]
}>()

const router = useRouter()

// 响应式数据
const searchInput = ref<HTMLInputElement>()
const searchQuery = ref('')
const results = ref<SearchResult[]>([])
const loading = ref(false)
const selectedIndex = ref(0)
const recentSearches = ref<string[]>([])

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 模拟搜索API
const searchApi = async (query: string): Promise<SearchResult[]> => {
  if (!query.trim()) return []
  
  // 模拟API延迟
  await new Promise(resolve => setTimeout(resolve, 300))
  
  // 模拟搜索结果
  const mockResults: SearchResult[] = [
    {
      type: 'article',
      id: 1,
      title: 'React 18 新特性深度解析',
      subtitle: '深入探讨 React 18 带来的革命性变化...',
      url: '/articles/1',
    },
    {
      type: 'author',
      id: 1,
      title: 'React 大师',
      subtitle: '@react_master · 156 篇文章',
      url: '/authors/react_master',
    },
    {
      type: 'topic',
      id: 1,
      title: 'React',
      subtitle: '1,250 篇文章 · 8,900 关注者',
      url: '/topics/react',
    },
  ].filter(item => 
    item.title.toLowerCase().includes(query.toLowerCase()) ||
    (item.subtitle && item.subtitle.toLowerCase().includes(query.toLowerCase()))
  )

  return mockResults
}

// 防抖搜索
const debouncedSearch = debounce(async (query: string) => {
  if (!query.trim()) {
    results.value = []
    loading.value = false
    return
  }

  loading.value = true
  try {
    const searchResults = await searchApi(query)
    results.value = searchResults
    selectedIndex.value = 0
  } catch (error) {
    console.error('搜索失败:', error)
    results.value = []
  } finally {
    loading.value = false
  }
}, 300)

// 处理搜索输入
const handleSearch = () => {
  debouncedSearch(searchQuery.value)
}

// 处理键盘事件
const handleKeydown = (e: KeyboardEvent) => {
  switch (e.key) {
    case 'ArrowDown':
      e.preventDefault()
      selectedIndex.value = Math.min(selectedIndex.value + 1, results.value.length - 1)
      break
    case 'ArrowUp':
      e.preventDefault()
      selectedIndex.value = Math.max(selectedIndex.value - 1, 0)
      break
    case 'Enter':
      e.preventDefault()
      if (results.value[selectedIndex.value]) {
        selectResult(results.value[selectedIndex.value])
      } else if (searchQuery.value.trim()) {
        performSearch(searchQuery.value)
      }
      break
    case 'Escape':
      closeModal()
      break
  }
}

// 选择结果
const selectResult = (result: SearchResult) => {
  addToRecentSearches(searchQuery.value)
  router.push(result.url)
  closeModal()
}

// 执行搜索
const performSearch = (query: string) => {
  if (!query.trim()) return
  
  addToRecentSearches(query)
  router.push(`/search?q=${encodeURIComponent(query)}`)
  closeModal()
}

// 添加到最近搜索
const addToRecentSearches = (query: string) => {
  const trimmedQuery = query.trim()
  if (!trimmedQuery) return
  
  const newRecentSearches = [
    trimmedQuery,
    ...recentSearches.value.filter(item => item !== trimmedQuery)
  ].slice(0, 5)
  
  recentSearches.value = newRecentSearches
  localStorage.setItem('ideary-recent-searches', JSON.stringify(newRecentSearches))
}

// 获取类型标签
const getTypeLabel = (type: string) => {
  switch (type) {
    case 'article':
      return '文章'
    case 'author':
      return '作者'
    case 'topic':
      return '话题'
    default:
      return ''
  }
}

// 关闭模态框
const closeModal = () => {
  visible.value = false
  searchQuery.value = ''
  results.value = []
  selectedIndex.value = 0
}

// 监听模态框显示状态
watch(visible, async (newVisible) => {
  if (newVisible) {
    await nextTick()
    searchInput.value?.focus()
  }
})

// 初始化最近搜索
onMounted(() => {
  const saved = localStorage.getItem('ideary-recent-searches')
  if (saved) {
    try {
      recentSearches.value = JSON.parse(saved)
    } catch (error) {
      console.error('解析最近搜索失败:', error)
    }
  }
})
</script>

<style lang="scss" scoped>
:deep(.search-modal) {
  .el-dialog__header {
    padding: 0;
    margin: 0;
  }
  
  .el-dialog__body {
    padding: 0;
    max-height: 400px;
    overflow-y: auto;
  }
  
  .el-dialog__footer {
    padding: 0.75rem 1rem;
    border-top: 1px solid var(--el-border-color-light);
  }
}

.search-header {
  padding: 1rem;
  border-bottom: 1px solid var(--el-border-color-light);
  
  .search-input-wrapper {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    
    .search-icon {
      color: var(--el-text-color-placeholder);
      font-size: 1.25rem;
    }
    
    .search-input {
      flex: 1;
      border: none;
      outline: none;
      font-size: 1rem;
      color: var(--el-text-color-primary);
      background: transparent;
      
      &::placeholder {
        color: var(--el-text-color-placeholder);
      }
    }
    
    .close-btn {
      padding: 0.25rem;
    }
  }
}

.search-content {
  min-height: 200px;
  
  .search-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 2rem;
    color: var(--el-text-color-regular);
  }
  
  .search-results {
    .result-item {
      display: flex;
      align-items: center;
      gap: 0.75rem;
      padding: 0.75rem 1rem;
      cursor: pointer;
      transition: background-color 0.2s ease;
      
      &:hover,
      &.active {
        background: var(--el-fill-color-light);
      }
      
      .result-icon {
        color: var(--el-color-primary);
        font-size: 1.125rem;
      }
      
      .result-content {
        flex: 1;
        min-width: 0;
        
        .result-title {
          font-weight: 500;
          color: var(--el-text-color-primary);
          margin-bottom: 0.25rem;
        }
        
        .result-subtitle {
          font-size: 0.875rem;
          color: var(--el-text-color-regular);
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
      
      .result-type {
        font-size: 0.75rem;
        color: var(--el-text-color-placeholder);
        background: var(--el-fill-color);
        padding: 0.25rem 0.5rem;
        border-radius: 0.25rem;
      }
    }
  }
  
  .no-results,
  .empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 0.75rem;
    padding: 2rem;
    color: var(--el-text-color-regular);
    
    .el-icon {
      font-size: 2rem;
      color: var(--el-text-color-placeholder);
    }
  }
  
  .recent-searches {
    padding: 1rem;
    
    .section-title {
      font-size: 0.75rem;
      font-weight: 600;
      color: var(--el-text-color-regular);
      text-transform: uppercase;
      letter-spacing: 0.05em;
      margin-bottom: 0.75rem;
    }
    
    .recent-item {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      padding: 0.5rem;
      cursor: pointer;
      border-radius: 0.25rem;
      transition: background-color 0.2s ease;
      
      &:hover {
        background: var(--el-fill-color-light);
      }
      
      .el-icon {
        color: var(--el-text-color-placeholder);
        font-size: 1rem;
      }
      
      span {
        color: var(--el-text-color-regular);
        font-size: 0.875rem;
      }
    }
  }
}

.search-footer {
  .search-tips {
    display: flex;
    justify-content: space-between;
    font-size: 0.75rem;
    color: var(--el-text-color-placeholder);
  }
}
</style>
