<template>
  <div class="not-found-page">
    <div class="not-found-container">
      <div class="not-found-content">
        <!-- 404 图标 -->
        <div class="error-icon">
          <span class="error-number">404</span>
        </div>
        
        <!-- 错误信息 -->
        <h1 class="error-title">页面未找到</h1>
        <p class="error-description">
          抱歉，您访问的页面不存在或已被移动。
        </p>
        
        <!-- 建议操作 -->
        <div class="suggestions">
          <h3>您可以尝试：</h3>
          <ul>
            <li>检查网址是否正确</li>
            <li>返回首页重新导航</li>
            <li>使用搜索功能查找内容</li>
            <li>联系我们获取帮助</li>
          </ul>
        </div>
        
        <!-- 操作按钮 -->
        <div class="actions">
          <el-button type="primary" size="large" @click="goHome">
            <el-icon><HomeFilled /></el-icon>
            返回首页
          </el-button>
          <el-button size="large" @click="goBack">
            <el-icon><Back /></el-icon>
            返回上页
          </el-button>
          <el-button size="large" @click="openSearch">
            <el-icon><Search /></el-icon>
            搜索内容
          </el-button>
        </div>
        
        <!-- 热门链接 -->
        <div class="popular-links">
          <h3>热门页面</h3>
          <div class="links-grid">
            <router-link to="/articles" class="link-item">
              <el-icon><Document /></el-icon>
              <span>技术文章</span>
            </router-link>
            <router-link to="/topics" class="link-item">
              <el-icon><Collection /></el-icon>
              <span>热门话题</span>
            </router-link>
            <router-link to="/authors" class="link-item">
              <el-icon><User /></el-icon>
              <span>优秀作者</span>
            </router-link>
            <router-link to="/about" class="link-item">
              <el-icon><InfoFilled /></el-icon>
              <span>关于我们</span>
            </router-link>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { 
  HomeFilled, 
  Back, 
  Search, 
  Document, 
  Collection, 
  User, 
  InfoFilled 
} from '@element-plus/icons-vue'

const router = useRouter()

// 返回首页
const goHome = () => {
  router.push('/')
}

// 返回上一页
const goBack = () => {
  if (window.history.length > 1) {
    router.go(-1)
  } else {
    router.push('/')
  }
}

// 打开搜索
const openSearch = () => {
  // 这里可以触发全局搜索模态框
  router.push('/search')
}

onMounted(() => {
  document.title = '页面未找到 - Ideary'
})
</script>

<style lang="scss" scoped>
.not-found-page {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, 
    rgba(59, 130, 246, 0.1) 0%, 
    rgba(255, 255, 255, 0.9) 50%, 
    rgba(16, 185, 129, 0.1) 100%);
}

.not-found-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 2rem;
}

.not-found-content {
  text-align: center;
  background: var(--el-bg-color);
  border-radius: 1.5rem;
  padding: 3rem;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  
  .error-icon {
    margin-bottom: 2rem;
    
    .error-number {
      font-size: 8rem;
      font-weight: bold;
      background: linear-gradient(135deg, #3b82f6, #1d4ed8);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      line-height: 1;
      
      @media (max-width: 768px) {
        font-size: 6rem;
      }
    }
  }
  
  .error-title {
    font-size: 2.5rem;
    font-weight: bold;
    color: var(--el-text-color-primary);
    margin-bottom: 1rem;
    
    @media (max-width: 768px) {
      font-size: 2rem;
    }
  }
  
  .error-description {
    font-size: 1.125rem;
    color: var(--el-text-color-regular);
    margin-bottom: 2rem;
    line-height: 1.6;
  }
  
  .suggestions {
    text-align: left;
    max-width: 400px;
    margin: 0 auto 2rem;
    padding: 1.5rem;
    background: var(--el-fill-color-lighter);
    border-radius: 1rem;
    
    h3 {
      font-size: 1.125rem;
      font-weight: 600;
      color: var(--el-text-color-primary);
      margin-bottom: 1rem;
    }
    
    ul {
      list-style: none;
      padding: 0;
      margin: 0;
      
      li {
        color: var(--el-text-color-regular);
        margin-bottom: 0.5rem;
        position: relative;
        padding-left: 1.5rem;
        
        &::before {
          content: '•';
          color: var(--el-color-primary);
          font-weight: bold;
          position: absolute;
          left: 0;
        }
      }
    }
  }
  
  .actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-bottom: 3rem;
    flex-wrap: wrap;
    
    @media (max-width: 640px) {
      flex-direction: column;
      align-items: center;
    }
  }
  
  .popular-links {
    h3 {
      font-size: 1.25rem;
      font-weight: 600;
      color: var(--el-text-color-primary);
      margin-bottom: 1.5rem;
    }
    
    .links-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
      gap: 1rem;
      
      .link-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 0.5rem;
        padding: 1rem;
        background: var(--el-fill-color-light);
        border-radius: 0.75rem;
        text-decoration: none;
        color: var(--el-text-color-regular);
        transition: all 0.3s ease;
        
        &:hover {
          background: var(--el-color-primary);
          color: white;
          transform: translateY(-2px);
        }
        
        .el-icon {
          font-size: 1.5rem;
        }
        
        span {
          font-size: 0.875rem;
          font-weight: 500;
        }
      }
    }
  }
}

// 暗色模式适配
.dark {
  .not-found-page {
    background: linear-gradient(135deg, 
      rgba(59, 130, 246, 0.2) 0%, 
      rgba(0, 0, 0, 0.9) 50%, 
      rgba(16, 185, 129, 0.2) 100%);
  }
}
</style>
