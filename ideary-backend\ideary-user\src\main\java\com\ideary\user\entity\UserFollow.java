package com.ideary.user.entity;

import com.baomidou.mybatisplus.annotation.*;
import jakarta.persistence.*;

import java.time.LocalDateTime;
import java.util.Objects;

/**
 * 用户关注关系实体类
 * 
 * 对应数据库表：user_follows
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 2024-01-01
 */
@Entity
@Table(name = "user_follows", indexes = {
    @Index(name = "uk_follower_following", columnList = "follower_id,following_id", unique = true),
    @Index(name = "idx_follower_id", columnList = "follower_id"),
    @Index(name = "idx_following_id", columnList = "following_id"),
    @Index(name = "idx_created_at", columnList = "created_at")
})
@TableName("user_follows")
public class UserFollow {

    /**
     * 主键ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 关注者ID
     */
    @Column(name = "follower_id", nullable = false)
    private Long followerId;

    /**
     * 被关注者ID
     */
    @Column(name = "following_id", nullable = false)
    private Long followingId;

    /**
     * 关注时间
     */
    @Column(name = "created_at", nullable = false, updatable = false)
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    // 构造函数
    public UserFollow() {}

    public UserFollow(Long followerId, Long followingId) {
        this.followerId = followerId;
        this.followingId = followingId;
    }

    // Getter和Setter方法
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getFollowerId() {
        return followerId;
    }

    public void setFollowerId(Long followerId) {
        this.followerId = followerId;
    }

    public Long getFollowingId() {
        return followingId;
    }

    public void setFollowingId(Long followingId) {
        this.followingId = followingId;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        UserFollow that = (UserFollow) o;
        return Objects.equals(id, that.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }

    @Override
    public String toString() {
        return "UserFollow{" +
                "id=" + id +
                ", followerId=" + followerId +
                ", followingId=" + followingId +
                ", createdAt=" + createdAt +
                '}';
    }
}
