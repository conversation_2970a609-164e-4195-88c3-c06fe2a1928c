name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

env:
  JAVA_VERSION: '17'
  NODE_VERSION: '18'

jobs:
  # 代码质量检查
  code-quality:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
    
    - name: Setup Java
      uses: actions/setup-java@v4
      with:
        java-version: ${{ env.JAVA_VERSION }}
        distribution: 'temurin'
    
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        cache-dependency-path: 'ideary-frontend/package-lock.json'
    
    - name: Cache Maven dependencies
      uses: actions/cache@v3
      with:
        path: ~/.m2
        key: ${{ runner.os }}-m2-${{ hashFiles('**/pom.xml') }}
        restore-keys: ${{ runner.os }}-m2
    
    - name: Backend Code Quality Check
      run: |
        cd ideary-backend
        mvn clean compile checkstyle:check spotbugs:check
    
    - name: Frontend Code Quality Check
      run: |
        cd ideary-frontend
        npm ci
        npm run lint
        npm run type-check

  # 后端测试
  backend-test:
    runs-on: ubuntu-latest
    services:
      mysql:
        image: mysql:8.0
        env:
          MYSQL_ROOT_PASSWORD: root123456
          MYSQL_DATABASE: ideary_test
        ports:
          - 3306:3306
        options: --health-cmd="mysqladmin ping" --health-interval=10s --health-timeout=5s --health-retries=3
      
      redis:
        image: redis:7.0-alpine
        ports:
          - 6379:6379
        options: --health-cmd="redis-cli ping" --health-interval=10s --health-timeout=5s --health-retries=3

    steps:
    - uses: actions/checkout@v4
    
    - name: Setup Java
      uses: actions/setup-java@v4
      with:
        java-version: ${{ env.JAVA_VERSION }}
        distribution: 'temurin'
    
    - name: Cache Maven dependencies
      uses: actions/cache@v3
      with:
        path: ~/.m2
        key: ${{ runner.os }}-m2-${{ hashFiles('**/pom.xml') }}
        restore-keys: ${{ runner.os }}-m2
    
    - name: Run Backend Tests
      run: |
        cd ideary-backend
        mvn clean test
      env:
        SPRING_PROFILES_ACTIVE: test
        MYSQL_HOST: localhost
        REDIS_HOST: localhost
    
    - name: Generate Test Report
      uses: dorny/test-reporter@v1
      if: success() || failure()
      with:
        name: Backend Test Results
        path: 'ideary-backend/**/target/surefire-reports/*.xml'
        reporter: java-junit
    
    - name: Upload Coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./ideary-backend/target/site/jacoco/jacoco.xml
        flags: backend

  # 前端测试
  frontend-test:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
    
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        cache-dependency-path: 'ideary-frontend/package-lock.json'
    
    - name: Install Dependencies
      run: |
        cd ideary-frontend
        npm ci
    
    - name: Run Frontend Tests
      run: |
        cd ideary-frontend
        npm run test:coverage
    
    - name: Upload Coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./ideary-frontend/coverage/lcov.info
        flags: frontend

  # 构建测试
  build-test:
    runs-on: ubuntu-latest
    needs: [code-quality, backend-test, frontend-test]
    steps:
    - uses: actions/checkout@v4
    
    - name: Setup Java
      uses: actions/setup-java@v4
      with:
        java-version: ${{ env.JAVA_VERSION }}
        distribution: 'temurin'
    
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        cache-dependency-path: 'ideary-frontend/package-lock.json'
    
    - name: Cache Maven dependencies
      uses: actions/cache@v3
      with:
        path: ~/.m2
        key: ${{ runner.os }}-m2-${{ hashFiles('**/pom.xml') }}
        restore-keys: ${{ runner.os }}-m2
    
    - name: Build Backend
      run: |
        cd ideary-backend
        mvn clean package -DskipTests
    
    - name: Build Frontend
      run: |
        cd ideary-frontend
        npm ci
        npm run build
    
    - name: Build Docker Images
      run: |
        docker build -t ideary-gateway:latest ./ideary-backend/ideary-gateway
        docker build -t ideary-user:latest ./ideary-backend/ideary-user
        docker build -t ideary-frontend:latest ./ideary-frontend

  # 安全扫描
  security-scan:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
    
    - name: Run Trivy vulnerability scanner
      uses: aquasecurity/trivy-action@master
      with:
        scan-type: 'fs'
        scan-ref: '.'
        format: 'sarif'
        output: 'trivy-results.sarif'
    
    - name: Upload Trivy scan results to GitHub Security tab
      uses: github/codeql-action/upload-sarif@v2
      with:
        sarif_file: 'trivy-results.sarif'

  # 部署到开发环境
  deploy-dev:
    runs-on: ubuntu-latest
    needs: [build-test]
    if: github.ref == 'refs/heads/develop'
    environment: development
    steps:
    - uses: actions/checkout@v4
    
    - name: Deploy to Development
      run: |
        echo "Deploying to development environment..."
        # 这里添加实际的部署脚本

  # 部署到生产环境
  deploy-prod:
    runs-on: ubuntu-latest
    needs: [build-test, security-scan]
    if: github.ref == 'refs/heads/main'
    environment: production
    steps:
    - uses: actions/checkout@v4
    
    - name: Deploy to Production
      run: |
        echo "Deploying to production environment..."
        # 这里添加实际的部署脚本
