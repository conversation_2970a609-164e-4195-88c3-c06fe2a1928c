# 测试环境配置文件
server:
  port: 0  # 随机端口

spring:
  application:
    name: ideary-user-test
  
  # 数据源配置（使用H2内存数据库）
  datasource:
    driver-class-name: org.h2.Driver
    url: jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
    username: sa
    password: 
    hikari:
      minimum-idle: 1
      maximum-pool-size: 5
  
  # JPA配置
  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.H2Dialect
        format_sql: true
  
  # Redis配置（测试环境使用内存数据库）
  data:
    redis:
      host: localhost
      port: 6379
      database: 15  # 使用测试数据库
  
  # 邮件配置（测试环境禁用）
  mail:
    host: localhost
    port: 25
    username: test
    password: test
  
  # 配置中心（测试环境禁用）
  cloud:
    nacos:
      discovery:
        enabled: false
      config:
        enabled: false

# MyBatis Plus配置
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      id-type: AUTO
      logic-delete-field: deleted_at
      logic-delete-value: 'NOW()'
      logic-not-delete-value: 'NULL'

# 管理端点配置
management:
  endpoints:
    web:
      exposure:
        include: health,info
  endpoint:
    health:
      show-details: always

# JWT配置
jwt:
  secret: test-jwt-secret-key
  expiration: 3600000  # 1小时
  refresh-expiration: 7200000  # 2小时
  header: Authorization
  prefix: "Bearer "

# 用户配置
user:
  password:
    min-length: 6
    max-length: 20
  verification:
    email:
      expire-minutes: 10
      max-send-times: 5

# 日志配置
logging:
  level:
    com.ideary.user: DEBUG
    com.baomidou.mybatisplus: INFO
    org.springframework.security: INFO
  pattern:
    console: "%d{HH:mm:ss.SSS} [%thread] %-5level [%logger{36}] - %msg%n"
