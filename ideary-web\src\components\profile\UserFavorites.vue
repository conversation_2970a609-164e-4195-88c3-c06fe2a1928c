<template>
  <div class="user-favorites">
    <el-skeleton v-if="loading" :rows="3" animated />
    
    <div v-else-if="favorites.length > 0" class="favorites-list">
      <div
        v-for="favorite in favorites"
        :key="favorite.id"
        class="favorite-item"
      >
        <h3 class="favorite-title">
          <router-link :to="`/articles/${favorite.articleId}`">
            {{ favorite.title }}
          </router-link>
        </h3>
        <p class="favorite-summary">{{ favorite.summary }}</p>
        <div class="favorite-meta">
          <span class="author-name">作者：{{ favorite.author }}</span>
          <span class="favorite-date">收藏于 {{ formatDate(favorite.createdAt) }}</span>
        </div>
      </div>
    </div>
    
    <div v-else class="empty-state">
      <el-empty description="暂无收藏" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import dayjs from 'dayjs'

interface Props {
  userId?: number
}

const props = defineProps<Props>()

const loading = ref(true)
const favorites = ref<any[]>([])

const formatDate = (date: string) => {
  return dayjs(date).format('YYYY年MM月DD日')
}

const fetchUserFavorites = async () => {
  try {
    loading.value = true
    await new Promise(resolve => setTimeout(resolve, 500))
    
    // 模拟数据
    favorites.value = [
      {
        id: 1,
        articleId: 1,
        title: '收藏的技术文章',
        summary: '这是一篇我收藏的优秀技术文章...',
        author: 'React 大师',
        createdAt: '2024-01-18T10:30:00Z',
      }
    ]
  } catch (error) {
    console.error('获取用户收藏失败:', error)
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  fetchUserFavorites()
})
</script>

<style lang="scss" scoped>
.user-favorites {
  .favorites-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
  }
  
  .favorite-item {
    padding: 1rem;
    border: 1px solid var(--el-border-color-light);
    border-radius: 0.5rem;
    
    .favorite-title {
      font-size: 1.125rem;
      font-weight: 600;
      margin-bottom: 0.5rem;
      
      a {
        color: var(--el-text-color-primary);
        text-decoration: none;
        
        &:hover {
          color: var(--el-color-primary);
        }
      }
    }
    
    .favorite-summary {
      color: var(--el-text-color-regular);
      margin-bottom: 0.75rem;
    }
    
    .favorite-meta {
      display: flex;
      justify-content: space-between;
      font-size: 0.875rem;
      color: var(--el-text-color-placeholder);
    }
  }
  
  .empty-state {
    text-align: center;
    padding: 2rem 0;
  }
}
</style>
