<template>
  <div class="user-following">
    <el-skeleton v-if="loading" :rows="3" animated />
    
    <div v-else-if="following.length > 0" class="following-list">
      <div
        v-for="user in following"
        :key="user.id"
        class="following-item"
      >
        <div class="user-info">
          <el-avatar :src="user.avatar" :size="40">
            {{ user.nickname?.[0] || user.username?.[0] }}
          </el-avatar>
          <div class="user-details">
            <h4 class="user-name">
              <router-link :to="`/authors/${user.username}`">
                {{ user.nickname || user.username }}
              </router-link>
            </h4>
            <p class="user-bio">{{ user.bio }}</p>
          </div>
        </div>
        <el-button size="small" @click="unfollowUser(user.id)">
          取消关注
        </el-button>
      </div>
    </div>
    
    <div v-else class="empty-state">
      <el-empty description="暂无关注的用户" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'

interface Props {
  userId?: number
}

const props = defineProps<Props>()

const loading = ref(true)
const following = ref<any[]>([])

const unfollowUser = async (userId: number) => {
  try {
    // 模拟取消关注API
    await new Promise(resolve => setTimeout(resolve, 500))
    
    following.value = following.value.filter(user => user.id !== userId)
    ElMessage.success('已取消关注')
  } catch (error) {
    ElMessage.error('操作失败，请重试')
  }
}

const fetchUserFollowing = async () => {
  try {
    loading.value = true
    await new Promise(resolve => setTimeout(resolve, 500))
    
    // 模拟数据
    following.value = [
      {
        id: 1,
        username: 'react_master',
        nickname: 'React 大师',
        avatar: 'https://avatars.githubusercontent.com/u/1?v=4',
        bio: '专注于 React 生态系统开发',
      }
    ]
  } catch (error) {
    console.error('获取关注列表失败:', error)
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  fetchUserFollowing()
})
</script>

<style lang="scss" scoped>
.user-following {
  .following-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
  }
  
  .following-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    border: 1px solid var(--el-border-color-light);
    border-radius: 0.5rem;
    
    .user-info {
      display: flex;
      align-items: center;
      gap: 1rem;
      flex: 1;
      
      .user-details {
        .user-name {
          font-size: 1rem;
          font-weight: 600;
          margin-bottom: 0.25rem;
          
          a {
            color: var(--el-text-color-primary);
            text-decoration: none;
            
            &:hover {
              color: var(--el-color-primary);
            }
          }
        }
        
        .user-bio {
          font-size: 0.875rem;
          color: var(--el-text-color-regular);
          margin: 0;
        }
      }
    }
  }
  
  .empty-state {
    text-align: center;
    padding: 2rem 0;
  }
}
</style>
