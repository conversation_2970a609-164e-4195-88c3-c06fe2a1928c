<template>
  <div class="about-page">
    <div class="page-container">
      <!-- 英雄区域 -->
      <section class="hero-section">
        <div class="hero-content">
          <h1 class="hero-title">关于 Ideary</h1>
          <p class="hero-subtitle">技术灵感分享社区</p>
          <p class="hero-description">
            我们致力于打造一个开放、友好的技术分享平台，让每一位开发者都能在这里分享知识、交流经验、获得灵感。
          </p>
        </div>
      </section>

      <!-- 使命愿景 -->
      <section class="mission-section">
        <div class="section-header">
          <h2 class="section-title">我们的使命</h2>
        </div>
        <div class="mission-grid">
          <div class="mission-item">
            <div class="mission-icon">
              <el-icon><Share /></el-icon>
            </div>
            <h3>知识分享</h3>
            <p>促进技术知识的自由流动，让优质内容触达更多开发者</p>
          </div>
          <div class="mission-item">
            <div class="mission-icon">
              <el-icon><Connection /></el-icon>
            </div>
            <h3>社区连接</h3>
            <p>连接全球开发者，构建活跃的技术交流社区</p>
          </div>
          <div class="mission-item">
            <div class="mission-icon">
              <el-icon><TrendCharts /></el-icon>
            </div>
            <h3>技术成长</h3>
            <p>帮助开发者持续学习，跟上技术发展的步伐</p>
          </div>
        </div>
      </section>

      <!-- 团队介绍 -->
      <section class="team-section">
        <div class="section-header">
          <h2 class="section-title">核心团队</h2>
          <p class="section-subtitle">来自不同背景的技术专家</p>
        </div>
        <div class="team-grid">
          <div class="team-member">
            <el-avatar :size="120" src="https://avatars.githubusercontent.com/u/1?v=4">
              张
            </el-avatar>
            <h3>张三</h3>
            <p class="role">创始人 & CEO</p>
            <p class="bio">前阿里巴巴技术专家，专注于大规模系统架构设计</p>
          </div>
          <div class="team-member">
            <el-avatar :size="120" src="https://avatars.githubusercontent.com/u/2?v=4">
              李
            </el-avatar>
            <h3>李四</h3>
            <p class="role">技术总监</p>
            <p class="bio">全栈工程师，开源项目贡献者，热爱技术分享</p>
          </div>
          <div class="team-member">
            <el-avatar :size="120" src="https://avatars.githubusercontent.com/u/3?v=4">
              王
            </el-avatar>
            <h3>王五</h3>
            <p class="role">产品总监</p>
            <p class="bio">用户体验专家，致力于打造最佳的开发者体验</p>
          </div>
        </div>
      </section>

      <!-- 数据统计 -->
      <section class="stats-section">
        <div class="section-header">
          <h2 class="section-title">平台数据</h2>
        </div>
        <div class="stats-grid">
          <div class="stat-item">
            <div class="stat-number">10,000+</div>
            <div class="stat-label">技术文章</div>
          </div>
          <div class="stat-item">
            <div class="stat-number">5,000+</div>
            <div class="stat-label">活跃用户</div>
          </div>
          <div class="stat-item">
            <div class="stat-number">500+</div>
            <div class="stat-label">技术话题</div>
          </div>
          <div class="stat-item">
            <div class="stat-number">50+</div>
            <div class="stat-label">技术领域</div>
          </div>
        </div>
      </section>

      <!-- 联系我们 -->
      <section class="contact-section">
        <div class="section-header">
          <h2 class="section-title">联系我们</h2>
          <p class="section-subtitle">有任何问题或建议，欢迎与我们联系</p>
        </div>
        <div class="contact-grid">
          <div class="contact-item">
            <el-icon><Message /></el-icon>
            <h3>邮箱</h3>
            <p><EMAIL></p>
          </div>
          <div class="contact-item">
            <el-icon><ChatDotRound /></el-icon>
            <h3>社区</h3>
            <p>加入我们的 Discord 社区</p>
          </div>
          <div class="contact-item">
            <el-icon><Location /></el-icon>
            <h3>地址</h3>
            <p>中国 · 深圳</p>
          </div>
        </div>
      </section>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted } from 'vue'
import { 
  Share, 
  Connection, 
  TrendCharts, 
  Message, 
  ChatDotRound, 
  Location 
} from '@element-plus/icons-vue'

onMounted(() => {
  document.title = '关于我们 - Ideary'
})
</script>

<style lang="scss" scoped>
.about-page {
  min-height: 100vh;
}

.page-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem 1rem;
}

.hero-section {
  text-align: center;
  padding: 4rem 0;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(16, 185, 129, 0.1));
  border-radius: 1rem;
  margin-bottom: 4rem;
  
  .hero-title {
    font-size: 3rem;
    font-weight: bold;
    color: var(--el-text-color-primary);
    margin-bottom: 1rem;
  }
  
  .hero-subtitle {
    font-size: 1.5rem;
    color: var(--el-color-primary);
    margin-bottom: 1.5rem;
  }
  
  .hero-description {
    font-size: 1.125rem;
    color: var(--el-text-color-regular);
    line-height: 1.6;
    max-width: 600px;
    margin: 0 auto;
  }
}

.section-header {
  text-align: center;
  margin-bottom: 3rem;
  
  .section-title {
    font-size: 2.5rem;
    font-weight: bold;
    color: var(--el-text-color-primary);
    margin-bottom: 1rem;
  }
  
  .section-subtitle {
    font-size: 1.125rem;
    color: var(--el-text-color-regular);
  }
}

.mission-section {
  margin-bottom: 4rem;
  
  .mission-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    
    .mission-item {
      text-align: center;
      padding: 2rem;
      background: var(--el-bg-color);
      border-radius: 1rem;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      
      .mission-icon {
        width: 4rem;
        height: 4rem;
        background: var(--el-color-primary);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 1rem;
        color: white;
        font-size: 1.5rem;
      }
      
      h3 {
        font-size: 1.25rem;
        font-weight: 600;
        margin-bottom: 1rem;
        color: var(--el-text-color-primary);
      }
      
      p {
        color: var(--el-text-color-regular);
        line-height: 1.6;
      }
    }
  }
}

.team-section {
  margin-bottom: 4rem;
  
  .team-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    
    .team-member {
      text-align: center;
      padding: 2rem;
      background: var(--el-bg-color);
      border-radius: 1rem;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      
      h3 {
        font-size: 1.25rem;
        font-weight: 600;
        margin: 1rem 0 0.5rem;
        color: var(--el-text-color-primary);
      }
      
      .role {
        color: var(--el-color-primary);
        font-weight: 500;
        margin-bottom: 1rem;
      }
      
      .bio {
        color: var(--el-text-color-regular);
        line-height: 1.5;
        font-size: 0.875rem;
      }
    }
  }
}

.stats-section {
  margin-bottom: 4rem;
  
  .stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
    
    .stat-item {
      text-align: center;
      padding: 2rem;
      background: var(--el-bg-color);
      border-radius: 1rem;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      
      .stat-number {
        font-size: 2.5rem;
        font-weight: bold;
        color: var(--el-color-primary);
        margin-bottom: 0.5rem;
      }
      
      .stat-label {
        color: var(--el-text-color-regular);
        font-size: 1rem;
      }
    }
  }
}

.contact-section {
  .contact-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    
    .contact-item {
      text-align: center;
      padding: 2rem;
      background: var(--el-bg-color);
      border-radius: 1rem;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      
      .el-icon {
        font-size: 2rem;
        color: var(--el-color-primary);
        margin-bottom: 1rem;
      }
      
      h3 {
        font-size: 1.25rem;
        font-weight: 600;
        margin-bottom: 0.5rem;
        color: var(--el-text-color-primary);
      }
      
      p {
        color: var(--el-text-color-regular);
      }
    }
  }
}
</style>
