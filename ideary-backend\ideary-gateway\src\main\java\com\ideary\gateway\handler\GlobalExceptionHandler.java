package com.ideary.gateway.handler;

import com.alibaba.fastjson2.JSON;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.web.reactive.error.ErrorWebExceptionHandler;
import org.springframework.cloud.gateway.support.NotFoundException;
import org.springframework.core.annotation.Order;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ResponseStatusException;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * 全局异常处理器
 * 
 * 功能说明：
 * 1. 统一异常处理
 * 2. 统一错误响应格式
 * 3. 异常日志记录
 * 4. 不同类型异常的特殊处理
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 2024-01-01
 */
@Component
@Order(-1)
public class GlobalExceptionHandler implements ErrorWebExceptionHandler {

    private static final Logger logger = LoggerFactory.getLogger(GlobalExceptionHandler.class);

    @Override
    public Mono<Void> handle(ServerWebExchange exchange, Throwable ex) {
        ServerHttpResponse response = exchange.getResponse();
        
        if (response.isCommitted()) {
            return Mono.error(ex);
        }

        // 设置响应头
        response.getHeaders().add("Content-Type", MediaType.APPLICATION_JSON_VALUE);

        HttpStatus httpStatus;
        String message;
        int code;

        // 根据异常类型设置不同的响应状态和消息
        if (ex instanceof NotFoundException) {
            httpStatus = HttpStatus.NOT_FOUND;
            code = 404;
            message = "请求的服务不存在";
            logger.warn("服务未找到: {}", ex.getMessage());
        } else if (ex instanceof ResponseStatusException) {
            ResponseStatusException responseStatusException = (ResponseStatusException) ex;
            httpStatus = HttpStatus.resolve(responseStatusException.getStatusCode().value());
            code = responseStatusException.getStatusCode().value();
            message = responseStatusException.getReason();
            logger.warn("响应状态异常: {}", ex.getMessage());
        } else if (ex.getMessage() != null && ex.getMessage().contains("Connection refused")) {
            httpStatus = HttpStatus.SERVICE_UNAVAILABLE;
            code = 503;
            message = "服务暂时不可用，请稍后重试";
            logger.error("服务连接被拒绝: {}", ex.getMessage());
        } else if (ex.getMessage() != null && ex.getMessage().contains("timeout")) {
            httpStatus = HttpStatus.GATEWAY_TIMEOUT;
            code = 504;
            message = "服务响应超时";
            logger.error("服务响应超时: {}", ex.getMessage());
        } else if (ex instanceof IllegalArgumentException) {
            httpStatus = HttpStatus.BAD_REQUEST;
            code = 400;
            message = "请求参数错误: " + ex.getMessage();
            logger.warn("请求参数错误: {}", ex.getMessage());
        } else {
            httpStatus = HttpStatus.INTERNAL_SERVER_ERROR;
            code = 500;
            message = "系统内部错误";
            logger.error("系统内部错误: ", ex);
        }

        // 设置响应状态码
        response.setStatusCode(httpStatus);

        // 构建错误响应体
        Map<String, Object> result = new HashMap<>();
        result.put("code", code);
        result.put("message", message);
        result.put("data", null);
        result.put("timestamp", new Date());
        result.put("path", exchange.getRequest().getURI().getPath());

        // 在开发环境下，可以添加更详细的错误信息
        if (isDevEnvironment()) {
            result.put("exception", ex.getClass().getSimpleName());
            result.put("detail", ex.getMessage());
        }

        String body = JSON.toJSONString(result);
        DataBuffer buffer = response.bufferFactory().wrap(body.getBytes(StandardCharsets.UTF_8));

        return response.writeWith(Mono.just(buffer));
    }

    /**
     * 判断是否为开发环境
     * 在实际项目中，可以通过配置文件或环境变量来判断
     */
    private boolean isDevEnvironment() {
        // 这里可以通过读取配置文件或环境变量来判断
        // 为了简化，这里直接返回true
        return true;
    }
}
