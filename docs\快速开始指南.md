# Ideary 快速开始指南

本指南将帮助您快速搭建和运行 Ideary 技术灵感分享社区。

## 🚀 一键启动

### 环境要求
- **Java**: JDK 17+
- **Node.js**: 18+
- **Docker**: 20.10+
- **Git**: 2.30+

### 快速启动
```bash
# 克隆项目
git clone https://github.com/ideary-team/ideary-blog.git
cd ideary-blog

# 一键启动所有服务
chmod +x start-ideary.sh
./start-ideary.sh
```

等待几分钟后，您就可以访问：
- **前端应用**: http://localhost:3000
- **API文档**: http://localhost:9080/doc.html
- **Nacos控制台**: http://localhost:8848/nacos (nacos/nacos)

## 📋 详细步骤

### 1. 环境检查
```bash
# 检查环境是否满足要求
./start-ideary.sh --check
```

### 2. 分步启动

#### 启动基础设施
```bash
# 启动数据库、缓存等基础服务
./start-ideary.sh --infrastructure
```

#### 启动后端服务
```bash
# 启动微服务
./start-ideary.sh --backend
```

#### 启动前端应用
```bash
# 启动前端应用
./start-ideary.sh --frontend
```

### 3. 验证服务状态
```bash
# 查看所有服务状态
./start-ideary.sh --status
```

## 🔧 开发模式

### 后端开发
```bash
# 进入网关服务目录
cd ideary-backend/ideary-gateway
./start-gateway.sh

# 进入用户服务目录
cd ../ideary-user
./start-user.sh
```

### 前端开发
```bash
# 进入前端目录
cd ideary-frontend
npm install
npm run dev
```

## 🐳 Docker 部署

### 使用 Docker Compose
```bash
# 构建并启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f

# 停止服务
docker-compose down
```

### 单独构建镜像
```bash
# 构建网关服务镜像
cd ideary-backend/ideary-gateway
docker build -t ideary-gateway:latest .

# 构建用户服务镜像
cd ../ideary-user
docker build -t ideary-user:latest .

# 构建前端应用镜像
cd ../../ideary-frontend
docker build -t ideary-frontend:latest .
```

## 📊 监控和管理

### 访问监控面板
- **Prometheus**: http://localhost:9090
- **Grafana**: http://localhost:3001 (admin/admin123456)
- **Kibana**: http://localhost:5601
- **Jaeger**: http://localhost:16686

### 查看应用日志
```bash
# 查看网关日志
tail -f logs/gateway.log

# 查看用户服务日志
tail -f logs/user.log

# 查看前端日志
tail -f logs/frontend.log
```

## 🔍 故障排除

### 常见问题

#### 端口冲突
```bash
# 检查端口占用
netstat -tuln | grep :3000
netstat -tuln | grep :9080

# 停止占用端口的进程
sudo lsof -ti:3000 | xargs kill -9
```

#### 服务启动失败
```bash
# 查看详细错误日志
docker-compose logs service-name

# 重启特定服务
docker-compose restart service-name
```

#### 数据库连接问题
```bash
# 检查MySQL服务状态
docker-compose exec mysql mysql -u ideary -p -e "SELECT 1"

# 重置数据库
docker-compose down -v
docker-compose up -d mysql
```

### 清理和重置
```bash
# 停止所有服务
./start-ideary.sh --stop

# 清理Docker资源
docker system prune -a

# 重新启动
./start-ideary.sh
```

## 🧪 测试

### 运行测试
```bash
# 后端测试
cd ideary-backend
mvn test

# 前端测试
cd ideary-frontend
npm test
```

### API 测试
访问 http://localhost:9080/doc.html 使用 Swagger UI 测试 API。

## 📝 配置说明

### 环境变量
主要配置文件：
- `ideary-frontend/.env.local` - 前端环境变量
- `ideary-backend/*/src/main/resources/application-*.yml` - 后端配置

### 数据库配置
默认数据库配置：
- **主机**: localhost:3306
- **数据库**: ideary
- **用户名**: ideary
- **密码**: ideary123456

### Redis 配置
默认 Redis 配置：
- **主机**: localhost:6379
- **密码**: 无

## 🎯 下一步

1. **用户注册**: 访问 http://localhost:3000/auth/register
2. **创建文章**: 登录后点击"写文章"
3. **浏览内容**: 探索首页的精选文章和热门话题
4. **管理后台**: 访问 http://localhost:9080/admin

## 📞 获取帮助

如果遇到问题，请：
1. 查看 [故障排除文档](./故障排除指南.md)
2. 在 GitHub 上提交 Issue
3. 加入我们的 Discord 社区
4. 发送邮件至 <EMAIL>

---

🎉 恭喜！您已成功启动 Ideary 系统。开始您的技术分享之旅吧！
