package com.ideary.user.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ideary.user.entity.UserFollow;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 用户关注关系Mapper接口
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 2024-01-01
 */
@Mapper
public interface UserFollowMapper extends BaseMapper<UserFollow> {

    /**
     * 检查是否已关注
     *
     * @param followerId 关注者ID
     * @param followingId 被关注者ID
     * @return 是否已关注
     */
    @Select("SELECT COUNT(*) > 0 FROM user_follows WHERE follower_id = #{followerId} AND following_id = #{followingId}")
    boolean isFollowing(@Param("followerId") Long followerId, @Param("followingId") Long followingId);

    /**
     * 取消关注
     *
     * @param followerId 关注者ID
     * @param followingId 被关注者ID
     * @return 删除行数
     */
    @Delete("DELETE FROM user_follows WHERE follower_id = #{followerId} AND following_id = #{followingId}")
    int unfollow(@Param("followerId") Long followerId, @Param("followingId") Long followingId);

    /**
     * 获取用户关注列表
     *
     * @param followerId 关注者ID
     * @param limit 限制数量
     * @param offset 偏移量
     * @return 关注列表
     */
    @Select("SELECT uf.*, u.username, u.nickname, u.avatar FROM user_follows uf " +
            "LEFT JOIN users u ON uf.following_id = u.id " +
            "WHERE uf.follower_id = #{followerId} AND u.deleted_at IS NULL " +
            "ORDER BY uf.created_at DESC LIMIT #{limit} OFFSET #{offset}")
    List<UserFollow> findFollowingList(@Param("followerId") Long followerId, 
                                       @Param("limit") Integer limit, 
                                       @Param("offset") Integer offset);

    /**
     * 获取用户粉丝列表
     *
     * @param followingId 被关注者ID
     * @param limit 限制数量
     * @param offset 偏移量
     * @return 粉丝列表
     */
    @Select("SELECT uf.*, u.username, u.nickname, u.avatar FROM user_follows uf " +
            "LEFT JOIN users u ON uf.follower_id = u.id " +
            "WHERE uf.following_id = #{followingId} AND u.deleted_at IS NULL " +
            "ORDER BY uf.created_at DESC LIMIT #{limit} OFFSET #{offset}")
    List<UserFollow> findFollowersList(@Param("followingId") Long followingId, 
                                       @Param("limit") Integer limit, 
                                       @Param("offset") Integer offset);

    /**
     * 统计用户关注数
     *
     * @param followerId 关注者ID
     * @return 关注数
     */
    @Select("SELECT COUNT(*) FROM user_follows uf " +
            "LEFT JOIN users u ON uf.following_id = u.id " +
            "WHERE uf.follower_id = #{followerId} AND u.deleted_at IS NULL")
    long countFollowing(@Param("followerId") Long followerId);

    /**
     * 统计用户粉丝数
     *
     * @param followingId 被关注者ID
     * @return 粉丝数
     */
    @Select("SELECT COUNT(*) FROM user_follows uf " +
            "LEFT JOIN users u ON uf.follower_id = u.id " +
            "WHERE uf.following_id = #{followingId} AND u.deleted_at IS NULL")
    long countFollowers(@Param("followingId") Long followingId);

    /**
     * 获取互相关注的用户列表
     *
     * @param userId 用户ID
     * @param limit 限制数量
     * @param offset 偏移量
     * @return 互相关注列表
     */
    @Select("SELECT uf1.*, u.username, u.nickname, u.avatar FROM user_follows uf1 " +
            "INNER JOIN user_follows uf2 ON uf1.following_id = uf2.follower_id AND uf1.follower_id = uf2.following_id " +
            "LEFT JOIN users u ON uf1.following_id = u.id " +
            "WHERE uf1.follower_id = #{userId} AND u.deleted_at IS NULL " +
            "ORDER BY uf1.created_at DESC LIMIT #{limit} OFFSET #{offset}")
    List<UserFollow> findMutualFollows(@Param("userId") Long userId, 
                                       @Param("limit") Integer limit, 
                                       @Param("offset") Integer offset);

    /**
     * 统计互相关注数
     *
     * @param userId 用户ID
     * @return 互相关注数
     */
    @Select("SELECT COUNT(*) FROM user_follows uf1 " +
            "INNER JOIN user_follows uf2 ON uf1.following_id = uf2.follower_id AND uf1.follower_id = uf2.following_id " +
            "LEFT JOIN users u ON uf1.following_id = u.id " +
            "WHERE uf1.follower_id = #{userId} AND u.deleted_at IS NULL")
    long countMutualFollows(@Param("userId") Long userId);

    /**
     * 批量检查关注状态
     *
     * @param followerId 关注者ID
     * @param followingIds 被关注者ID列表
     * @return 关注关系列表
     */
    @Select("<script>" +
            "SELECT * FROM user_follows WHERE follower_id = #{followerId} AND following_id IN " +
            "<foreach collection='followingIds' item='followingId' open='(' separator=',' close=')'>" +
            "#{followingId}" +
            "</foreach>" +
            "</script>")
    List<UserFollow> findFollowStatus(@Param("followerId") Long followerId, 
                                      @Param("followingIds") List<Long> followingIds);

    /**
     * 删除用户的所有关注关系（用户注销时）
     *
     * @param userId 用户ID
     * @return 删除行数
     */
    @Delete("DELETE FROM user_follows WHERE follower_id = #{userId} OR following_id = #{userId}")
    int deleteAllByUserId(@Param("userId") Long userId);
}
