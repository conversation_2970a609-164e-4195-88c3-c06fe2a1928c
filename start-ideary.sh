#!/bin/bash

# Ideary博客系统一键启动脚本

set -e

echo "=========================================="
echo "  🚀 Ideary 技术灵感分享社区"
echo "  一键启动脚本"
echo "=========================================="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查命令是否存在
check_command() {
    if ! command -v $1 > /dev/null 2>&1; then
        log_error "$1 未安装，请先安装 $1"
        return 1
    fi
    return 0
}

# 检查端口是否被占用
check_port() {
    if netstat -tuln | grep -q ":$1 "; then
        log_warning "端口 $1 已被占用"
        return 1
    fi
    return 0
}

# 等待服务启动
wait_for_service() {
    local url=$1
    local service_name=$2
    local max_attempts=30
    local attempt=1

    log_info "等待 $service_name 启动..."
    
    while [ $attempt -le $max_attempts ]; do
        if curl -s "$url" > /dev/null 2>&1; then
            log_success "$service_name 启动成功"
            return 0
        fi
        
        echo -n "."
        sleep 2
        attempt=$((attempt + 1))
    done
    
    log_error "$service_name 启动超时"
    return 1
}

# 显示帮助信息
show_help() {
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  --help, -h          显示帮助信息"
    echo "  --check             仅检查环境，不启动服务"
    echo "  --infrastructure    仅启动基础设施"
    echo "  --backend           仅启动后端服务"
    echo "  --frontend          仅启动前端应用"
    echo "  --stop              停止所有服务"
    echo "  --restart           重启所有服务"
    echo "  --status            查看服务状态"
    echo ""
    echo "示例:"
    echo "  $0                  启动完整系统"
    echo "  $0 --check          检查环境"
    echo "  $0 --stop           停止所有服务"
}

# 环境检查
check_environment() {
    log_info "检查运行环境..."
    
    # 检查必要的命令
    local required_commands=("java" "node" "npm" "docker" "docker-compose" "curl" "netstat")
    local missing_commands=()
    
    for cmd in "${required_commands[@]}"; do
        if ! check_command "$cmd"; then
            missing_commands+=("$cmd")
        fi
    done
    
    if [ ${#missing_commands[@]} -ne 0 ]; then
        log_error "缺少必要的命令: ${missing_commands[*]}"
        log_error "请安装缺少的软件后重试"
        exit 1
    fi
    
    # 检查Java版本
    JAVA_VERSION=$(java -version 2>&1 | awk -F '"' '/version/ {print $2}' | cut -d'.' -f1)
    if [ "$JAVA_VERSION" -lt 17 ]; then
        log_error "Java版本过低，需要JDK 17+，当前版本: $(java -version 2>&1 | head -n1)"
        exit 1
    fi
    
    # 检查Node.js版本
    NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
    if [ "$NODE_VERSION" -lt 18 ]; then
        log_error "Node.js版本过低，需要Node.js 18+，当前版本: $(node -v)"
        exit 1
    fi
    
    # 检查Docker是否运行
    if ! docker info > /dev/null 2>&1; then
        log_error "Docker未运行，请启动Docker后重试"
        exit 1
    fi
    
    log_success "环境检查通过"
    log_info "Java版本: $(java -version 2>&1 | head -n1)"
    log_info "Node.js版本: $(node -v)"
    log_info "Docker版本: $(docker --version)"
}

# 启动基础设施
start_infrastructure() {
    log_info "启动基础设施服务..."
    
    if [ ! -f "infrastructure/start-infrastructure.sh" ]; then
        log_error "基础设施启动脚本不存在"
        return 1
    fi
    
    cd infrastructure
    chmod +x start-infrastructure.sh
    ./start-infrastructure.sh
    cd ..
    
    # 等待关键服务启动
    wait_for_service "http://localhost:8848/nacos" "Nacos"
    wait_for_service "http://localhost:3306" "MySQL" || true  # MySQL可能不支持HTTP检查
    
    log_success "基础设施启动完成"
}

# 启动后端服务
start_backend() {
    log_info "启动后端微服务..."
    
    # 启动网关服务
    if [ -f "ideary-backend/ideary-gateway/start-gateway.sh" ]; then
        log_info "启动API网关..."
        cd ideary-backend/ideary-gateway
        chmod +x start-gateway.sh
        nohup ./start-gateway.sh > ../../logs/gateway.log 2>&1 &
        cd ../..
        wait_for_service "http://localhost:9080/actuator/health" "API网关"
    fi
    
    # 启动用户服务
    if [ -f "ideary-backend/ideary-user/start-user.sh" ]; then
        log_info "启动用户服务..."
        cd ideary-backend/ideary-user
        chmod +x start-user.sh
        nohup ./start-user.sh > ../../logs/user.log 2>&1 &
        cd ../..
        wait_for_service "http://localhost:9081/actuator/health" "用户服务"
    fi
    
    log_success "后端服务启动完成"
}

# 启动前端应用
start_frontend() {
    log_info "启动前端应用..."
    
    if [ ! -f "ideary-frontend/start-frontend.sh" ]; then
        log_error "前端启动脚本不存在"
        return 1
    fi
    
    cd ideary-frontend
    chmod +x start-frontend.sh
    nohup ./start-frontend.sh > ../logs/frontend.log 2>&1 &
    cd ..
    
    wait_for_service "http://localhost:3000" "前端应用"
    
    log_success "前端应用启动完成"
}

# 停止所有服务
stop_services() {
    log_info "停止所有服务..."
    
    # 停止前端
    pkill -f "next" || true
    
    # 停止后端服务
    pkill -f "ideary-gateway" || true
    pkill -f "ideary-user" || true
    
    # 停止基础设施
    if [ -f "infrastructure/docker-compose.yml" ]; then
        cd infrastructure
        docker-compose down
        cd ..
    fi
    
    log_success "所有服务已停止"
}

# 查看服务状态
show_status() {
    log_info "服务状态检查..."
    
    echo ""
    echo "🔍 端口占用情况:"
    echo "  3000  - 前端应用:    $(check_port 3000 && echo "未启动" || echo "运行中")"
    echo "  9080  - API网关:     $(check_port 9080 && echo "未启动" || echo "运行中")"
    echo "  9081  - 用户服务:    $(check_port 9081 && echo "未启动" || echo "运行中")"
    echo "  8848  - Nacos:       $(check_port 8848 && echo "未启动" || echo "运行中")"
    echo "  3306  - MySQL:       $(check_port 3306 && echo "未启动" || echo "运行中")"
    echo "  6379  - Redis:       $(check_port 6379 && echo "未启动" || echo "运行中")"
    
    echo ""
    echo "🐳 Docker容器状态:"
    docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" | grep -E "(ideary|mysql|redis|nacos)" || echo "  无相关容器运行"
    
    echo ""
    echo "📊 系统资源使用:"
    echo "  CPU使用率: $(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | cut -d'%' -f1)%"
    echo "  内存使用: $(free -h | awk '/^Mem:/ {print $3 "/" $2}')"
    echo "  磁盘使用: $(df -h . | awk 'NR==2 {print $3 "/" $2 " (" $5 ")"}')"
}

# 创建日志目录
mkdir -p logs

# 解析命令行参数
case "${1:-}" in
    --help|-h)
        show_help
        exit 0
        ;;
    --check)
        check_environment
        exit 0
        ;;
    --infrastructure)
        check_environment
        start_infrastructure
        exit 0
        ;;
    --backend)
        check_environment
        start_backend
        exit 0
        ;;
    --frontend)
        check_environment
        start_frontend
        exit 0
        ;;
    --stop)
        stop_services
        exit 0
        ;;
    --restart)
        stop_services
        sleep 3
        check_environment
        start_infrastructure
        start_backend
        start_frontend
        ;;
    --status)
        show_status
        exit 0
        ;;
    "")
        # 默认启动完整系统
        check_environment
        start_infrastructure
        start_backend
        start_frontend
        ;;
    *)
        log_error "未知选项: $1"
        show_help
        exit 1
        ;;
esac

# 显示启动完成信息
echo ""
echo "🎉 Ideary 系统启动完成！"
echo ""
echo "📋 访问地址:"
echo "  🌐 前端应用:     http://localhost:3000"
echo "  🔌 API网关:      http://localhost:9080"
echo "  📚 API文档:      http://localhost:9080/doc.html"
echo "  ⚙️  Nacos控制台: http://localhost:8848/nacos (nacos/nacos)"
echo ""
echo "📝 日志文件:"
echo "  前端日志: logs/frontend.log"
echo "  网关日志: logs/gateway.log"
echo "  用户服务: logs/user.log"
echo ""
echo "🔧 管理命令:"
echo "  查看状态: $0 --status"
echo "  停止服务: $0 --stop"
echo "  重启服务: $0 --restart"
echo ""
echo "💡 提示: 按 Ctrl+C 可以停止此脚本，但服务会继续运行"
