<template>
  <div class="article-detail-page">
    <div class="page-container">
      <el-skeleton v-if="loading" :rows="10" animated />
      
      <article v-else-if="article" class="article-content">
        <!-- 文章头部 -->
        <header class="article-header">
          <div class="article-tags">
            <el-tag
              v-for="tag in article.tags"
              :key="tag"
              type="primary"
              effect="plain"
            >
              {{ tag }}
            </el-tag>
          </div>
          
          <h1 class="article-title">{{ article.title }}</h1>
          
          <div class="article-meta">
            <div class="author-info">
              <el-avatar :src="article.author.avatar" :size="40">
                {{ article.author.nickname?.[0] || article.author.username?.[0] }}
              </el-avatar>
              <div class="author-details">
                <div class="author-name">{{ article.author.nickname || article.author.username }}</div>
                <div class="publish-info">
                  {{ formatDate(article.publishedAt) }} · {{ article.readingTime }} 分钟阅读
                </div>
              </div>
            </div>
            
            <div class="article-actions">
              <el-button @click="toggleLike" :type="article.isLiked ? 'primary' : 'default'">
                <el-icon><Star /></el-icon>
                {{ article.likeCount }}
              </el-button>
              <el-button @click="toggleFavorite" :type="article.isFavorited ? 'warning' : 'default'">
                <el-icon><Collection /></el-icon>
                收藏
              </el-button>
              <el-button @click="shareArticle">
                <el-icon><Share /></el-icon>
                分享
              </el-button>
            </div>
          </div>
        </header>
        
        <!-- 文章内容 -->
        <div class="article-body">
          <div v-if="article.coverImage" class="article-cover">
            <img :src="article.coverImage" :alt="article.title" />
          </div>
          
          <div class="article-text" v-html="article.content"></div>
        </div>
        
        <!-- 文章统计 -->
        <div class="article-stats">
          <span><el-icon><View /></el-icon> {{ formatNumber(article.viewCount) }} 阅读</span>
          <span><el-icon><Star /></el-icon> {{ formatNumber(article.likeCount) }} 点赞</span>
          <span><el-icon><ChatDotRound /></el-icon> {{ formatNumber(article.commentCount) }} 评论</span>
        </div>
      </article>
      
      <div v-else class="not-found">
        <el-empty description="文章不存在或已被删除" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { Star, Collection, Share, View, ChatDotRound } from '@element-plus/icons-vue'
import dayjs from 'dayjs'

const route = useRoute()

const loading = ref(true)
const article = ref<any>(null)

const formatDate = (date: string) => {
  return dayjs(date).format('YYYY年MM月DD日')
}

const formatNumber = (num: number) => {
  if (num < 1000) return num.toString()
  if (num < 10000) return `${(num / 1000).toFixed(1)}k`
  return `${Math.floor(num / 1000)}k`
}

const toggleLike = async () => {
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 300))
    
    article.value.isLiked = !article.value.isLiked
    article.value.likeCount += article.value.isLiked ? 1 : -1
    
    ElMessage.success(article.value.isLiked ? '点赞成功' : '取消点赞')
  } catch (error) {
    ElMessage.error('操作失败，请重试')
  }
}

const toggleFavorite = async () => {
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 300))
    
    article.value.isFavorited = !article.value.isFavorited
    
    ElMessage.success(article.value.isFavorited ? '收藏成功' : '取消收藏')
  } catch (error) {
    ElMessage.error('操作失败，请重试')
  }
}

const shareArticle = () => {
  if (navigator.share) {
    navigator.share({
      title: article.value.title,
      url: window.location.href
    })
  } else {
    navigator.clipboard.writeText(window.location.href)
    ElMessage.success('链接已复制到剪贴板')
  }
}

const fetchArticle = async () => {
  try {
    loading.value = true
    const articleId = route.params.id
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 800))
    
    // 模拟数据
    article.value = {
      id: articleId,
      title: 'React 18 新特性深度解析：并发渲染与自动批处理',
      content: `
        <p>React 18 是 React 的一个重要版本，引入了许多令人兴奋的新特性...</p>
        <h2>并发渲染</h2>
        <p>并发渲染是 React 18 最重要的特性之一...</p>
        <h2>自动批处理</h2>
        <p>自动批处理可以提高应用性能...</p>
      `,
      coverImage: 'https://images.unsplash.com/photo-1633356122544-f134324a6cee?w=800&h=400&fit=crop',
      author: {
        id: 1,
        username: 'react_master',
        nickname: 'React 大师',
        avatar: 'https://avatars.githubusercontent.com/u/1?v=4'
      },
      tags: ['React', 'JavaScript', '前端开发'],
      viewCount: 12500,
      likeCount: 856,
      commentCount: 124,
      readingTime: 8,
      publishedAt: '2024-01-15T10:30:00Z',
      isLiked: false,
      isFavorited: false
    }
  } catch (error) {
    console.error('获取文章失败:', error)
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  fetchArticle()
  document.title = `${article.value?.title || '文章详情'} - Ideary`
})
</script>

<style lang="scss" scoped>
.article-detail-page {
  min-height: 100vh;
  background: var(--el-bg-color-page);
}

.page-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 2rem 1rem;
}

.article-content {
  background: var(--el-bg-color);
  border-radius: 1rem;
  padding: 2rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.article-header {
  margin-bottom: 2rem;
  
  .article-tags {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 1rem;
    flex-wrap: wrap;
  }
  
  .article-title {
    font-size: 2rem;
    font-weight: bold;
    color: var(--el-text-color-primary);
    margin-bottom: 1.5rem;
    line-height: 1.4;
  }
  
  .article-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    .author-info {
      display: flex;
      align-items: center;
      gap: 1rem;
      
      .author-details {
        .author-name {
          font-weight: 500;
          color: var(--el-text-color-primary);
        }
        
        .publish-info {
          font-size: 0.875rem;
          color: var(--el-text-color-placeholder);
        }
      }
    }
    
    .article-actions {
      display: flex;
      gap: 0.5rem;
    }
  }
}

.article-body {
  margin-bottom: 2rem;
  
  .article-cover {
    margin-bottom: 2rem;
    border-radius: 0.5rem;
    overflow: hidden;
    
    img {
      width: 100%;
      height: auto;
      display: block;
    }
  }
  
  .article-text {
    line-height: 1.8;
    color: var(--el-text-color-primary);
    
    :deep(h1), :deep(h2), :deep(h3) {
      margin: 2rem 0 1rem;
      color: var(--el-text-color-primary);
    }
    
    :deep(p) {
      margin-bottom: 1rem;
    }
    
    :deep(code) {
      background: var(--el-fill-color-light);
      padding: 0.25rem 0.5rem;
      border-radius: 0.25rem;
      font-family: 'Courier New', monospace;
    }
  }
}

.article-stats {
  display: flex;
  gap: 2rem;
  padding-top: 1rem;
  border-top: 1px solid var(--el-border-color-light);
  color: var(--el-text-color-placeholder);
  
  span {
    display: flex;
    align-items: center;
    gap: 0.25rem;
  }
}

.not-found {
  text-align: center;
  padding: 3rem 0;
}
</style>
