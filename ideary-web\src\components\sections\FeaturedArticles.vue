<template>
  <div class="featured-articles">
    <el-skeleton v-if="loading" :rows="3" animated />
    
    <div v-else-if="articles.length > 0" class="articles-grid">
      <!-- 主要文章 -->
      <article v-if="articles[0]" class="main-article">
        <div class="article-card">
          <div v-if="articles[0].coverImage" class="article-cover">
            <img :src="articles[0].coverImage" :alt="articles[0].title" />
            <div class="cover-overlay">
              <div class="article-tags">
                <el-tag
                  v-for="tag in articles[0].tags.slice(0, 2)"
                  :key="tag"
                  type="primary"
                  effect="light"
                  size="small"
                >
                  {{ tag }}
                </el-tag>
              </div>
            </div>
          </div>
          
          <div class="article-content">
            <h3 class="article-title">
              <router-link :to="`/articles/${articles[0].id}`">
                {{ articles[0].title }}
              </router-link>
            </h3>
            
            <p class="article-summary">{{ articles[0].summary }}</p>
            
            <div class="article-meta">
              <div class="author-info">
                <el-avatar
                  :src="articles[0].author.avatar"
                  :size="32"
                  class="author-avatar"
                >
                  {{ articles[0].author.nickname?.[0] || articles[0].author.username?.[0] }}
                </el-avatar>
                <div class="author-details">
                  <div class="author-name">{{ articles[0].author.nickname || articles[0].author.username }}</div>
                  <div class="article-date">{{ formatDate(articles[0].publishedAt) }}</div>
                </div>
              </div>
              
              <div class="article-stats">
                <span class="stat-item">
                  <el-icon><View /></el-icon>
                  {{ formatNumber(articles[0].viewCount) }}
                </span>
                <span class="stat-item">
                  <el-icon><Star /></el-icon>
                  {{ formatNumber(articles[0].likeCount) }}
                </span>
                <span class="stat-item">
                  <el-icon><ChatDotRound /></el-icon>
                  {{ formatNumber(articles[0].commentCount) }}
                </span>
              </div>
            </div>
          </div>
        </div>
      </article>
      
      <!-- 侧边文章列表 -->
      <div class="side-articles">
        <article
          v-for="article in articles.slice(1, 4)"
          :key="article.id"
          class="side-article"
        >
          <div class="article-card">
            <div class="article-tags">
              <el-tag
                v-for="tag in article.tags.slice(0, 2)"
                :key="tag"
                type="primary"
                effect="plain"
                size="small"
              >
                {{ tag }}
              </el-tag>
            </div>
            
            <h4 class="article-title">
              <router-link :to="`/articles/${article.id}`">
                {{ article.title }}
              </router-link>
            </h4>
            
            <p class="article-summary">{{ article.summary }}</p>
            
            <div class="article-meta">
              <div class="author-info">
                <el-avatar
                  :src="article.author.avatar"
                  :size="24"
                  class="author-avatar"
                >
                  {{ article.author.nickname?.[0] || article.author.username?.[0] }}
                </el-avatar>
                <span class="author-name">{{ article.author.nickname || article.author.username }}</span>
              </div>
              
              <div class="article-stats">
                <span class="stat-item">
                  <el-icon><View /></el-icon>
                  {{ formatNumber(article.viewCount) }}
                </span>
                <span class="stat-item">
                  <el-icon><Star /></el-icon>
                  {{ formatNumber(article.likeCount) }}
                </span>
              </div>
            </div>
          </div>
        </article>
      </div>
    </div>
    
    <div v-else class="empty-state">
      <el-empty description="暂无精选文章" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { View, Star, ChatDotRound } from '@element-plus/icons-vue'
import dayjs from 'dayjs'
import 'dayjs/locale/zh-cn'

dayjs.locale('zh-cn')

interface Article {
  id: number
  title: string
  summary: string
  coverImage?: string
  author: {
    id: number
    username: string
    nickname?: string
    avatar?: string
  }
  tags: string[]
  viewCount: number
  likeCount: number
  commentCount: number
  publishedAt: string
  readingTime: number
}

const loading = ref(true)
const articles = ref<Article[]>([])

// 格式化日期
const formatDate = (date: string) => {
  return dayjs(date).format('YYYY年MM月DD日')
}

// 格式化数字
const formatNumber = (num: number) => {
  if (num < 1000) return num.toString()
  if (num < 10000) return `${(num / 1000).toFixed(1)}k`
  return `${Math.floor(num / 1000)}k`
}

// 获取精选文章
const fetchFeaturedArticles = async () => {
  try {
    loading.value = true
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 模拟数据
    articles.value = [
      {
        id: 1,
        title: 'React 18 新特性深度解析：并发渲染与自动批处理',
        summary: '深入探讨 React 18 带来的革命性变化，包括并发渲染、自动批处理、Suspense 改进等核心特性，以及它们如何提升应用性能。',
        coverImage: 'https://images.unsplash.com/photo-1633356122544-f134324a6cee?w=800&h=400&fit=crop',
        author: {
          id: 1,
          username: 'react_master',
          nickname: 'React 大师',
          avatar: 'https://avatars.githubusercontent.com/u/1?v=4',
        },
        tags: ['React', 'JavaScript', '前端开发'],
        viewCount: 12500,
        likeCount: 856,
        commentCount: 124,
        publishedAt: '2024-01-15T10:30:00Z',
        readingTime: 8,
      },
      {
        id: 2,
        title: 'TypeScript 5.0 新特性全面解读',
        summary: 'TypeScript 5.0 正式发布，带来了装饰器、const 类型参数、枚举改进等重要特性。',
        author: {
          id: 2,
          username: 'ts_expert',
          nickname: 'TypeScript 专家',
          avatar: 'https://avatars.githubusercontent.com/u/2?v=4',
        },
        tags: ['TypeScript', 'JavaScript'],
        viewCount: 9800,
        likeCount: 642,
        commentCount: 89,
        publishedAt: '2024-01-14T14:20:00Z',
        readingTime: 12,
      },
      {
        id: 3,
        title: '微前端架构实战：从零到一构建可扩展的前端系统',
        summary: '通过实际项目案例，详细介绍微前端架构的设计思路、技术选型、实现方案。',
        author: {
          id: 3,
          username: 'frontend_architect',
          nickname: '前端架构师',
          avatar: 'https://avatars.githubusercontent.com/u/3?v=4',
        },
        tags: ['微前端', '架构设计'],
        viewCount: 15600,
        likeCount: 1024,
        commentCount: 156,
        publishedAt: '2024-01-13T09:15:00Z',
        readingTime: 15,
      },
      {
        id: 4,
        title: 'Vue 3 Composition API 最佳实践指南',
        summary: '深入理解 Vue 3 Composition API 的设计理念和使用技巧。',
        author: {
          id: 4,
          username: 'vue_expert',
          nickname: 'Vue 专家',
          avatar: 'https://avatars.githubusercontent.com/u/4?v=4',
        },
        tags: ['Vue.js', 'Composition API'],
        viewCount: 8900,
        likeCount: 567,
        commentCount: 78,
        publishedAt: '2024-01-12T16:45:00Z',
        readingTime: 10,
      },
    ]
  } catch (error) {
    console.error('获取精选文章失败:', error)
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  fetchFeaturedArticles()
})
</script>

<style lang="scss" scoped>
.featured-articles {
  .articles-grid {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 2rem;
    
    @media (max-width: 1024px) {
      grid-template-columns: 1fr;
    }
  }
  
  .main-article {
    .article-card {
      background: var(--el-bg-color);
      border-radius: 1rem;
      overflow: hidden;
      box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
      transition: all 0.3s ease;
      
      &:hover {
        box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
        transform: translateY(-2px);
      }
    }
    
    .article-cover {
      position: relative;
      height: 300px;
      overflow: hidden;
      
      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.3s ease;
      }
      
      &:hover img {
        transform: scale(1.05);
      }
      
      .cover-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(to bottom, transparent, rgba(0, 0, 0, 0.3));
        display: flex;
        align-items: flex-start;
        justify-content: flex-start;
        padding: 1rem;
      }
      
      .article-tags {
        display: flex;
        gap: 0.5rem;
        flex-wrap: wrap;
      }
    }
    
    .article-content {
      padding: 1.5rem;
    }
    
    .article-title {
      font-size: 1.5rem;
      font-weight: bold;
      margin-bottom: 1rem;
      line-height: 1.4;
      
      a {
        color: var(--el-text-color-primary);
        text-decoration: none;
        
        &:hover {
          color: var(--el-color-primary);
        }
      }
    }
    
    .article-summary {
      color: var(--el-text-color-regular);
      line-height: 1.6;
      margin-bottom: 1.5rem;
      display: -webkit-box;
      -webkit-line-clamp: 3;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }
  }
  
  .side-articles {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    
    .side-article {
      .article-card {
        background: var(--el-bg-color);
        border-radius: 0.75rem;
        padding: 1.5rem;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
        
        &:hover {
          box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
          transform: translateY(-1px);
        }
      }
      
      .article-tags {
        display: flex;
        gap: 0.5rem;
        margin-bottom: 0.75rem;
        flex-wrap: wrap;
      }
      
      .article-title {
        font-size: 1.125rem;
        font-weight: 600;
        margin-bottom: 0.75rem;
        line-height: 1.4;
        
        a {
          color: var(--el-text-color-primary);
          text-decoration: none;
          
          &:hover {
            color: var(--el-color-primary);
          }
        }
      }
      
      .article-summary {
        color: var(--el-text-color-regular);
        font-size: 0.875rem;
        line-height: 1.5;
        margin-bottom: 1rem;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
      }
    }
  }
  
  .article-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    .author-info {
      display: flex;
      align-items: center;
      gap: 0.75rem;
      
      .author-details {
        .author-name {
          font-weight: 500;
          color: var(--el-text-color-primary);
          font-size: 0.875rem;
        }
        
        .article-date {
          font-size: 0.75rem;
          color: var(--el-text-color-placeholder);
        }
      }
    }
    
    .article-stats {
      display: flex;
      gap: 1rem;
      
      .stat-item {
        display: flex;
        align-items: center;
        gap: 0.25rem;
        color: var(--el-text-color-placeholder);
        font-size: 0.875rem;
        
        .el-icon {
          font-size: 1rem;
        }
      }
    }
  }
  
  .side-articles .article-meta {
    .author-info {
      gap: 0.5rem;
      
      .author-name {
        font-size: 0.75rem;
      }
    }
    
    .article-stats {
      gap: 0.75rem;
      
      .stat-item {
        font-size: 0.75rem;
        
        .el-icon {
          font-size: 0.875rem;
        }
      }
    }
  }
  
  .empty-state {
    text-align: center;
    padding: 3rem 0;
  }
}
</style>
