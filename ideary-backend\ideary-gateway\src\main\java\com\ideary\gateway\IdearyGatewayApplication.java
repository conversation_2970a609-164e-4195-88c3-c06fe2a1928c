package com.ideary.gateway;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;

/**
 * Ideary网关服务启动类
 * 
 * 功能说明：
 * 1. 统一请求入口和路由分发
 * 2. 用户身份认证与授权
 * 3. 接口限流和监控
 * 4. 跨域处理
 * 5. 统一异常处理和响应格式
 * 6. 健康检查端点
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 2024-01-01
 */
@SpringBootApplication
@EnableDiscoveryClient
public class IdearyGatewayApplication {

    public static void main(String[] args) {
        SpringApplication.run(IdearyGatewayApplication.class, args);
        System.out.println("""
            
            ========================================
                Ideary Gateway Service Started
            ========================================
            
            🚀 网关服务启动成功！
            
            📋 服务信息：
               服务名称: ideary-gateway
               服务端口: 9080
               服务版本: 1.0.0
            
            🔗 访问地址：
               网关地址: http://localhost:9080
               健康检查: http://localhost:9080/actuator/health
               服务信息: http://localhost:9080/actuator/info
            
            📚 主要功能：
               ✅ 统一请求入口和路由分发
               ✅ 用户身份认证与授权
               ✅ 接口限流和监控
               ✅ 跨域处理
               ✅ 统一异常处理
               ✅ 健康检查端点
            
            ========================================
            """);
    }
}
