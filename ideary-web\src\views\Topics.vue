<template>
  <div class="topics-page">
    <div class="page-container">
      <div class="page-header">
        <h1 class="page-title">技术话题</h1>
        <p class="page-subtitle">探索热门技术话题和趋势</p>
      </div>

      <div class="topics-grid">
        <div
          v-for="topic in topics"
          :key="topic.id"
          class="topic-card"
          @click="$router.push(`/topics/${topic.name.toLowerCase()}`)"
        >
          <div class="topic-icon" :style="{ backgroundColor: topic.color }">
            <span>{{ topic.name[0] }}</span>
          </div>
          <h3 class="topic-name">{{ topic.name }}</h3>
          <p class="topic-description">{{ topic.description }}</p>
          <div class="topic-stats">
            <span>{{ topic.articlesCount }} 篇文章</span>
            <span>{{ topic.followersCount }} 关注者</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'

const topics = ref([
  {
    id: 1,
    name: 'React',
    description: '用于构建用户界面的 JavaScript 库',
    articlesCount: 1250,
    followersCount: 8900,
    color: '#61dafb'
  },
  {
    id: 2,
    name: 'Vue.js',
    description: '渐进式 JavaScript 框架',
    articlesCount: 756,
    followersCount: 5600,
    color: '#4fc08d'
  },
  {
    id: 3,
    name: 'TypeScript',
    description: 'JavaScript 的超集，添加了静态类型',
    articlesCount: 980,
    followersCount: 7200,
    color: '#3178c6'
  }
])

onMounted(() => {
  document.title = '技术话题 - Ideary'
})
</script>

<style lang="scss" scoped>
.topics-page {
  min-height: 100vh;
  background: var(--el-bg-color-page);
}

.page-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem 1rem;
}

.page-header {
  text-align: center;
  margin-bottom: 3rem;
  
  .page-title {
    font-size: 2.5rem;
    font-weight: bold;
    color: var(--el-text-color-primary);
    margin-bottom: 1rem;
  }
  
  .page-subtitle {
    font-size: 1.125rem;
    color: var(--el-text-color-regular);
  }
}

.topics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.topic-card {
  background: var(--el-bg-color);
  border-radius: 1rem;
  padding: 2rem;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  
  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  }
  
  .topic-icon {
    width: 4rem;
    height: 4rem;
    border-radius: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    color: white;
    font-size: 1.5rem;
    font-weight: bold;
  }
  
  .topic-name {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--el-text-color-primary);
  }
  
  .topic-description {
    color: var(--el-text-color-regular);
    margin-bottom: 1rem;
    line-height: 1.5;
  }
  
  .topic-stats {
    display: flex;
    justify-content: center;
    gap: 1rem;
    font-size: 0.875rem;
    color: var(--el-text-color-placeholder);
  }
}
</style>
