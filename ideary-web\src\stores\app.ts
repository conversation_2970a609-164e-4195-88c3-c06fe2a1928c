import { defineStore } from 'pinia'
import { ref } from 'vue'

export const useAppStore = defineStore('app', () => {
  // 状态
  const loading = ref(false)
  const sidebarCollapsed = ref(false)
  const theme = ref<'light' | 'dark'>('light')
  const language = ref('zh-CN')
  const deviceType = ref<'desktop' | 'tablet' | 'mobile'>('desktop')

  // 设置加载状态
  const setLoading = (value: boolean) => {
    loading.value = value
  }

  // 切换侧边栏
  const toggleSidebar = () => {
    sidebarCollapsed.value = !sidebarCollapsed.value
  }

  // 设置侧边栏状态
  const setSidebarCollapsed = (value: boolean) => {
    sidebarCollapsed.value = value
  }

  // 切换主题
  const toggleTheme = () => {
    theme.value = theme.value === 'light' ? 'dark' : 'light'
    localStorage.setItem('ideary-theme', theme.value)
  }

  // 设置主题
  const setTheme = (value: 'light' | 'dark') => {
    theme.value = value
    localStorage.setItem('ideary-theme', value)
  }

  // 设置语言
  const setLanguage = (value: string) => {
    language.value = value
    localStorage.setItem('ideary-language', value)
  }

  // 设置设备类型
  const setDeviceType = (value: 'desktop' | 'tablet' | 'mobile') => {
    deviceType.value = value
  }

  // 初始化应用设置
  const initApp = () => {
    // 恢复主题设置
    const savedTheme = localStorage.getItem('ideary-theme') as 'light' | 'dark'
    if (savedTheme) {
      theme.value = savedTheme
    }

    // 恢复语言设置
    const savedLanguage = localStorage.getItem('ideary-language')
    if (savedLanguage) {
      language.value = savedLanguage
    }

    // 检测设备类型
    const checkDeviceType = () => {
      const width = window.innerWidth
      if (width < 768) {
        deviceType.value = 'mobile'
      } else if (width < 1024) {
        deviceType.value = 'tablet'
      } else {
        deviceType.value = 'desktop'
      }
    }

    checkDeviceType()
    window.addEventListener('resize', checkDeviceType)
  }

  return {
    // 状态
    loading: readonly(loading),
    sidebarCollapsed: readonly(sidebarCollapsed),
    theme: readonly(theme),
    language: readonly(language),
    deviceType: readonly(deviceType),

    // 方法
    setLoading,
    toggleSidebar,
    setSidebarCollapsed,
    toggleTheme,
    setTheme,
    setLanguage,
    setDeviceType,
    initApp
  }
})
