<template>
  <div class="settings-page">
    <div class="page-container">
      <div class="settings-header">
        <h1>账户设置</h1>
        <p>管理您的个人信息和偏好设置</p>
      </div>

      <el-tabs v-model="activeTab" class="settings-tabs">
        <!-- 个人信息 -->
        <el-tab-pane label="个人信息" name="profile">
          <el-form ref="profileFormRef" :model="profileForm" :rules="profileRules" label-width="100px">
            <el-form-item label="用户名" prop="username">
              <el-input v-model="profileForm.username" disabled />
              <div class="form-tip">用户名不可修改</div>
            </el-form-item>

            <el-form-item label="昵称" prop="nickname">
              <el-input v-model="profileForm.nickname" placeholder="请输入昵称" />
            </el-form-item>

            <el-form-item label="邮箱" prop="email">
              <el-input v-model="profileForm.email" placeholder="请输入邮箱" />
            </el-form-item>

            <el-form-item label="个人简介" prop="bio">
              <el-input
                v-model="profileForm.bio"
                type="textarea"
                :rows="4"
                placeholder="介绍一下自己..."
                maxlength="200"
                show-word-limit
              />
            </el-form-item>

            <el-form-item>
              <el-button type="primary" @click="saveProfile" :loading="savingProfile">
                保存更改
              </el-button>
            </el-form-item>
          </el-form>
        </el-tab-pane>

        <!-- 密码修改 -->
        <el-tab-pane label="密码修改" name="password">
          <el-form ref="passwordFormRef" :model="passwordForm" :rules="passwordRules" label-width="100px">
            <el-form-item label="当前密码" prop="currentPassword">
              <el-input
                v-model="passwordForm.currentPassword"
                type="password"
                placeholder="请输入当前密码"
                show-password
              />
            </el-form-item>

            <el-form-item label="新密码" prop="newPassword">
              <el-input
                v-model="passwordForm.newPassword"
                type="password"
                placeholder="请输入新密码"
                show-password
              />
            </el-form-item>

            <el-form-item label="确认密码" prop="confirmPassword">
              <el-input
                v-model="passwordForm.confirmPassword"
                type="password"
                placeholder="请再次输入新密码"
                show-password
              />
            </el-form-item>

            <el-form-item>
              <el-button type="primary" @click="changePassword" :loading="changingPassword">
                修改密码
              </el-button>
            </el-form-item>
          </el-form>
        </el-tab-pane>

        <!-- 偏好设置 -->
        <el-tab-pane label="偏好设置" name="preferences">
          <el-form label-width="120px">
            <el-form-item label="主题模式">
              <el-radio-group v-model="preferences.theme" @change="changeTheme">
                <el-radio label="light">浅色模式</el-radio>
                <el-radio label="dark">深色模式</el-radio>
                <el-radio label="auto">跟随系统</el-radio>
              </el-radio-group>
            </el-form-item>

            <el-form-item label="语言设置">
              <el-select v-model="preferences.language" @change="changeLanguage">
                <el-option label="简体中文" value="zh-CN" />
                <el-option label="English" value="en-US" />
              </el-select>
            </el-form-item>

            <el-form-item label="邮件通知">
              <el-switch
                v-model="preferences.emailNotifications"
                @change="savePreferences"
                active-text="开启"
                inactive-text="关闭"
              />
            </el-form-item>

            <el-form-item label="推送通知">
              <el-switch
                v-model="preferences.pushNotifications"
                @change="savePreferences"
                active-text="开启"
                inactive-text="关闭"
              />
            </el-form-item>
          </el-form>
        </el-tab-pane>

        <!-- 隐私设置 -->
        <el-tab-pane label="隐私设置" name="privacy">
          <el-form label-width="120px">
            <el-form-item label="个人资料">
              <el-radio-group v-model="privacy.profileVisibility" @change="savePrivacy">
                <el-radio label="public">公开</el-radio>
                <el-radio label="followers">仅关注者可见</el-radio>
                <el-radio label="private">私密</el-radio>
              </el-radio-group>
            </el-form-item>

            <el-form-item label="文章列表">
              <el-radio-group v-model="privacy.articlesVisibility" @change="savePrivacy">
                <el-radio label="public">公开</el-radio>
                <el-radio label="followers">仅关注者可见</el-radio>
                <el-radio label="private">私密</el-radio>
              </el-radio-group>
            </el-form-item>

            <el-form-item label="关注列表">
              <el-switch
                v-model="privacy.showFollowing"
                @change="savePrivacy"
                active-text="公开"
                inactive-text="隐藏"
              />
            </el-form-item>

            <el-form-item label="粉丝列表">
              <el-switch
                v-model="privacy.showFollowers"
                @change="savePrivacy"
                active-text="公开"
                inactive-text="隐藏"
              />
            </el-form-item>
          </el-form>
        </el-tab-pane>

        <!-- 账户管理 -->
        <el-tab-pane label="账户管理" name="account">
          <div class="account-section">
            <h3>危险操作</h3>
            <p class="section-description">以下操作不可逆，请谨慎操作</p>

            <div class="danger-actions">
              <el-button type="danger" plain @click="exportData">
                <el-icon><Download /></el-icon>
                导出数据
              </el-button>

              <el-button type="danger" @click="deleteAccount">
                <el-icon><Delete /></el-icon>
                删除账户
              </el-button>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox, type FormInstance, type FormRules } from 'element-plus'
import { Download, Delete } from '@element-plus/icons-vue'
import { useUserStore } from '@/stores/user'

const userStore = useUserStore()

// 响应式数据
const activeTab = ref('profile')
const savingProfile = ref(false)
const changingPassword = ref(false)

// 表单引用
const profileFormRef = ref<FormInstance>()
const passwordFormRef = ref<FormInstance>()

// 个人信息表单
const profileForm = reactive({
  username: '',
  nickname: '',
  email: '',
  bio: ''
})

// 密码修改表单
const passwordForm = reactive({
  currentPassword: '',
  newPassword: '',
  confirmPassword: ''
})

// 偏好设置
const preferences = reactive({
  theme: 'light',
  language: 'zh-CN',
  emailNotifications: true,
  pushNotifications: false
})

// 隐私设置
const privacy = reactive({
  profileVisibility: 'public',
  articlesVisibility: 'public',
  showFollowing: true,
  showFollowers: true
})

// 表单验证规则
const profileRules: FormRules = {
  nickname: [
    { required: true, message: '请输入昵称', trigger: 'blur' },
    { min: 2, max: 20, message: '昵称长度在 2 到 20 个字符', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱地址', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ]
}

const passwordRules: FormRules = {
  currentPassword: [
    { required: true, message: '请输入当前密码', trigger: 'blur' }
  ],
  newPassword: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能少于6位', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请确认新密码', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (value !== passwordForm.newPassword) {
          callback(new Error('两次输入密码不一致'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
}

// 保存个人信息
const saveProfile = async () => {
  if (!profileFormRef.value) return

  try {
    await profileFormRef.value.validate()
    savingProfile.value = true

    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))

    userStore.updateUser(profileForm)
    ElMessage.success('个人信息保存成功')
  } catch (error) {
    ElMessage.error('保存失败，请重试')
  } finally {
    savingProfile.value = false
  }
}

// 修改密码
const changePassword = async () => {
  if (!passwordFormRef.value) return

  try {
    await passwordFormRef.value.validate()
    changingPassword.value = true

    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))

    ElMessage.success('密码修改成功')
    
    // 清空表单
    passwordForm.currentPassword = ''
    passwordForm.newPassword = ''
    passwordForm.confirmPassword = ''
  } catch (error) {
    ElMessage.error('密码修改失败，请重试')
  } finally {
    changingPassword.value = false
  }
}

// 更改主题
const changeTheme = (theme: string) => {
  // 这里可以实现主题切换逻辑
  ElMessage.success(`已切换到${theme === 'light' ? '浅色' : theme === 'dark' ? '深色' : '自动'}模式`)
}

// 更改语言
const changeLanguage = (language: string) => {
  ElMessage.success(`语言已切换到${language === 'zh-CN' ? '简体中文' : 'English'}`)
}

// 保存偏好设置
const savePreferences = () => {
  ElMessage.success('偏好设置已保存')
}

// 保存隐私设置
const savePrivacy = () => {
  ElMessage.success('隐私设置已保存')
}

// 导出数据
const exportData = () => {
  ElMessageBox.confirm(
    '确定要导出您的所有数据吗？这将包括您的文章、评论和个人信息。',
    '导出数据',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    ElMessage.success('数据导出请求已提交，我们将在24小时内发送到您的邮箱')
  }).catch(() => {
    // 用户取消
  })
}

// 删除账户
const deleteAccount = () => {
  ElMessageBox.confirm(
    '删除账户将永久删除您的所有数据，包括文章、评论和个人信息。此操作不可逆！',
    '删除账户',
    {
      confirmButtonText: '确定删除',
      cancelButtonText: '取消',
      type: 'error'
    }
  ).then(() => {
    ElMessageBox.prompt('请输入您的密码以确认删除', '确认删除', {
      confirmButtonText: '删除账户',
      cancelButtonText: '取消',
      inputType: 'password'
    }).then(() => {
      ElMessage.success('账户删除请求已提交')
    })
  }).catch(() => {
    // 用户取消
  })
}

// 初始化
onMounted(() => {
  // 加载用户信息
  const userInfo = userStore.userInfo
  if (userInfo) {
    profileForm.username = userInfo.username
    profileForm.nickname = userInfo.nickname || ''
    profileForm.email = userInfo.email || ''
    profileForm.bio = userInfo.bio || ''
  }

  document.title = '账户设置 - Ideary'
})
</script>

<style lang="scss" scoped>
.settings-page {
  min-height: 100vh;
  background: var(--el-bg-color-page);
}

.page-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 2rem 1rem;
}

.settings-header {
  text-align: center;
  margin-bottom: 2rem;
  
  h1 {
    font-size: 2rem;
    font-weight: bold;
    color: var(--el-text-color-primary);
    margin-bottom: 0.5rem;
  }
  
  p {
    color: var(--el-text-color-regular);
  }
}

.settings-tabs {
  background: var(--el-bg-color);
  border-radius: 1rem;
  padding: 2rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  
  :deep(.el-tabs__content) {
    padding-top: 1rem;
  }
  
  .form-tip {
    font-size: 0.875rem;
    color: var(--el-text-color-placeholder);
    margin-top: 0.25rem;
  }
  
  .account-section {
    h3 {
      color: var(--el-color-danger);
      margin-bottom: 0.5rem;
    }
    
    .section-description {
      color: var(--el-text-color-regular);
      margin-bottom: 1.5rem;
    }
    
    .danger-actions {
      display: flex;
      gap: 1rem;
      flex-wrap: wrap;
    }
  }
}
</style>
