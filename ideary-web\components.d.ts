/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

declare module 'vue' {
  export interface GlobalComponents {
    AppFooter: typeof import('./src/components/layout/AppFooter.vue')['default']
    AppHeader: typeof import('./src/components/layout/AppHeader.vue')['default']
    ElConfigProvider: typeof import('element-plus/es')['ElConfigProvider']
    ElLoadingService: typeof import('element-plus/es')['ElLoadingService']
    FeaturedArticles: typeof import('./src/components/sections/FeaturedArticles.vue')['default']
    NewsletterSection: typeof import('./src/components/sections/NewsletterSection.vue')['default']
    PopularAuthors: <AUTHORS>
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    SearchModal: typeof import('./src/components/common/SearchModal.vue')['default']
    TrendingTopics: typeof import('./src/components/sections/TrendingTopics.vue')['default']
    UserArticles: typeof import('./src/components/profile/UserArticles.vue')['default']
    UserDrafts: typeof import('./src/components/profile/UserDrafts.vue')['default']
    UserFavorites: typeof import('./src/components/profile/UserFavorites.vue')['default']
    UserFollowers: typeof import('./src/components/profile/UserFollowers.vue')['default']
    UserFollowing: typeof import('./src/components/profile/UserFollowing.vue')['default']
  }
}
