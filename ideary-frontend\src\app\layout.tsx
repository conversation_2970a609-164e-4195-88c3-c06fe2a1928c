import type { Metadata } from 'next';
import { Inter } from 'next/font/google';
import { ThemeProvider } from '@/components/providers/theme-provider';
import { AuthProvider } from '@/components/providers/auth-provider';
import { ToastProvider } from '@/components/providers/toast-provider';
import { Header } from '@/components/layout/header';
import { Footer } from '@/components/layout/footer';
import '@/styles/globals.css';

const inter = Inter({ subsets: ['latin'] });

export const metadata: Metadata = {
  title: {
    default: 'Ideary - 技术灵感分享社区',
    template: '%s | Ideary',
  },
  description: '一个专注于技术分享和灵感交流的现代化社区平台，汇聚优秀的技术文章、经验分享和创新思维。',
  keywords: [
    'Ideary',
    '技术博客',
    '技术分享',
    '编程',
    '开发者社区',
    '技术文章',
    '编程教程',
    '开源项目',
    '技术讨论',
    '程序员',
  ],
  authors: [{ name: 'Ideary Team' }],
  creator: 'Ideary Team',
  publisher: 'Ideary',
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL('https://ideary.dev'),
  alternates: {
    canonical: '/',
    languages: {
      'zh-CN': '/zh-CN',
      'en-US': '/en-US',
    },
  },
  openGraph: {
    type: 'website',
    locale: 'zh_CN',
    url: 'https://ideary.dev',
    title: 'Ideary - 技术灵感分享社区',
    description: '一个专注于技术分享和灵感交流的现代化社区平台',
    siteName: 'Ideary',
    images: [
      {
        url: '/images/og-image.png',
        width: 1200,
        height: 630,
        alt: 'Ideary - 技术灵感分享社区',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Ideary - 技术灵感分享社区',
    description: '一个专注于技术分享和灵感交流的现代化社区平台',
    images: ['/images/twitter-image.png'],
    creator: '@ideary_dev',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  verification: {
    google: 'your-google-verification-code',
    yandex: 'your-yandex-verification-code',
    yahoo: 'your-yahoo-verification-code',
  },
  category: 'technology',
  classification: 'Technology Blog',
  referrer: 'origin-when-cross-origin',
  colorScheme: 'light dark',
  themeColor: [
    { media: '(prefers-color-scheme: light)', color: '#ffffff' },
    { media: '(prefers-color-scheme: dark)', color: '#0f172a' },
  ],
  viewport: {
    width: 'device-width',
    initialScale: 1,
    maximumScale: 5,
    userScalable: true,
  },
  appleWebApp: {
    capable: true,
    statusBarStyle: 'default',
    title: 'Ideary',
  },
  applicationName: 'Ideary',
  generator: 'Next.js',
  manifest: '/manifest.json',
  icons: {
    icon: [
      { url: '/favicon-16x16.png', sizes: '16x16', type: 'image/png' },
      { url: '/favicon-32x32.png', sizes: '32x32', type: 'image/png' },
      { url: '/favicon-96x96.png', sizes: '96x96', type: 'image/png' },
    ],
    apple: [
      { url: '/apple-touch-icon.png', sizes: '180x180', type: 'image/png' },
    ],
    other: [
      {
        rel: 'mask-icon',
        url: '/safari-pinned-tab.svg',
        color: '#3b82f6',
      },
    ],
  },
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang='zh-CN' suppressHydrationWarning>
      <head>
        <link rel='preconnect' href='https://fonts.googleapis.com' />
        <link rel='preconnect' href='https://fonts.gstatic.com' crossOrigin='anonymous' />
        <link rel='dns-prefetch' href='//fonts.googleapis.com' />
        <link rel='dns-prefetch' href='//fonts.gstatic.com' />
        <meta name='msapplication-TileColor' content='#3b82f6' />
        <meta name='msapplication-config' content='/browserconfig.xml' />
      </head>
      <body className={`${inter.className} min-h-screen bg-background text-foreground antialiased`}>
        <ThemeProvider
          attribute='class'
          defaultTheme='system'
          enableSystem
          disableTransitionOnChange
        >
          <AuthProvider>
            <ToastProvider>
              <div className='relative flex min-h-screen flex-col'>
                <Header />
                <main className='flex-1'>
                  {children}
                </main>
                <Footer />
              </div>
            </ToastProvider>
          </AuthProvider>
        </ThemeProvider>
        
        {/* 性能监控脚本 */}
        <script
          dangerouslySetInnerHTML={{
            __html: `
              // 页面加载性能监控
              window.addEventListener('load', function() {
                if ('performance' in window) {
                  const perfData = performance.getEntriesByType('navigation')[0];
                  if (perfData) {
                    console.log('页面加载时间:', perfData.loadEventEnd - perfData.fetchStart, 'ms');
                  }
                }
              });
              
              // 错误监控
              window.addEventListener('error', function(e) {
                console.error('页面错误:', e.error);
              });
              
              window.addEventListener('unhandledrejection', function(e) {
                console.error('未处理的Promise拒绝:', e.reason);
              });
            `,
          }}
        />
      </body>
    </html>
  );
}
