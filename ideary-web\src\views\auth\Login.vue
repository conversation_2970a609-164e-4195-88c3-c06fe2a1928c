<template>
  <div class="login-page">
    <div class="login-form">
      <div class="form-header">
        <h1 class="form-title">欢迎回来</h1>
        <p class="form-subtitle">登录您的 Ideary 账户</p>
      </div>

      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        @submit.prevent="handleSubmit"
        class="login-form-content"
        size="large"
      >
        <el-form-item prop="account">
          <el-input
            v-model="form.account"
            placeholder="邮箱或用户名"
            :prefix-icon="User"
            clearable
          />
        </el-form-item>

        <el-form-item prop="password">
          <el-input
            v-model="form.password"
            type="password"
            placeholder="密码"
            :prefix-icon="Lock"
            show-password
            clearable
          />
        </el-form-item>

        <div class="form-options">
          <el-checkbox v-model="form.rememberMe">
            记住我
          </el-checkbox>
          <router-link to="/auth/forgot-password" class="forgot-link">
            忘记密码？
          </router-link>
        </div>

        <el-form-item>
          <el-button
            type="primary"
            native-type="submit"
            :loading="loading"
            class="submit-btn"
          >
            {{ loading ? '登录中...' : '登录' }}
          </el-button>
        </el-form-item>
      </el-form>

      <div class="form-footer">
        <p class="register-link">
          还没有账户？
          <router-link to="/auth/register">立即注册</router-link>
        </p>
      </div>

      <!-- 第三方登录 -->
      <div class="social-login">
        <div class="divider">
          <span>或者</span>
        </div>
        
        <div class="social-buttons">
          <el-button class="social-btn github-btn" @click="handleSocialLogin('github')">
            <el-icon><svg viewBox="0 0 24 24" fill="currentColor"><path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/></svg></el-icon>
            GitHub 登录
          </el-button>
          
          <el-button class="social-btn google-btn" @click="handleSocialLogin('google')">
            <el-icon><svg viewBox="0 0 24 24" fill="currentColor"><path d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z" fill="#4285F4"/><path d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z" fill="#34A853"/><path d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z" fill="#FBBC05"/><path d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z" fill="#EA4335"/></svg></el-icon>
            Google 登录
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { User, Lock } from '@element-plus/icons-vue'
import { useUserStore } from '@/stores/user'
import type { LoginRequest } from '@/types/auth'

const router = useRouter()
const userStore = useUserStore()

// 表单引用
const formRef = ref<FormInstance>()

// 加载状态
const loading = ref(false)

// 表单数据
const form = reactive<LoginRequest>({
  account: '',
  password: '',
  rememberMe: false
})

// 表单验证规则
const rules: FormRules = {
  account: [
    { required: true, message: '请输入邮箱或用户名', trigger: 'blur' },
    { min: 3, message: '用户名至少3个字符', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, message: '密码至少6个字符', trigger: 'blur' }
  ]
}

// 处理表单提交
const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    loading.value = true
    
    await userStore.login(form)
    
    ElMessage.success('登录成功！')
    
    // 跳转到首页或之前的页面
    const redirect = router.currentRoute.value.query.redirect as string
    router.push(redirect || '/')
    
  } catch (error: any) {
    console.error('登录失败:', error)
    ElMessage.error(error.message || '登录失败，请重试')
  } finally {
    loading.value = false
  }
}

// 处理第三方登录
const handleSocialLogin = (provider: string) => {
  ElMessage.info(`${provider} 登录功能开发中...`)
}

// 页面标题
onMounted(() => {
  document.title = '登录 - Ideary'
})
</script>

<style lang="scss" scoped>
.login-page {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
}

.login-form {
  width: 100%;
  max-width: 400px;
  
  .form-header {
    text-align: center;
    margin-bottom: 2rem;
    
    .form-title {
      font-size: 2rem;
      font-weight: bold;
      color: var(--el-text-color-primary);
      margin-bottom: 0.5rem;
    }
    
    .form-subtitle {
      color: var(--el-text-color-regular);
      font-size: 1rem;
    }
  }
  
  .login-form-content {
    .form-options {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 1.5rem;
      
      .forgot-link {
        color: var(--el-color-primary);
        text-decoration: none;
        font-size: 0.875rem;
        
        &:hover {
          text-decoration: underline;
        }
      }
    }
    
    .submit-btn {
      width: 100%;
      height: 48px;
      font-size: 1rem;
      font-weight: 500;
    }
  }
  
  .form-footer {
    text-align: center;
    margin-top: 1.5rem;
    
    .register-link {
      color: var(--el-text-color-regular);
      font-size: 0.875rem;
      
      a {
        color: var(--el-color-primary);
        text-decoration: none;
        font-weight: 500;
        
        &:hover {
          text-decoration: underline;
        }
      }
    }
  }
  
  .social-login {
    margin-top: 2rem;
    
    .divider {
      position: relative;
      text-align: center;
      margin-bottom: 1.5rem;
      
      &::before {
        content: '';
        position: absolute;
        top: 50%;
        left: 0;
        right: 0;
        height: 1px;
        background: var(--el-border-color-light);
      }
      
      span {
        background: var(--el-bg-color);
        padding: 0 1rem;
        color: var(--el-text-color-placeholder);
        font-size: 0.875rem;
      }
    }
    
    .social-buttons {
      display: flex;
      flex-direction: column;
      gap: 0.75rem;
      
      .social-btn {
        width: 100%;
        height: 44px;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
        border: 1px solid var(--el-border-color);
        background: var(--el-bg-color);
        color: var(--el-text-color-primary);
        
        &:hover {
          border-color: var(--el-color-primary);
        }
        
        .el-icon {
          font-size: 1.25rem;
          
          svg {
            width: 1.25rem;
            height: 1.25rem;
          }
        }
        
        &.github-btn:hover {
          background: #24292e;
          color: white;
          border-color: #24292e;
        }
        
        &.google-btn:hover {
          background: #4285f4;
          color: white;
          border-color: #4285f4;
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 480px) {
  .login-page {
    padding: 1rem;
  }
  
  .login-form {
    .form-header {
      .form-title {
        font-size: 1.75rem;
      }
    }
  }
}
</style>
