<template>
  <div class="newsletter-section">
    <div v-if="!isSubscribed" class="newsletter-form">
      <div class="newsletter-content">
        <div class="newsletter-icon">
          <el-icon><Message /></el-icon>
        </div>
        
        <h2 class="newsletter-title">订阅技术周刊</h2>
        <p class="newsletter-description">
          每周为您精选最新的技术文章、开发工具和行业动态，让您紧跟技术前沿
        </p>
        
        <el-form
          ref="formRef"
          :model="form"
          :rules="rules"
          @submit.prevent="handleSubmit"
          class="subscription-form"
        >
          <div class="form-group">
            <el-input
              v-model="form.email"
              type="email"
              placeholder="输入您的邮箱地址"
              size="large"
              class="email-input"
            >
              <template #prefix>
                <el-icon><Message /></el-icon>
              </template>
            </el-input>
            
            <el-button
              type="primary"
              size="large"
              native-type="submit"
              :loading="loading"
              class="subscribe-btn"
            >
              {{ loading ? '订阅中...' : '立即订阅' }}
            </el-button>
          </div>
        </el-form>
        
        <div class="newsletter-features">
          <div class="feature-item">
            <el-icon><CircleCheckFilled /></el-icon>
            <span>精选内容</span>
            <p>每周精心挑选最有价值的技术文章和教程</p>
          </div>
          
          <div class="feature-item">
            <el-icon><CircleCheckFilled /></el-icon>
            <span>行业动态</span>
            <p>第一时间了解技术行业的最新发展和趋势</p>
          </div>
          
          <div class="feature-item">
            <el-icon><CircleCheckFilled /></el-icon>
            <span>开发工具</span>
            <p>推荐实用的开发工具和资源，提升工作效率</p>
          </div>
        </div>
        
        <div class="privacy-notice">
          <el-icon><Lock /></el-icon>
          <span>我们承诺保护您的隐私，不会向第三方分享您的邮箱地址</span>
        </div>
      </div>
    </div>
    
    <div v-else class="success-state">
      <div class="success-content">
        <div class="success-icon">
          <el-icon><CircleCheckFilled /></el-icon>
        </div>
        
        <h2 class="success-title">订阅成功！</h2>
        <p class="success-description">
          感谢您订阅 Ideary 技术周刊！我们会定期为您推送最新的技术文章和行业动态。
        </p>
        
        <div class="success-actions">
          <el-button @click="resetForm" class="reset-btn">
            继续订阅其他邮箱
          </el-button>
          <el-button type="primary" @click="$router.push('/articles')">
            浏览精彩文章
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { 
  Message, 
  CircleCheckFilled, 
  Lock 
} from '@element-plus/icons-vue'

const formRef = ref<FormInstance>()
const loading = ref(false)
const isSubscribed = ref(false)

// 表单数据
const form = reactive({
  email: ''
})

// 验证规则
const rules: FormRules = {
  email: [
    { required: true, message: '请输入邮箱地址', trigger: 'blur' },
    { type: 'email', message: '请输入有效的邮箱地址', trigger: 'blur' }
  ]
}

// 处理订阅
const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    loading.value = true
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1500))
    
    // 订阅成功
    isSubscribed.value = true
    ElMessage.success('订阅成功！感谢您的关注')
    
  } catch (error: any) {
    if (error.message) {
      ElMessage.error(error.message)
    } else {
      ElMessage.error('订阅失败，请稍后重试')
    }
  } finally {
    loading.value = false
  }
}

// 重置表单
const resetForm = () => {
  isSubscribed.value = false
  form.email = ''
  if (formRef.value) {
    formRef.value.resetFields()
  }
}
</script>

<style lang="scss" scoped>
.newsletter-section {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  border-radius: 1.5rem;
  padding: 3rem;
  color: white;
  position: relative;
  overflow: hidden;
  
  @media (max-width: 768px) {
    padding: 2rem 1.5rem;
  }
  
  // 背景装饰
  &::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 16rem;
    height: 16rem;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    transform: translate(8rem, -8rem);
  }
  
  &::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 12rem;
    height: 12rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 50%;
    transform: translate(-6rem, 6rem);
  }
  
  .newsletter-form,
  .success-state {
    position: relative;
    z-index: 1;
  }
  
  .newsletter-content,
  .success-content {
    max-width: 800px;
    margin: 0 auto;
    text-align: center;
  }
  
  .newsletter-icon,
  .success-icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 4rem;
    height: 4rem;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    margin-bottom: 1.5rem;
    
    .el-icon {
      font-size: 2rem;
    }
  }
  
  .newsletter-title,
  .success-title {
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 1rem;
    
    @media (max-width: 768px) {
      font-size: 2rem;
    }
  }
  
  .newsletter-description,
  .success-description {
    font-size: 1.125rem;
    opacity: 0.9;
    margin-bottom: 2rem;
    line-height: 1.6;
    
    @media (max-width: 768px) {
      font-size: 1rem;
    }
  }
  
  .subscription-form {
    margin-bottom: 2.5rem;
    
    .form-group {
      display: flex;
      gap: 1rem;
      max-width: 500px;
      margin: 0 auto;
      
      @media (max-width: 640px) {
        flex-direction: column;
      }
      
      .email-input {
        flex: 1;
        
        :deep(.el-input__wrapper) {
          background: rgba(255, 255, 255, 0.9);
          border: none;
          border-radius: 0.5rem;
          
          .el-input__inner {
            color: var(--el-text-color-primary);
            
            &::placeholder {
              color: var(--el-text-color-placeholder);
            }
          }
          
          .el-input__prefix {
            color: var(--el-text-color-placeholder);
          }
        }
      }
      
      .subscribe-btn {
        background: rgba(255, 255, 255, 0.2);
        border: 1px solid rgba(255, 255, 255, 0.3);
        color: white;
        
        &:hover {
          background: rgba(255, 255, 255, 0.3);
        }
        
        @media (max-width: 640px) {
          width: 100%;
        }
      }
    }
  }
  
  .newsletter-features {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
    
    @media (max-width: 768px) {
      grid-template-columns: 1fr;
      gap: 1rem;
    }
    
    .feature-item {
      text-align: left;
      
      @media (max-width: 768px) {
        text-align: center;
      }
      
      .el-icon {
        color: rgba(255, 255, 255, 0.8);
        margin-right: 0.5rem;
        vertical-align: middle;
      }
      
      > span {
        font-weight: 600;
        font-size: 1rem;
        margin-bottom: 0.5rem;
        display: inline-block;
      }
      
      p {
        font-size: 0.875rem;
        opacity: 0.8;
        line-height: 1.4;
        margin: 0;
      }
    }
  }
  
  .privacy-notice {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    opacity: 0.8;
    
    .el-icon {
      font-size: 1rem;
    }
  }
  
  .success-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    
    @media (max-width: 640px) {
      flex-direction: column;
      align-items: center;
    }
    
    .reset-btn {
      background: rgba(255, 255, 255, 0.1);
      border: 1px solid rgba(255, 255, 255, 0.3);
      color: white;
      
      &:hover {
        background: rgba(255, 255, 255, 0.2);
      }
    }
    
    .el-button--primary {
      background: rgba(255, 255, 255, 0.2);
      border: 1px solid rgba(255, 255, 255, 0.3);
      
      &:hover {
        background: rgba(255, 255, 255, 0.3);
      }
    }
  }
}
</style>
