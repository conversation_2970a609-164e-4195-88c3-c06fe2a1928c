import { Suspense } from 'react';
import { HeroSection } from '@/components/sections/hero-section';
import { FeaturedArticles } from '@/components/sections/featured-articles';
import { PopularAuthors } from '@/components/sections/popular-authors';
import { TrendingTopics } from '@/components/sections/trending-topics';
import { NewsletterSection } from '@/components/sections/newsletter-section';
import { LoadingSpinner } from '@/components/ui/loading-spinner';

export default function HomePage() {
  return (
    <div className="min-h-screen">
      {/* 英雄区域 */}
      <HeroSection />
      
      {/* 主要内容区域 */}
      <div className="container-xl py-12 space-y-16">
        {/* 精选文章 */}
        <section className="space-y-8">
          <div className="text-center">
            <h2 className="text-3xl font-bold text-secondary-900 dark:text-secondary-100">
              精选文章
            </h2>
            <p className="mt-4 text-lg text-secondary-600 dark:text-secondary-400">
              发现最新的技术洞察和创新思维
            </p>
          </div>
          <Suspense fallback={<LoadingSpinner />}>
            <FeaturedArticles />
          </Suspense>
        </section>

        {/* 热门作者 */}
        <section className="space-y-8">
          <div className="text-center">
            <h2 className="text-3xl font-bold text-secondary-900 dark:text-secondary-100">
              热门作者
            </h2>
            <p className="mt-4 text-lg text-secondary-600 dark:text-secondary-400">
              关注优秀的技术分享者
            </p>
          </div>
          <Suspense fallback={<LoadingSpinner />}>
            <PopularAuthors />
          </Suspense>
        </section>

        {/* 热门话题 */}
        <section className="space-y-8">
          <div className="text-center">
            <h2 className="text-3xl font-bold text-secondary-900 dark:text-secondary-100">
              热门话题
            </h2>
            <p className="mt-4 text-lg text-secondary-600 dark:text-secondary-400">
              探索当前最受关注的技术领域
            </p>
          </div>
          <Suspense fallback={<LoadingSpinner />}>
            <TrendingTopics />
          </Suspense>
        </section>

        {/* 邮件订阅 */}
        <NewsletterSection />
      </div>
    </div>
  );
}
