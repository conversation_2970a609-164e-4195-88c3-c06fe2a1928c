'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  EnvelopeIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
} from '@heroicons/react/24/outline';
import { Button } from '@/components/ui/button';
import { isValidEmail } from '@/lib/utils';
import { toast } from 'react-hot-toast';

export function NewsletterSection() {
  const [email, setEmail] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isSubscribed, setIsSubscribed] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!email.trim()) {
      toast.error('请输入邮箱地址');
      return;
    }

    if (!isValidEmail(email)) {
      toast.error('请输入有效的邮箱地址');
      return;
    }

    setIsLoading(true);

    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      // 模拟订阅成功
      setIsSubscribed(true);
      toast.success('订阅成功！感谢您的关注');
      setEmail('');
    } catch (error) {
      toast.error('订阅失败，请稍后重试');
    } finally {
      setIsLoading(false);
    }
  };

  if (isSubscribed) {
    return (
      <motion.section
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
        className="relative overflow-hidden bg-gradient-to-r from-primary-600 to-primary-700 rounded-2xl"
      >
        <div className="absolute inset-0 bg-black/10" />
        <div className="relative px-8 py-12 text-center">
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className="inline-flex items-center justify-center w-16 h-16 bg-white/20 rounded-full mb-6"
          >
            <CheckCircleIcon className="h-8 w-8 text-white" />
          </motion.div>
          
          <h2 className="text-3xl font-bold text-white mb-4">
            订阅成功！
          </h2>
          
          <p className="text-xl text-primary-100 mb-6 max-w-2xl mx-auto">
            感谢您订阅 Ideary 技术周刊！我们会定期为您推送最新的技术文章和行业动态。
          </p>
          
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button
              variant="outline"
              size="lg"
              onClick={() => setIsSubscribed(false)}
              className="bg-white/10 border-white/30 text-white hover:bg-white/20"
            >
              继续订阅其他邮箱
            </Button>
            <Button
              variant="outline"
              size="lg"
              onClick={() => window.location.href = '/articles'}
              className="bg-white/10 border-white/30 text-white hover:bg-white/20"
            >
              浏览精彩文章
            </Button>
          </div>
        </div>
      </motion.section>
    );
  }

  return (
    <motion.section
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
      className="relative overflow-hidden bg-gradient-to-r from-primary-600 to-primary-700 rounded-2xl"
    >
      {/* 背景装饰 */}
      <div className="absolute inset-0">
        <div className="absolute top-0 right-0 w-64 h-64 bg-white/10 rounded-full -translate-y-32 translate-x-32" />
        <div className="absolute bottom-0 left-0 w-48 h-48 bg-white/5 rounded-full translate-y-24 -translate-x-24" />
      </div>

      <div className="relative px-8 py-12 lg:px-12">
        <div className="max-w-4xl mx-auto text-center">
          {/* 图标 */}
          <div className="inline-flex items-center justify-center w-16 h-16 bg-white/20 rounded-full mb-6">
            <EnvelopeIcon className="h-8 w-8 text-white" />
          </div>

          {/* 标题 */}
          <h2 className="text-3xl lg:text-4xl font-bold text-white mb-4">
            订阅技术周刊
          </h2>

          {/* 描述 */}
          <p className="text-xl text-primary-100 mb-8 max-w-2xl mx-auto">
            每周为您精选最新的技术文章、开发工具和行业动态，让您紧跟技术前沿
          </p>

          {/* 订阅表单 */}
          <form onSubmit={handleSubmit} className="max-w-md mx-auto mb-8">
            <div className="flex flex-col sm:flex-row gap-3">
              <div className="flex-1">
                <input
                  type="email"
                  placeholder="输入您的邮箱地址"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  className="w-full px-4 py-3 text-secondary-900 bg-white rounded-lg border-0 focus:outline-none focus:ring-2 focus:ring-white/50 placeholder-secondary-500"
                  disabled={isLoading}
                />
              </div>
              <Button
                type="submit"
                size="lg"
                loading={isLoading}
                className="bg-white text-primary-600 hover:bg-primary-50 focus:ring-white/50"
              >
                {isLoading ? '订阅中...' : '立即订阅'}
              </Button>
            </div>
          </form>

          {/* 特性列表 */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 text-left">
            <div className="flex items-start space-x-3">
              <div className="flex-shrink-0 w-6 h-6 bg-white/20 rounded-full flex items-center justify-center mt-1">
                <CheckCircleIcon className="h-4 w-4 text-white" />
              </div>
              <div>
                <h3 className="font-semibold text-white mb-1">精选内容</h3>
                <p className="text-sm text-primary-100">
                  每周精心挑选最有价值的技术文章和教程
                </p>
              </div>
            </div>

            <div className="flex items-start space-x-3">
              <div className="flex-shrink-0 w-6 h-6 bg-white/20 rounded-full flex items-center justify-center mt-1">
                <CheckCircleIcon className="h-4 w-4 text-white" />
              </div>
              <div>
                <h3 className="font-semibold text-white mb-1">行业动态</h3>
                <p className="text-sm text-primary-100">
                  第一时间了解技术行业的最新发展和趋势
                </p>
              </div>
            </div>

            <div className="flex items-start space-x-3">
              <div className="flex-shrink-0 w-6 h-6 bg-white/20 rounded-full flex items-center justify-center mt-1">
                <CheckCircleIcon className="h-4 w-4 text-white" />
              </div>
              <div>
                <h3 className="font-semibold text-white mb-1">开发工具</h3>
                <p className="text-sm text-primary-100">
                  推荐实用的开发工具和资源，提升工作效率
                </p>
              </div>
            </div>
          </div>

          {/* 隐私说明 */}
          <div className="mt-8 flex items-center justify-center space-x-2 text-sm text-primary-200">
            <ExclamationTriangleIcon className="h-4 w-4" />
            <span>我们承诺保护您的隐私，不会向第三方分享您的邮箱地址</span>
          </div>
        </div>
      </div>
    </motion.section>
  );
}
