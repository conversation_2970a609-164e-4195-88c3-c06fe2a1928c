'use client';

import React, { createContext, useContext, useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { authApi } from '@/lib/api/auth';
import { User, LoginResponse } from '@/types/auth';
import { toast } from 'react-hot-toast';

interface AuthContextType {
  user: User | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  login: (email: string, password: string) => Promise<void>;
  register: (data: RegisterData) => Promise<void>;
  logout: () => Promise<void>;
  refreshToken: () => Promise<void>;
  updateUser: (data: Partial<User>) => void;
}

interface RegisterData {
  username: string;
  email: string;
  password: string;
  confirmPassword: string;
  emailCode: string;
  nickname?: string;
  agreeTerms: boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const router = useRouter();

  const isAuthenticated = !!user;

  // 初始化认证状态
  useEffect(() => {
    const initAuth = async () => {
      try {
        const token = localStorage.getItem('accessToken');
        if (token) {
          // 验证token并获取用户信息
          const userInfo = await authApi.getCurrentUser();
          setUser(userInfo);
        }
      } catch (error) {
        // Token无效，清除本地存储
        localStorage.removeItem('accessToken');
        localStorage.removeItem('refreshToken');
        console.error('认证初始化失败:', error);
      } finally {
        setIsLoading(false);
      }
    };

    initAuth();
  }, []);

  // 登录
  const login = async (account: string, password: string) => {
    try {
      setIsLoading(true);
      const response: LoginResponse = await authApi.login({ account, password });
      
      // 保存token
      localStorage.setItem('accessToken', response.accessToken);
      localStorage.setItem('refreshToken', response.refreshToken);
      
      // 设置用户信息
      setUser({
        id: response.userId,
        username: response.username,
        email: response.email,
        nickname: response.nickname,
        avatar: response.avatar,
        roles: response.roles,
      });

      toast.success('登录成功！');
      router.push('/dashboard');
    } catch (error: any) {
      toast.error(error.message || '登录失败');
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  // 注册
  const register = async (data: RegisterData) => {
    try {
      setIsLoading(true);
      const response: LoginResponse = await authApi.register(data);
      
      // 保存token
      localStorage.setItem('accessToken', response.accessToken);
      localStorage.setItem('refreshToken', response.refreshToken);
      
      // 设置用户信息
      setUser({
        id: response.userId,
        username: response.username,
        email: response.email,
        nickname: response.nickname,
        avatar: response.avatar,
        roles: response.roles,
      });

      toast.success('注册成功！欢迎加入Ideary！');
      router.push('/dashboard');
    } catch (error: any) {
      toast.error(error.message || '注册失败');
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  // 登出
  const logout = async () => {
    try {
      setIsLoading(true);
      
      // 调用登出API
      await authApi.logout();
      
      // 清除本地存储
      localStorage.removeItem('accessToken');
      localStorage.removeItem('refreshToken');
      
      // 清除用户状态
      setUser(null);
      
      toast.success('已安全登出');
      router.push('/');
    } catch (error: any) {
      console.error('登出失败:', error);
      // 即使API调用失败，也要清除本地状态
      localStorage.removeItem('accessToken');
      localStorage.removeItem('refreshToken');
      setUser(null);
      router.push('/');
    } finally {
      setIsLoading(false);
    }
  };

  // 刷新Token
  const refreshToken = async () => {
    try {
      const refreshToken = localStorage.getItem('refreshToken');
      if (!refreshToken) {
        throw new Error('没有刷新Token');
      }

      const response: LoginResponse = await authApi.refreshToken(refreshToken);
      
      // 更新token
      localStorage.setItem('accessToken', response.accessToken);
      
      // 更新用户信息
      setUser({
        id: response.userId,
        username: response.username,
        email: response.email,
        nickname: response.nickname,
        avatar: response.avatar,
        roles: response.roles,
      });
    } catch (error) {
      console.error('刷新Token失败:', error);
      // 刷新失败，清除认证状态
      localStorage.removeItem('accessToken');
      localStorage.removeItem('refreshToken');
      setUser(null);
      router.push('/auth/login');
    }
  };

  // 更新用户信息
  const updateUser = (data: Partial<User>) => {
    if (user) {
      setUser({ ...user, ...data });
    }
  };

  // 自动刷新Token
  useEffect(() => {
    if (!isAuthenticated) return;

    const interval = setInterval(() => {
      refreshToken().catch(console.error);
    }, 30 * 60 * 1000); // 每30分钟刷新一次

    return () => clearInterval(interval);
  }, [isAuthenticated]);

  const value: AuthContextType = {
    user,
    isLoading,
    isAuthenticated,
    login,
    register,
    logout,
    refreshToken,
    updateUser,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}
