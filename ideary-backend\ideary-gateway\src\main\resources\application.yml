# Ideary网关服务配置文件
server:
  port: 9080
  servlet:
    context-path: /

spring:
  application:
    name: ideary-gateway
  
  # 配置中心
  cloud:
    nacos:
      discovery:
        server-addr: localhost:8848
        namespace: public
        group: DEFAULT_GROUP
        service: ${spring.application.name}
        metadata:
          version: 1.0.0
          author: Ideary Team
      config:
        server-addr: localhost:8848
        namespace: public
        group: DEFAULT_GROUP
        file-extension: yml
        shared-configs:
          - data-id: common-config.yml
            group: DEFAULT_GROUP
            refresh: true
    
    # 网关配置
    gateway:
      # 全局跨域配置
      globalcors:
        cors-configurations:
          '[/**]':
            allowed-origin-patterns: "*"
            allowed-methods: "*"
            allowed-headers: "*"
            allow-credentials: true
            max-age: 3600
      
      # 路由配置
      routes:
        # 用户服务路由
        - id: ideary-user
          uri: lb://ideary-user
          predicates:
            - Path=/api/v1/users/**,/api/v1/auth/**,/api/v1/profiles/**
          filters:
            - name: RequestRateLimiter
              args:
                redis-rate-limiter.replenishRate: 10
                redis-rate-limiter.burstCapacity: 20
                redis-rate-limiter.requestedTokens: 1
                key-resolver: "#{@ipKeyResolver}"
            - name: AuthFilter
              args:
                excludePaths: /api/v1/auth/login,/api/v1/auth/register,/api/v1/auth/refresh
        
        # 内容服务路由
        - id: ideary-content
          uri: lb://ideary-content
          predicates:
            - Path=/api/v1/articles/**,/api/v1/comments/**,/api/v1/categories/**,/api/v1/tags/**
          filters:
            - name: RequestRateLimiter
              args:
                redis-rate-limiter.replenishRate: 20
                redis-rate-limiter.burstCapacity: 40
                redis-rate-limiter.requestedTokens: 1
                key-resolver: "#{@ipKeyResolver}"
            - name: AuthFilter
              args:
                excludePaths: /api/v1/articles/list,/api/v1/articles/*/detail,/api/v1/categories,/api/v1/tags
        
        # 搜索服务路由
        - id: ideary-search
          uri: lb://ideary-search
          predicates:
            - Path=/api/v1/search/**
          filters:
            - name: RequestRateLimiter
              args:
                redis-rate-limiter.replenishRate: 30
                redis-rate-limiter.burstCapacity: 60
                redis-rate-limiter.requestedTokens: 1
                key-resolver: "#{@ipKeyResolver}"
        
        # 管理服务路由
        - id: ideary-admin
          uri: lb://ideary-admin
          predicates:
            - Path=/api/v1/admin/**
          filters:
            - name: RequestRateLimiter
              args:
                redis-rate-limiter.replenishRate: 5
                redis-rate-limiter.burstCapacity: 10
                redis-rate-limiter.requestedTokens: 1
                key-resolver: "#{@ipKeyResolver}"
            - name: AuthFilter
              args:
                requireRoles: ADMIN,SUPER_ADMIN
        
        # 公共服务路由
        - id: ideary-common
          uri: lb://ideary-common
          predicates:
            - Path=/api/v1/upload/**,/api/v1/notifications/**,/api/v1/sms/**,/api/v1/email/**
          filters:
            - name: RequestRateLimiter
              args:
                redis-rate-limiter.replenishRate: 15
                redis-rate-limiter.burstCapacity: 30
                redis-rate-limiter.requestedTokens: 1
                key-resolver: "#{@ipKeyResolver}"
            - name: AuthFilter
              args:
                excludePaths: /api/v1/upload/avatar
      
      # 默认过滤器
      default-filters:
        - name: Retry
          args:
            retries: 3
            statuses: BAD_GATEWAY,GATEWAY_TIMEOUT
            methods: GET,POST
        - name: CircuitBreaker
          args:
            name: default
            fallbackUri: forward:/fallback
        - AddRequestHeader=X-Request-Source, gateway
        - AddRequestHeader=X-Request-Time, "#{T(System).currentTimeMillis()}"
  
  # Redis配置
  data:
    redis:
      host: localhost
      port: 6379
      database: 0
      timeout: 3000ms
      lettuce:
        pool:
          max-active: 8
          max-idle: 8
          min-idle: 0
          max-wait: -1ms

# 管理端点配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus,gateway
  endpoint:
    health:
      show-details: always
  info:
    env:
      enabled: true

# 应用信息
info:
  app:
    name: ${spring.application.name}
    description: Ideary技术灵感分享社区 - API网关服务
    version: 1.0.0
    author: Ideary Team
  build:
    java-version: ${java.version}
    spring-boot-version: ${spring-boot.version}

# 日志配置
logging:
  level:
    com.ideary.gateway: DEBUG
    org.springframework.cloud.gateway: DEBUG
    org.springframework.web.reactive: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%logger{50}] - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%logger{50}] - %msg%n"
  file:
    name: logs/ideary-gateway.log
    max-size: 100MB
    max-history: 30

# JWT配置
jwt:
  secret: ideary-jwt-secret-key-2024
  expiration: 86400000  # 24小时
  refresh-expiration: 604800000  # 7天
  header: Authorization
  prefix: "Bearer "

# 限流配置
rate-limit:
  # 默认限流配置
  default:
    replenish-rate: 10
    burst-capacity: 20
  # 登录接口特殊限流
  login:
    replenish-rate: 5
    burst-capacity: 10
  # 注册接口特殊限流
  register:
    replenish-rate: 3
    burst-capacity: 5
