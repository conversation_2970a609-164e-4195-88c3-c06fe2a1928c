package com.ideary.gateway.controller;

import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * 熔断降级控制器
 * 
 * 功能说明：
 * 1. 提供服务熔断时的降级响应
 * 2. 统一的降级处理逻辑
 * 3. 友好的错误提示
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 2024-01-01
 */
@RestController
public class FallbackController {

    /**
     * 通用降级处理
     */
    @RequestMapping("/fallback")
    public ResponseEntity<Map<String, Object>> fallback() {
        Map<String, Object> result = new HashMap<>();
        result.put("code", 503);
        result.put("message", "服务暂时不可用，请稍后重试");
        result.put("data", null);
        result.put("timestamp", new Date());
        
        return ResponseEntity.status(HttpStatus.SERVICE_UNAVAILABLE).body(result);
    }

    /**
     * 用户服务降级处理
     */
    @RequestMapping("/fallback/user")
    public ResponseEntity<Map<String, Object>> userFallback() {
        Map<String, Object> result = new HashMap<>();
        result.put("code", 503);
        result.put("message", "用户服务暂时不可用，请稍后重试");
        result.put("data", null);
        result.put("timestamp", new Date());
        
        return ResponseEntity.status(HttpStatus.SERVICE_UNAVAILABLE).body(result);
    }

    /**
     * 内容服务降级处理
     */
    @RequestMapping("/fallback/content")
    public ResponseEntity<Map<String, Object>> contentFallback() {
        Map<String, Object> result = new HashMap<>();
        result.put("code", 503);
        result.put("message", "内容服务暂时不可用，请稍后重试");
        result.put("data", null);
        result.put("timestamp", new Date());
        
        return ResponseEntity.status(HttpStatus.SERVICE_UNAVAILABLE).body(result);
    }

    /**
     * 搜索服务降级处理
     */
    @RequestMapping("/fallback/search")
    public ResponseEntity<Map<String, Object>> searchFallback() {
        Map<String, Object> result = new HashMap<>();
        result.put("code", 503);
        result.put("message", "搜索服务暂时不可用，请稍后重试");
        result.put("data", null);
        result.put("timestamp", new Date());
        
        return ResponseEntity.status(HttpStatus.SERVICE_UNAVAILABLE).body(result);
    }

    /**
     * 管理服务降级处理
     */
    @RequestMapping("/fallback/admin")
    public ResponseEntity<Map<String, Object>> adminFallback() {
        Map<String, Object> result = new HashMap<>();
        result.put("code", 503);
        result.put("message", "管理服务暂时不可用，请稍后重试");
        result.put("data", null);
        result.put("timestamp", new Date());
        
        return ResponseEntity.status(HttpStatus.SERVICE_UNAVAILABLE).body(result);
    }

    /**
     * 公共服务降级处理
     */
    @RequestMapping("/fallback/common")
    public ResponseEntity<Map<String, Object>> commonFallback() {
        Map<String, Object> result = new HashMap<>();
        result.put("code", 503);
        result.put("message", "公共服务暂时不可用，请稍后重试");
        result.put("data", null);
        result.put("timestamp", new Date());
        
        return ResponseEntity.status(HttpStatus.SERVICE_UNAVAILABLE).body(result);
    }
}
