<template>
  <div class="write-page">
    <div class="page-container">
      <div class="write-header">
        <h1>写文章</h1>
        <div class="header-actions">
          <el-button @click="saveDraft" :loading="savingDraft">
            <el-icon><Document /></el-icon>
            保存草稿
          </el-button>
          <el-button type="primary" @click="publishArticle" :loading="publishing">
            <el-icon><Upload /></el-icon>
            发布文章
          </el-button>
        </div>
      </div>

      <el-form ref="formRef" :model="form" :rules="rules" class="write-form">
        <el-form-item prop="title">
          <el-input
            v-model="form.title"
            placeholder="请输入文章标题..."
            size="large"
            class="title-input"
          />
        </el-form-item>

        <el-form-item prop="tags">
          <el-select
            v-model="form.tags"
            multiple
            filterable
            allow-create
            placeholder="选择或创建标签"
            class="tags-select"
          >
            <el-option
              v-for="tag in availableTags"
              :key="tag"
              :label="tag"
              :value="tag"
            />
          </el-select>
        </el-form-item>

        <el-form-item prop="summary">
          <el-input
            v-model="form.summary"
            type="textarea"
            :rows="3"
            placeholder="请输入文章摘要..."
            maxlength="200"
            show-word-limit
          />
        </el-form-item>

        <el-form-item prop="content">
          <div class="editor-container">
            <div class="editor-toolbar">
              <el-button-group>
                <el-button size="small" @click="insertMarkdown('**', '**')">
                  <el-icon><Bold /></el-icon>
                </el-button>
                <el-button size="small" @click="insertMarkdown('*', '*')">
                  <el-icon><Italic /></el-icon>
                </el-button>
                <el-button size="small" @click="insertMarkdown('`', '`')">
                  <el-icon><Code /></el-icon>
                </el-button>
              </el-button-group>
              
              <el-button-group>
                <el-button size="small" @click="insertMarkdown('# ', '')">H1</el-button>
                <el-button size="small" @click="insertMarkdown('## ', '')">H2</el-button>
                <el-button size="small" @click="insertMarkdown('### ', '')">H3</el-button>
              </el-button-group>
              
              <el-button-group>
                <el-button size="small" @click="insertMarkdown('- ', '')">
                  <el-icon><List /></el-icon>
                </el-button>
                <el-button size="small" @click="insertMarkdown('[链接文字](', ')')">
                  <el-icon><Link /></el-icon>
                </el-button>
                <el-button size="small" @click="insertImage">
                  <el-icon><Picture /></el-icon>
                </el-button>
              </el-button-group>
            </div>
            
            <el-input
              ref="contentEditor"
              v-model="form.content"
              type="textarea"
              :rows="20"
              placeholder="开始写作吧... 支持 Markdown 语法"
              class="content-editor"
            />
          </div>
        </el-form-item>

        <el-form-item label="封面图片">
          <el-upload
            class="cover-uploader"
            action="#"
            :show-file-list="false"
            :before-upload="beforeCoverUpload"
            :http-request="handleCoverUpload"
          >
            <img v-if="form.coverImage" :src="form.coverImage" class="cover-preview" />
            <el-icon v-else class="cover-uploader-icon"><Plus /></el-icon>
          </el-upload>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage, ElMessageBox, type FormInstance, type FormRules } from 'element-plus'
import {
  Document,
  Upload,
  Bold,
  Italic,
  Code,
  List,
  Link,
  Picture,
  Plus
} from '@element-plus/icons-vue'

const router = useRouter()
const route = useRoute()

// 表单引用
const formRef = ref<FormInstance>()
const contentEditor = ref<HTMLTextAreaElement>()

// 加载状态
const savingDraft = ref(false)
const publishing = ref(false)

// 表单数据
const form = reactive({
  title: '',
  summary: '',
  content: '',
  tags: [] as string[],
  coverImage: ''
})

// 可用标签
const availableTags = ref([
  'JavaScript', 'TypeScript', 'React', 'Vue.js', 'Node.js',
  '前端开发', '后端开发', '全栈开发', '移动开发', '人工智能',
  '数据科学', '云计算', '架构设计', '开发工具', '最佳实践'
])

// 表单验证规则
const rules: FormRules = {
  title: [
    { required: true, message: '请输入文章标题', trigger: 'blur' },
    { min: 5, message: '标题至少5个字符', trigger: 'blur' },
    { max: 100, message: '标题不能超过100个字符', trigger: 'blur' }
  ],
  summary: [
    { required: true, message: '请输入文章摘要', trigger: 'blur' },
    { min: 10, message: '摘要至少10个字符', trigger: 'blur' }
  ],
  content: [
    { required: true, message: '请输入文章内容', trigger: 'blur' },
    { min: 100, message: '内容至少100个字符', trigger: 'blur' }
  ],
  tags: [
    { required: true, message: '请选择至少一个标签', trigger: 'change' }
  ]
}

// 插入Markdown语法
const insertMarkdown = (before: string, after: string) => {
  const textarea = contentEditor.value?.textarea
  if (!textarea) return

  const start = textarea.selectionStart
  const end = textarea.selectionEnd
  const selectedText = form.content.substring(start, end)
  
  const newText = before + selectedText + after
  form.content = form.content.substring(0, start) + newText + form.content.substring(end)
  
  // 设置光标位置
  setTimeout(() => {
    textarea.focus()
    const newCursorPos = start + before.length + selectedText.length
    textarea.setSelectionRange(newCursorPos, newCursorPos)
  }, 0)
}

// 插入图片
const insertImage = () => {
  ElMessageBox.prompt('请输入图片URL', '插入图片', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    inputPattern: /^https?:\/\/.+/,
    inputErrorMessage: '请输入有效的图片URL'
  }).then(({ value }) => {
    insertMarkdown(`![图片描述](${value})`, '')
  }).catch(() => {
    // 用户取消
  })
}

// 封面上传前检查
const beforeCoverUpload = (file: File) => {
  const isImage = file.type.startsWith('image/')
  const isLt2M = file.size / 1024 / 1024 < 2

  if (!isImage) {
    ElMessage.error('只能上传图片文件!')
    return false
  }
  if (!isLt2M) {
    ElMessage.error('图片大小不能超过 2MB!')
    return false
  }
  return true
}

// 处理封面上传
const handleCoverUpload = (options: any) => {
  const file = options.file
  const reader = new FileReader()
  
  reader.onload = (e) => {
    form.coverImage = e.target?.result as string
  }
  
  reader.readAsDataURL(file)
}

// 保存草稿
const saveDraft = async () => {
  try {
    savingDraft.value = true
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    ElMessage.success('草稿保存成功')
  } catch (error) {
    ElMessage.error('保存草稿失败，请重试')
  } finally {
    savingDraft.value = false
  }
}

// 发布文章
const publishArticle = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    
    publishing.value = true
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1500))
    
    ElMessage.success('文章发布成功！')
    router.push('/profile')
    
  } catch (error: any) {
    if (error.message) {
      ElMessage.error(error.message)
    } else {
      ElMessage.error('发布失败，请重试')
    }
  } finally {
    publishing.value = false
  }
}

// 初始化
onMounted(() => {
  // 如果是编辑草稿，加载草稿内容
  const draftId = route.query.draft
  if (draftId) {
    // 模拟加载草稿
    form.title = '草稿标题'
    form.summary = '草稿摘要'
    form.content = '草稿内容...'
    form.tags = ['JavaScript']
  }
  
  document.title = '写文章 - Ideary'
})
</script>

<style lang="scss" scoped>
.write-page {
  min-height: 100vh;
  background: var(--el-bg-color-page);
}

.page-container {
  max-width: 1000px;
  margin: 0 auto;
  padding: 2rem 1rem;
}

.write-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  
  h1 {
    font-size: 2rem;
    font-weight: bold;
    color: var(--el-text-color-primary);
  }
  
  .header-actions {
    display: flex;
    gap: 1rem;
  }
}

.write-form {
  background: var(--el-bg-color);
  border-radius: 1rem;
  padding: 2rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  
  .title-input {
    :deep(.el-input__wrapper) {
      font-size: 1.5rem;
      font-weight: 600;
    }
  }
  
  .tags-select {
    width: 100%;
  }
  
  .editor-container {
    border: 1px solid var(--el-border-color);
    border-radius: 0.5rem;
    overflow: hidden;
    
    .editor-toolbar {
      display: flex;
      gap: 0.5rem;
      padding: 0.75rem;
      background: var(--el-fill-color-lighter);
      border-bottom: 1px solid var(--el-border-color);
    }
    
    .content-editor {
      :deep(.el-textarea__inner) {
        border: none;
        border-radius: 0;
        font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        line-height: 1.6;
      }
    }
  }
  
  .cover-uploader {
    :deep(.el-upload) {
      border: 1px dashed var(--el-border-color);
      border-radius: 6px;
      cursor: pointer;
      position: relative;
      overflow: hidden;
      transition: var(--el-transition-duration-fast);
      
      &:hover {
        border-color: var(--el-color-primary);
      }
    }
    
    .cover-uploader-icon {
      font-size: 28px;
      color: #8c939d;
      width: 178px;
      height: 178px;
      text-align: center;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    
    .cover-preview {
      width: 178px;
      height: 178px;
      display: block;
      object-fit: cover;
    }
  }
}
</style>
