import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { authApi } from '@/api/auth'
import type { User, LoginRequest, RegisterRequest } from '@/types/auth'
import { ElMessage } from 'element-plus'

export const useUserStore = defineStore('user', () => {
  // 状态
  const user = ref<User | null>(null)
  const token = ref<string>('')
  const refreshToken = ref<string>('')
  const loading = ref(false)

  // 计算属性
  const isAuthenticated = computed(() => !!token.value && !!user.value)
  const userInfo = computed(() => user.value)
  const isLoading = computed(() => loading.value)

  // 设置用户信息
  const setUser = (userData: User) => {
    user.value = userData
  }

  // 设置Token
  const setToken = (accessToken: string, refreshTokenValue?: string) => {
    token.value = accessToken
    if (refreshTokenValue) {
      refreshToken.value = refreshTokenValue
    }
    
    // 保存到localStorage
    localStorage.setItem('ideary-token', accessToken)
    if (refreshTokenValue) {
      localStorage.setItem('ideary-refresh-token', refreshTokenValue)
    }
  }

  // 清除用户信息
  const clearUser = () => {
    user.value = null
    token.value = ''
    refreshToken.value = ''
    
    // 清除localStorage
    localStorage.removeItem('ideary-token')
    localStorage.removeItem('ideary-refresh-token')
    localStorage.removeItem('ideary-user')
  }

  // 登录
  const login = async (loginData: LoginRequest) => {
    try {
      loading.value = true
      const response = await authApi.login(loginData)
      
      // 设置用户信息和token
      setUser({
        id: response.userId,
        username: response.username,
        email: response.email,
        nickname: response.nickname,
        avatar: response.avatar,
        roles: response.roles
      })
      
      setToken(response.accessToken, response.refreshToken)
      
      // 保存用户信息到localStorage
      localStorage.setItem('ideary-user', JSON.stringify(user.value))
      
      ElMessage.success('登录成功！')
      return response
    } catch (error: any) {
      ElMessage.error(error.message || '登录失败')
      throw error
    } finally {
      loading.value = false
    }
  }

  // 注册
  const register = async (registerData: RegisterRequest) => {
    try {
      loading.value = true
      const response = await authApi.register(registerData)
      
      // 设置用户信息和token
      setUser({
        id: response.userId,
        username: response.username,
        email: response.email,
        nickname: response.nickname,
        avatar: response.avatar,
        roles: response.roles
      })
      
      setToken(response.accessToken, response.refreshToken)
      
      // 保存用户信息到localStorage
      localStorage.setItem('ideary-user', JSON.stringify(user.value))
      
      ElMessage.success('注册成功！欢迎加入Ideary！')
      return response
    } catch (error: any) {
      ElMessage.error(error.message || '注册失败')
      throw error
    } finally {
      loading.value = false
    }
  }

  // 登出
  const logout = async () => {
    try {
      loading.value = true
      
      // 调用登出API
      if (token.value) {
        await authApi.logout()
      }
      
      // 清除用户信息
      clearUser()
      
      ElMessage.success('已安全登出')
    } catch (error: any) {
      console.error('登出失败:', error)
      // 即使API调用失败，也要清除本地状态
      clearUser()
    } finally {
      loading.value = false
    }
  }

  // 检查认证状态
  const checkAuth = async () => {
    try {
      // 从localStorage恢复token
      const savedToken = localStorage.getItem('ideary-token')
      const savedRefreshToken = localStorage.getItem('ideary-refresh-token')
      const savedUser = localStorage.getItem('ideary-user')
      
      if (savedToken && savedUser) {
        token.value = savedToken
        if (savedRefreshToken) {
          refreshToken.value = savedRefreshToken
        }
        
        try {
          user.value = JSON.parse(savedUser)
          
          // 验证token是否有效
          await authApi.getCurrentUser()
        } catch (error) {
          // token无效，清除本地数据
          clearUser()
        }
      }
    } catch (error) {
      console.error('检查认证状态失败:', error)
      clearUser()
    }
  }

  // 刷新token
  const refreshAccessToken = async () => {
    try {
      if (!refreshToken.value) {
        throw new Error('没有刷新token')
      }
      
      const response = await authApi.refreshToken(refreshToken.value)
      setToken(response.accessToken)
      
      return response.accessToken
    } catch (error) {
      console.error('刷新token失败:', error)
      clearUser()
      throw error
    }
  }

  // 更新用户信息
  const updateUser = (userData: Partial<User>) => {
    if (user.value) {
      user.value = { ...user.value, ...userData }
      localStorage.setItem('ideary-user', JSON.stringify(user.value))
    }
  }

  return {
    // 状态
    user: readonly(user),
    token: readonly(token),
    refreshToken: readonly(refreshToken),
    loading: readonly(loading),

    // 计算属性
    isAuthenticated,
    userInfo,
    isLoading,

    // 方法
    setUser,
    setToken,
    clearUser,
    login,
    register,
    logout,
    checkAuth,
    refreshAccessToken,
    updateUser
  }
})
