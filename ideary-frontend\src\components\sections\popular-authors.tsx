'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { motion } from 'framer-motion';
import { 
  UserPlusIcon,
  DocumentTextIcon,
  HeartIcon,
  EyeIcon,
  CheckBadgeIcon,
} from '@heroicons/react/24/outline';
import { Avatar } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { formatNumber } from '@/lib/utils';
import { useAuth } from '@/components/providers/auth-provider';

interface Author {
  id: number;
  username: string;
  nickname: string;
  avatar?: string;
  bio?: string;
  isVerified: boolean;
  isFollowing: boolean;
  followersCount: number;
  articlesCount: number;
  totalLikes: number;
  totalViews: number;
  specialties: string[];
}

export function PopularAuthors() {
  const [authors, setAuthors] = useState<Author[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { isAuthenticated } = useAuth();

  useEffect(() => {
    fetchPopularAuthors();
  }, []);

  const fetchPopularAuthors = async () => {
    try {
      setLoading(true);
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 800));
      
      // 模拟数据
      const mockAuthors: <AUTHORS>
        {
          id: 1,
          username: 'react_master',
          nickname: 'React 大师',
          avatar: 'https://avatars.githubusercontent.com/u/1?v=4',
          bio: '专注于 React 生态系统开发，拥有 8 年前端开发经验，热爱分享技术心得。',
          isVerified: true,
          isFollowing: false,
          followersCount: 12500,
          articlesCount: 156,
          totalLikes: 45600,
          totalViews: 890000,
          specialties: ['React', 'TypeScript', '前端架构'],
        },
        {
          id: 2,
          username: 'vue_expert',
          nickname: 'Vue.js 专家',
          avatar: 'https://avatars.githubusercontent.com/u/2?v=4',
          bio: 'Vue.js 核心团队成员，致力于推广 Vue 生态系统，分享最佳实践和设计模式。',
          isVerified: true,
          isFollowing: true,
          followersCount: 9800,
          articlesCount: 98,
          totalLikes: 32400,
          totalViews: 650000,
          specialties: ['Vue.js', 'Nuxt.js', '组件设计'],
        },
        {
          id: 3,
          username: 'fullstack_dev',
          nickname: '全栈开发者',
          avatar: 'https://avatars.githubusercontent.com/u/3?v=4',
          bio: '全栈工程师，专注于现代 Web 技术栈，从前端到后端，从设计到部署。',
          isVerified: false,
          isFollowing: false,
          followersCount: 7600,
          articlesCount: 124,
          totalLikes: 28900,
          totalViews: 520000,
          specialties: ['Node.js', 'Python', 'DevOps'],
        },
        {
          id: 4,
          username: 'mobile_dev',
          nickname: '移动端开发',
          avatar: 'https://avatars.githubusercontent.com/u/4?v=4',
          bio: 'React Native 和 Flutter 开发专家，专注于跨平台移动应用开发。',
          isVerified: true,
          isFollowing: false,
          followersCount: 6200,
          articlesCount: 87,
          totalLikes: 21500,
          totalViews: 380000,
          specialties: ['React Native', 'Flutter', '移动开发'],
        },
        {
          id: 5,
          username: 'ai_researcher',
          nickname: 'AI 研究员',
          avatar: 'https://avatars.githubusercontent.com/u/5?v=4',
          bio: '人工智能研究员，专注于机器学习和深度学习在 Web 开发中的应用。',
          isVerified: true,
          isFollowing: false,
          followersCount: 8900,
          articlesCount: 76,
          totalLikes: 35600,
          totalViews: 720000,
          specialties: ['AI/ML', 'TensorFlow', 'Python'],
        },
        {
          id: 6,
          username: 'ui_designer',
          nickname: 'UI/UX 设计师',
          avatar: 'https://avatars.githubusercontent.com/u/6?v=4',
          bio: '资深 UI/UX 设计师，专注于用户体验设计和前端实现的完美结合。',
          isVerified: false,
          isFollowing: true,
          followersCount: 5400,
          articlesCount: 65,
          totalLikes: 18700,
          totalViews: 290000,
          specialties: ['UI/UX', '设计系统', 'Figma'],
        },
      ];

      setAuthors(mockAuthors);
    } catch (err) {
      setError('加载作者信息失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  const handleFollow = async (authorId: number) => {
    if (!isAuthenticated) {
      // 跳转到登录页面
      window.location.href = '/auth/login';
      return;
    }

    try {
      // 模拟API调用
      setAuthors(prev => prev.map(author => 
        author.id === authorId 
          ? { 
              ...author, 
              isFollowing: !author.isFollowing,
              followersCount: author.isFollowing 
                ? author.followersCount - 1 
                : author.followersCount + 1
            }
          : author
      ));
    } catch (error) {
      console.error('关注操作失败:', error);
    }
  };

  if (loading) {
    return <LoadingSpinner text="加载热门作者中..." />;
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <p className="text-error-600 mb-4">{error}</p>
        <Button onClick={fetchPopularAuthors}>重试</Button>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {authors.map((author, index) => (
        <motion.div
          key={author.id}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: index * 0.1 }}
        >
          <div className="card group hover:shadow-medium transition-all duration-300">
            <div className="card-body text-center">
              {/* 头像和认证标识 */}
              <div className="relative inline-block mb-4">
                <Avatar
                  src={author.avatar}
                  alt={author.nickname}
                  size="xl"
                  className="mx-auto"
                />
                {author.isVerified && (
                  <div className="absolute -bottom-1 -right-1 bg-white dark:bg-secondary-800 rounded-full p-1">
                    <CheckBadgeIcon className="h-5 w-5 text-primary-600" />
                  </div>
                )}
              </div>

              {/* 作者信息 */}
              <div className="mb-4">
                <h3 className="text-lg font-semibold text-secondary-900 dark:text-secondary-100 mb-1">
                  <Link 
                    href={`/authors/${author.username}`}
                    className="hover:text-primary-600 transition-colors duration-200"
                  >
                    {author.nickname}
                  </Link>
                </h3>
                <p className="text-sm text-secondary-500 mb-2">@{author.username}</p>
                <p className="text-sm text-secondary-600 dark:text-secondary-400 line-clamp-2">
                  {author.bio}
                </p>
              </div>

              {/* 专业领域标签 */}
              <div className="flex flex-wrap justify-center gap-1 mb-4">
                {author.specialties.slice(0, 3).map((specialty) => (
                  <span
                    key={specialty}
                    className="px-2 py-1 text-xs bg-primary-100 dark:bg-primary-900 text-primary-800 dark:text-primary-200 rounded-full"
                  >
                    {specialty}
                  </span>
                ))}
              </div>

              {/* 统计数据 */}
              <div className="grid grid-cols-2 gap-4 mb-4 text-sm">
                <div className="text-center">
                  <div className="flex items-center justify-center space-x-1 text-secondary-500 mb-1">
                    <DocumentTextIcon className="h-4 w-4" />
                    <span>文章</span>
                  </div>
                  <div className="font-semibold text-secondary-900 dark:text-secondary-100">
                    {formatNumber(author.articlesCount)}
                  </div>
                </div>
                <div className="text-center">
                  <div className="flex items-center justify-center space-x-1 text-secondary-500 mb-1">
                    <HeartIcon className="h-4 w-4" />
                    <span>获赞</span>
                  </div>
                  <div className="font-semibold text-secondary-900 dark:text-secondary-100">
                    {formatNumber(author.totalLikes)}
                  </div>
                </div>
                <div className="text-center">
                  <div className="flex items-center justify-center space-x-1 text-secondary-500 mb-1">
                    <UserPlusIcon className="h-4 w-4" />
                    <span>粉丝</span>
                  </div>
                  <div className="font-semibold text-secondary-900 dark:text-secondary-100">
                    {formatNumber(author.followersCount)}
                  </div>
                </div>
                <div className="text-center">
                  <div className="flex items-center justify-center space-x-1 text-secondary-500 mb-1">
                    <EyeIcon className="h-4 w-4" />
                    <span>阅读</span>
                  </div>
                  <div className="font-semibold text-secondary-900 dark:text-secondary-100">
                    {formatNumber(author.totalViews)}
                  </div>
                </div>
              </div>

              {/* 关注按钮 */}
              <Button
                variant={author.isFollowing ? "outline" : "primary"}
                size="sm"
                onClick={() => handleFollow(author.id)}
                className="w-full"
              >
                <UserPlusIcon className="h-4 w-4 mr-2" />
                {author.isFollowing ? '已关注' : '关注'}
              </Button>
            </div>
          </div>
        </motion.div>
      ))}
    </div>
  );
}
