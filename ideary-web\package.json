{"name": "ideary-web", "version": "1.0.0", "description": "Ideary技术灵感分享社区 - Vue 3前端应用", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc && vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "type-check": "vue-tsc --noEmit", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage"}, "dependencies": {"vue": "^3.4.15", "vue-router": "^4.2.5", "pinia": "^2.1.7", "element-plus": "^2.4.4", "@element-plus/icons-vue": "^2.3.1", "axios": "^1.6.2", "@vueuse/core": "^10.7.0", "dayjs": "^1.11.10", "markdown-it": "^14.0.0", "highlight.js": "^11.9.0", "nprogress": "^0.2.0", "lodash-es": "^4.17.21"}, "devDependencies": {"@types/node": "^20.10.5", "@types/markdown-it": "^13.0.7", "@types/nprogress": "^0.2.3", "@types/lodash-es": "^4.17.12", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "@vitejs/plugin-vue": "^5.0.2", "@vue/eslint-config-prettier": "^9.0.0", "@vue/eslint-config-typescript": "^12.0.0", "@vue/test-utils": "^2.4.3", "@vue/tsconfig": "^0.5.1", "eslint": "^8.56.0", "eslint-plugin-vue": "^9.19.2", "jsdom": "^23.2.0", "prettier": "^3.1.1", "typescript": "^5.3.3", "unplugin-auto-import": "^0.17.2", "unplugin-vue-components": "^0.26.0", "vite": "^5.0.10", "vitest": "^1.1.0", "vue-tsc": "^1.8.25"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "keywords": ["ideary", "blog", "vue3", "typescript", "element-plus", "vite"], "author": "Ideary Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/ideary-team/ideary-blog.git"}}