<template>
  <div class="articles-page">
    <div class="page-container">
      <!-- 页面头部 -->
      <div class="page-header">
        <h1 class="page-title">技术文章</h1>
        <p class="page-subtitle">发现最新的技术文章和深度思考</p>
      </div>

      <!-- 筛选和搜索 -->
      <div class="filters-section">
        <div class="filters-container">
          <!-- 搜索框 -->
          <div class="search-box">
            <el-input
              v-model="searchQuery"
              placeholder="搜索文章标题、内容或作者..."
              @input="handleSearch"
              clearable
              size="large"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
          </div>

          <!-- 筛选选项 -->
          <div class="filter-options">
            <el-select
              v-model="selectedCategory"
              placeholder="选择分类"
              @change="handleFilter"
              clearable
              size="large"
            >
              <el-option
                v-for="category in categories"
                :key="category.value"
                :label="category.label"
                :value="category.value"
              />
            </el-select>

            <el-select
              v-model="sortBy"
              placeholder="排序方式"
              @change="handleSort"
              size="large"
            >
              <el-option label="最新发布" value="latest" />
              <el-option label="最多阅读" value="views" />
              <el-option label="最多点赞" value="likes" />
              <el-option label="最多评论" value="comments" />
            </el-select>
          </div>
        </div>
      </div>

      <!-- 文章列表 -->
      <div class="articles-section">
        <el-skeleton v-if="loading" :rows="5" animated />
        
        <div v-else-if="articles.length > 0" class="articles-list">
          <article
            v-for="article in articles"
            :key="article.id"
            class="article-item"
          >
            <div class="article-content">
              <!-- 文章标签 -->
              <div class="article-tags">
                <el-tag
                  v-for="tag in article.tags.slice(0, 3)"
                  :key="tag"
                  type="primary"
                  effect="plain"
                  size="small"
                >
                  {{ tag }}
                </el-tag>
              </div>

              <!-- 文章标题 -->
              <h2 class="article-title">
                <router-link :to="`/articles/${article.id}`">
                  {{ article.title }}
                </router-link>
              </h2>

              <!-- 文章摘要 -->
              <p class="article-summary">{{ article.summary }}</p>

              <!-- 文章元信息 -->
              <div class="article-meta">
                <div class="author-info">
                  <el-avatar
                    :src="article.author.avatar"
                    :size="32"
                    class="author-avatar"
                  >
                    {{ article.author.nickname?.[0] || article.author.username?.[0] }}
                  </el-avatar>
                  <div class="author-details">
                    <span class="author-name">
                      {{ article.author.nickname || article.author.username }}
                    </span>
                    <span class="publish-date">{{ formatDate(article.publishedAt) }}</span>
                  </div>
                </div>

                <div class="article-stats">
                  <span class="stat-item">
                    <el-icon><View /></el-icon>
                    {{ formatNumber(article.viewCount) }}
                  </span>
                  <span class="stat-item">
                    <el-icon><Star /></el-icon>
                    {{ formatNumber(article.likeCount) }}
                  </span>
                  <span class="stat-item">
                    <el-icon><ChatDotRound /></el-icon>
                    {{ formatNumber(article.commentCount) }}
                  </span>
                  <span class="reading-time">
                    <el-icon><Clock /></el-icon>
                    {{ article.readingTime }} 分钟阅读
                  </span>
                </div>
              </div>
            </div>

            <!-- 文章封面 -->
            <div v-if="article.coverImage" class="article-cover">
              <img :src="article.coverImage" :alt="article.title" />
            </div>
          </article>
        </div>

        <div v-else class="empty-state">
          <el-empty description="暂无文章" />
        </div>

        <!-- 分页 -->
        <div v-if="articles.length > 0" class="pagination-section">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :total="total"
            :page-sizes="[10, 20, 50]"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handlePageChange"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { Search, View, Star, ChatDotRound, Clock } from '@element-plus/icons-vue'
import { debounce } from 'lodash-es'
import dayjs from 'dayjs'

interface Article {
  id: number
  title: string
  summary: string
  coverImage?: string
  author: {
    id: number
    username: string
    nickname?: string
    avatar?: string
  }
  tags: string[]
  viewCount: number
  likeCount: number
  commentCount: number
  readingTime: number
  publishedAt: string
}

const router = useRouter()
const route = useRoute()

// 响应式数据
const loading = ref(true)
const articles = ref<Article[]>([])
const searchQuery = ref('')
const selectedCategory = ref('')
const sortBy = ref('latest')
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)

// 分类选项
const categories = [
  { label: '前端开发', value: 'frontend' },
  { label: '后端开发', value: 'backend' },
  { label: '移动开发', value: 'mobile' },
  { label: '人工智能', value: 'ai' },
  { label: '数据科学', value: 'data' },
  { label: '云计算', value: 'cloud' },
  { label: '架构设计', value: 'architecture' },
  { label: '开发工具', value: 'tools' },
]

// 格式化日期
const formatDate = (date: string) => {
  return dayjs(date).format('YYYY年MM月DD日')
}

// 格式化数字
const formatNumber = (num: number) => {
  if (num < 1000) return num.toString()
  if (num < 10000) return `${(num / 1000).toFixed(1)}k`
  return `${Math.floor(num / 1000)}k`
}

// 防抖搜索
const debouncedSearch = debounce(() => {
  currentPage.value = 1
  fetchArticles()
}, 300)

// 处理搜索
const handleSearch = () => {
  debouncedSearch()
}

// 处理筛选
const handleFilter = () => {
  currentPage.value = 1
  fetchArticles()
}

// 处理排序
const handleSort = () => {
  currentPage.value = 1
  fetchArticles()
}

// 处理分页大小变化
const handleSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1
  fetchArticles()
}

// 处理页码变化
const handlePageChange = (page: number) => {
  currentPage.value = page
  fetchArticles()
}

// 获取文章列表
const fetchArticles = async () => {
  try {
    loading.value = true
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 800))
    
    // 模拟数据
    const mockArticles: Article[] = Array.from({ length: pageSize.value }, (_, index) => ({
      id: (currentPage.value - 1) * pageSize.value + index + 1,
      title: `技术文章标题 ${(currentPage.value - 1) * pageSize.value + index + 1}`,
      summary: '这是一篇关于现代Web开发技术的深度文章，探讨了最新的技术趋势和最佳实践...',
      coverImage: Math.random() > 0.5 ? `https://picsum.photos/300/200?random=${index}` : undefined,
      author: {
        id: index + 1,
        username: `author_${index + 1}`,
        nickname: `作者 ${index + 1}`,
        avatar: `https://avatars.githubusercontent.com/u/${index + 1}?v=4`,
      },
      tags: ['React', 'TypeScript', '前端开发'].slice(0, Math.floor(Math.random() * 3) + 1),
      viewCount: Math.floor(Math.random() * 10000) + 100,
      likeCount: Math.floor(Math.random() * 1000) + 10,
      commentCount: Math.floor(Math.random() * 100) + 1,
      readingTime: Math.floor(Math.random() * 15) + 3,
      publishedAt: dayjs().subtract(Math.floor(Math.random() * 30), 'day').toISOString(),
    }))
    
    articles.value = mockArticles
    total.value = 500 // 模拟总数
    
  } catch (error) {
    console.error('获取文章列表失败:', error)
  } finally {
    loading.value = false
  }
}

// 初始化
onMounted(() => {
  // 从URL参数获取初始值
  const query = route.query
  if (query.q) searchQuery.value = query.q as string
  if (query.category) selectedCategory.value = query.category as string
  if (query.sort) sortBy.value = query.sort as string
  if (query.page) currentPage.value = Number(query.page)
  
  fetchArticles()
  
  // 设置页面标题
  document.title = '技术文章 - Ideary'
})
</script>

<style lang="scss" scoped>
.articles-page {
  min-height: 100vh;
  background: var(--el-bg-color-page);
}

.page-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem 1rem;
}

.page-header {
  text-align: center;
  margin-bottom: 3rem;
  
  .page-title {
    font-size: 2.5rem;
    font-weight: bold;
    color: var(--el-text-color-primary);
    margin-bottom: 1rem;
    
    @media (max-width: 768px) {
      font-size: 2rem;
    }
  }
  
  .page-subtitle {
    font-size: 1.125rem;
    color: var(--el-text-color-regular);
  }
}

.filters-section {
  margin-bottom: 2rem;
  
  .filters-container {
    display: flex;
    gap: 1rem;
    align-items: center;
    
    @media (max-width: 768px) {
      flex-direction: column;
      align-items: stretch;
    }
    
    .search-box {
      flex: 1;
      max-width: 400px;
      
      @media (max-width: 768px) {
        max-width: none;
      }
    }
    
    .filter-options {
      display: flex;
      gap: 1rem;
      
      @media (max-width: 768px) {
        flex-direction: column;
      }
      
      .el-select {
        width: 150px;
        
        @media (max-width: 768px) {
          width: 100%;
        }
      }
    }
  }
}

.articles-section {
  .articles-list {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    margin-bottom: 3rem;
  }
  
  .article-item {
    background: var(--el-bg-color);
    border-radius: 1rem;
    padding: 1.5rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    display: flex;
    gap: 1.5rem;
    
    &:hover {
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
      transform: translateY(-2px);
    }
    
    @media (max-width: 768px) {
      flex-direction: column;
      gap: 1rem;
    }
    
    .article-content {
      flex: 1;
      min-width: 0;
    }
    
    .article-tags {
      display: flex;
      gap: 0.5rem;
      margin-bottom: 1rem;
      flex-wrap: wrap;
    }
    
    .article-title {
      font-size: 1.5rem;
      font-weight: 600;
      margin-bottom: 1rem;
      line-height: 1.4;
      
      a {
        color: var(--el-text-color-primary);
        text-decoration: none;
        
        &:hover {
          color: var(--el-color-primary);
        }
      }
    }
    
    .article-summary {
      color: var(--el-text-color-regular);
      line-height: 1.6;
      margin-bottom: 1.5rem;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }
    
    .article-meta {
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      @media (max-width: 640px) {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
      }
      
      .author-info {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        
        .author-details {
          display: flex;
          flex-direction: column;
          
          .author-name {
            font-weight: 500;
            color: var(--el-text-color-primary);
            font-size: 0.875rem;
          }
          
          .publish-date {
            font-size: 0.75rem;
            color: var(--el-text-color-placeholder);
          }
        }
      }
      
      .article-stats {
        display: flex;
        gap: 1rem;
        align-items: center;
        
        .stat-item,
        .reading-time {
          display: flex;
          align-items: center;
          gap: 0.25rem;
          color: var(--el-text-color-placeholder);
          font-size: 0.875rem;
          
          .el-icon {
            font-size: 1rem;
          }
        }
      }
    }
    
    .article-cover {
      width: 200px;
      height: 120px;
      flex-shrink: 0;
      border-radius: 0.5rem;
      overflow: hidden;
      
      @media (max-width: 768px) {
        width: 100%;
        height: 200px;
      }
      
      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.3s ease;
      }
      
      &:hover img {
        transform: scale(1.05);
      }
    }
  }
  
  .empty-state {
    text-align: center;
    padding: 3rem 0;
  }
  
  .pagination-section {
    display: flex;
    justify-content: center;
    margin-top: 2rem;
  }
}
</style>
