<template>
  <div class="author-profile-page">
    <div class="page-container">
      <el-skeleton v-if="loading" :rows="8" animated />
      
      <div v-else-if="author" class="author-content">
        <!-- 作者信息卡片 -->
        <div class="author-card">
          <div class="author-header">
            <el-avatar :src="author.avatar" :size="120" class="author-avatar">
              {{ author.nickname?.[0] || author.username?.[0] }}
            </el-avatar>
            
            <div class="author-info">
              <h1 class="author-name">{{ author.nickname || author.username }}</h1>
              <p class="author-username">@{{ author.username }}</p>
              <p v-if="author.bio" class="author-bio">{{ author.bio }}</p>
              
              <div class="author-stats">
                <div class="stat-item">
                  <span class="stat-value">{{ formatNumber(author.articlesCount) }}</span>
                  <span class="stat-label">文章</span>
                </div>
                <div class="stat-item">
                  <span class="stat-value">{{ formatNumber(author.followersCount) }}</span>
                  <span class="stat-label">粉丝</span>
                </div>
                <div class="stat-item">
                  <span class="stat-value">{{ formatNumber(author.followingCount) }}</span>
                  <span class="stat-label">关注</span>
                </div>
                <div class="stat-item">
                  <span class="stat-value">{{ formatNumber(author.totalLikes) }}</span>
                  <span class="stat-label">获赞</span>
                </div>
              </div>
            </div>
            
            <div class="author-actions">
              <el-button 
                :type="author.isFollowing ? 'default' : 'primary'"
                @click="toggleFollow"
                :loading="followLoading"
              >
                {{ author.isFollowing ? '已关注' : '关注' }}
              </el-button>
            </div>
          </div>
        </div>

        <!-- 作者文章 -->
        <div class="articles-section">
          <h2>发布的文章</h2>
          
          <div v-if="articles.length > 0" class="articles-list">
            <div
              v-for="article in articles"
              :key="article.id"
              class="article-item"
            >
              <div class="article-content">
                <div class="article-tags">
                  <el-tag
                    v-for="tag in article.tags.slice(0, 3)"
                    :key="tag"
                    type="primary"
                    effect="plain"
                    size="small"
                  >
                    {{ tag }}
                  </el-tag>
                </div>
                
                <h3 class="article-title">
                  <router-link :to="`/articles/${article.id}`">
                    {{ article.title }}
                  </router-link>
                </h3>
                
                <p class="article-summary">{{ article.summary }}</p>
                
                <div class="article-meta">
                  <span class="publish-date">{{ formatDate(article.publishedAt) }}</span>
                  <div class="article-stats">
                    <span><el-icon><View /></el-icon> {{ formatNumber(article.viewCount) }}</span>
                    <span><el-icon><Star /></el-icon> {{ formatNumber(article.likeCount) }}</span>
                    <span><el-icon><ChatDotRound /></el-icon> {{ formatNumber(article.commentCount) }}</span>
                  </div>
                </div>
              </div>
              
              <div v-if="article.coverImage" class="article-cover">
                <img :src="article.coverImage" :alt="article.title" />
              </div>
            </div>
          </div>
          
          <div v-else class="empty-articles">
            <el-empty description="暂无发布的文章" />
          </div>
        </div>
      </div>
      
      <div v-else class="not-found">
        <el-empty description="作者不存在" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { View, Star, ChatDotRound } from '@element-plus/icons-vue'
import dayjs from 'dayjs'

const route = useRoute()

const loading = ref(true)
const followLoading = ref(false)
const author = ref<any>(null)
const articles = ref<any[]>([])

const formatDate = (date: string) => {
  return dayjs(date).format('YYYY年MM月DD日')
}

const formatNumber = (num: number) => {
  if (num < 1000) return num.toString()
  if (num < 10000) return `${(num / 1000).toFixed(1)}k`
  return `${Math.floor(num / 1000)}k`
}

const toggleFollow = async () => {
  try {
    followLoading.value = true
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500))
    
    author.value.isFollowing = !author.value.isFollowing
    author.value.followersCount += author.value.isFollowing ? 1 : -1
    
    ElMessage.success(author.value.isFollowing ? '关注成功' : '取消关注成功')
  } catch (error) {
    ElMessage.error('操作失败，请重试')
  } finally {
    followLoading.value = false
  }
}

const fetchAuthorProfile = async () => {
  try {
    loading.value = true
    const username = route.params.username as string
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 800))
    
    // 模拟数据
    author.value = {
      id: 1,
      username: username,
      nickname: 'React 大师',
      avatar: 'https://avatars.githubusercontent.com/u/1?v=4',
      bio: '专注于 React 生态系统开发，拥有 8 年前端开发经验，热爱分享技术心得。',
      articlesCount: 156,
      followersCount: 12500,
      followingCount: 186,
      totalLikes: 45600,
      isFollowing: false
    }
    
    articles.value = [
      {
        id: 1,
        title: 'React 18 新特性深度解析',
        summary: '深入探讨 React 18 带来的革命性变化，包括并发渲染、自动批处理等核心特性...',
        coverImage: 'https://images.unsplash.com/photo-1633356122544-f134324a6cee?w=300&h=200&fit=crop',
        tags: ['React', 'JavaScript', '前端开发'],
        viewCount: 12500,
        likeCount: 856,
        commentCount: 124,
        publishedAt: '2024-01-15T10:30:00Z'
      },
      {
        id: 2,
        title: 'React Hooks 最佳实践指南',
        summary: '全面介绍 React Hooks 的使用技巧和最佳实践，帮助开发者写出更优雅的代码...',
        tags: ['React', 'Hooks', 'JavaScript'],
        viewCount: 9800,
        likeCount: 642,
        commentCount: 89,
        publishedAt: '2024-01-12T14:20:00Z'
      }
    ]
  } catch (error) {
    console.error('获取作者资料失败:', error)
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  fetchAuthorProfile()
  document.title = `${author.value?.nickname || author.value?.username || '作者资料'} - Ideary`
})
</script>

<style lang="scss" scoped>
.author-profile-page {
  min-height: 100vh;
  background: var(--el-bg-color-page);
}

.page-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem 1rem;
}

.author-content {
  .author-card {
    background: var(--el-bg-color);
    border-radius: 1rem;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    
    .author-header {
      display: flex;
      gap: 2rem;
      align-items: flex-start;
      
      @media (max-width: 768px) {
        flex-direction: column;
        text-align: center;
      }
      
      .author-avatar {
        border: 4px solid var(--el-border-color-light);
        flex-shrink: 0;
      }
      
      .author-info {
        flex: 1;
        min-width: 0;
        
        .author-name {
          font-size: 2rem;
          font-weight: bold;
          color: var(--el-text-color-primary);
          margin-bottom: 0.5rem;
        }
        
        .author-username {
          color: var(--el-text-color-placeholder);
          font-size: 1.125rem;
          margin-bottom: 1rem;
        }
        
        .author-bio {
          color: var(--el-text-color-regular);
          line-height: 1.6;
          margin-bottom: 1.5rem;
        }
        
        .author-stats {
          display: flex;
          gap: 2rem;
          
          @media (max-width: 640px) {
            justify-content: center;
            gap: 1rem;
          }
          
          .stat-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            
            .stat-value {
              font-size: 1.5rem;
              font-weight: bold;
              color: var(--el-text-color-primary);
            }
            
            .stat-label {
              font-size: 0.875rem;
              color: var(--el-text-color-placeholder);
            }
          }
        }
      }
      
      .author-actions {
        flex-shrink: 0;
        
        @media (max-width: 768px) {
          align-self: center;
        }
      }
    }
  }
  
  .articles-section {
    background: var(--el-bg-color);
    border-radius: 1rem;
    padding: 2rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    
    h2 {
      font-size: 1.5rem;
      font-weight: 600;
      color: var(--el-text-color-primary);
      margin-bottom: 1.5rem;
    }
    
    .articles-list {
      display: flex;
      flex-direction: column;
      gap: 1.5rem;
    }
    
    .article-item {
      display: flex;
      gap: 1.5rem;
      padding: 1.5rem;
      border: 1px solid var(--el-border-color-light);
      border-radius: 0.75rem;
      transition: all 0.3s ease;
      
      &:hover {
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        transform: translateY(-2px);
      }
      
      @media (max-width: 768px) {
        flex-direction: column;
        gap: 1rem;
      }
      
      .article-content {
        flex: 1;
        min-width: 0;
        
        .article-tags {
          display: flex;
          gap: 0.5rem;
          margin-bottom: 0.75rem;
          flex-wrap: wrap;
        }
        
        .article-title {
          font-size: 1.25rem;
          font-weight: 600;
          margin-bottom: 0.75rem;
          line-height: 1.4;
          
          a {
            color: var(--el-text-color-primary);
            text-decoration: none;
            
            &:hover {
              color: var(--el-color-primary);
            }
          }
        }
        
        .article-summary {
          color: var(--el-text-color-regular);
          line-height: 1.6;
          margin-bottom: 1rem;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          overflow: hidden;
        }
        
        .article-meta {
          display: flex;
          justify-content: space-between;
          align-items: center;
          
          .publish-date {
            color: var(--el-text-color-placeholder);
            font-size: 0.875rem;
          }
          
          .article-stats {
            display: flex;
            gap: 1rem;
            color: var(--el-text-color-placeholder);
            font-size: 0.875rem;
            
            span {
              display: flex;
              align-items: center;
              gap: 0.25rem;
            }
          }
        }
      }
      
      .article-cover {
        width: 200px;
        height: 120px;
        flex-shrink: 0;
        border-radius: 0.5rem;
        overflow: hidden;
        
        @media (max-width: 768px) {
          width: 100%;
          height: 200px;
        }
        
        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }
    }
    
    .empty-articles {
      text-align: center;
      padding: 2rem 0;
    }
  }
}

.not-found {
  text-align: center;
  padding: 3rem 0;
}
</style>
