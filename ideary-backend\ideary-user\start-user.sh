#!/bin/bash

# Ideary用户服务启动脚本

set -e

echo "=========================================="
echo "  Ideary User Service 启动脚本"
echo "=========================================="

# 检查Java环境
if ! command -v java > /dev/null 2>&1; then
    echo "❌ Java未安装，请先安装JDK 17+"
    exit 1
fi

# 检查Java版本
JAVA_VERSION=$(java -version 2>&1 | awk -F '"' '/version/ {print $2}' | cut -d'.' -f1)
if [ "$JAVA_VERSION" -lt 17 ]; then
    echo "❌ Java版本过低，需要JDK 17+，当前版本: $JAVA_VERSION"
    exit 1
fi

# 检查Maven
if ! command -v mvn > /dev/null 2>&1; then
    echo "❌ Maven未安装，请先安装Maven 3.8+"
    exit 1
fi

# 检查基础设施服务
echo "🔍 检查基础设施服务..."

# 检查MySQL
if ! mysql -h localhost -P 3306 -u ideary -pideary123456 -e "SELECT 1" > /dev/null 2>&1; then
    echo "❌ MySQL服务未启动或连接失败，请先启动基础设施服务"
    echo "   运行命令: cd ../../infrastructure && ./start-infrastructure.sh"
    exit 1
fi

# 检查Nacos
if ! curl -s http://localhost:8848/nacos/v1/ns/operator/metrics > /dev/null; then
    echo "❌ Nacos服务未启动，请先启动基础设施服务"
    exit 1
fi

# 检查Redis
if ! redis-cli -h localhost -p 6379 ping > /dev/null 2>&1; then
    echo "❌ Redis服务未启动，请先启动基础设施服务"
    exit 1
fi

echo "✅ 基础设施服务检查通过"

# 清理之前的构建
echo "🧹 清理之前的构建..."
mvn clean

# 编译项目
echo "🔨 编译项目..."
mvn compile

# 运行测试
echo "🧪 运行测试..."
mvn test

# 打包项目
echo "📦 打包项目..."
mvn package -DskipTests

# 检查JAR文件
JAR_FILE="target/ideary-user-1.0.0.jar"
if [ ! -f "$JAR_FILE" ]; then
    echo "❌ JAR文件不存在: $JAR_FILE"
    exit 1
fi

# 创建日志目录
mkdir -p logs

# 设置JVM参数
JVM_OPTS="-Xms512m -Xmx1024m"
JVM_OPTS="$JVM_OPTS -XX:+UseG1GC"
JVM_OPTS="$JVM_OPTS -XX:MaxGCPauseMillis=200"
JVM_OPTS="$JVM_OPTS -XX:+PrintGCDetails"
JVM_OPTS="$JVM_OPTS -XX:+PrintGCTimeStamps"
JVM_OPTS="$JVM_OPTS -Xloggc:logs/gc.log"
JVM_OPTS="$JVM_OPTS -XX:+UseGCLogFileRotation"
JVM_OPTS="$JVM_OPTS -XX:NumberOfGCLogFiles=5"
JVM_OPTS="$JVM_OPTS -XX:GCLogFileSize=10M"

# 设置应用参数
APP_OPTS="--spring.profiles.active=dev"
APP_OPTS="$APP_OPTS --server.port=9081"
APP_OPTS="$APP_OPTS --logging.file.name=logs/ideary-user.log"

echo "🚀 启动用户服务..."
echo "   JAR文件: $JAR_FILE"
echo "   JVM参数: $JVM_OPTS"
echo "   应用参数: $APP_OPTS"
echo ""

# 启动应用
java $JVM_OPTS -jar $JAR_FILE $APP_OPTS

echo ""
echo "🎉 用户服务启动完成！"
echo ""
echo "📋 访问地址："
echo "   服务地址: http://localhost:9081"
echo "   健康检查: http://localhost:9081/actuator/health"
echo "   API文档: http://localhost:9081/doc.html"
echo ""
echo "🔑 主要API接口："
echo "   POST /api/v1/auth/register    - 用户注册"
echo "   POST /api/v1/auth/login       - 用户登录"
echo "   POST /api/v1/auth/logout      - 用户登出"
echo "   GET  /api/v1/users/profile    - 获取用户资料"
echo "   PUT  /api/v1/users/profile    - 更新用户资料"
echo "   POST /api/v1/users/{id}/follow - 关注用户"
echo ""
echo "📝 日志文件："
echo "   应用日志: logs/ideary-user.log"
echo "   GC日志: logs/gc.log"
echo ""
