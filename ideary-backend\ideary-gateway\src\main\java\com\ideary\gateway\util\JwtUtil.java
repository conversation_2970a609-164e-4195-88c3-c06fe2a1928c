package com.ideary.gateway.util;

import com.auth0.jwt.JWT;
import com.auth0.jwt.JWTVerifier;
import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.exceptions.JWTVerificationException;
import com.auth0.jwt.interfaces.DecodedJWT;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

/**
 * JWT工具类
 * 
 * 功能说明：
 * 1. 生成JWT Token
 * 2. 验证JWT Token
 * 3. 解析JWT Token
 * 4. 刷新JWT Token
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 2024-01-01
 */
@Component
public class JwtUtil {

    @Value("${jwt.secret}")
    private String secret;

    @Value("${jwt.expiration}")
    private Long expiration;

    @Value("${jwt.refresh-expiration}")
    private Long refreshExpiration;

    @Value("${jwt.header}")
    private String header;

    @Value("${jwt.prefix}")
    private String prefix;

    /**
     * 生成访问Token
     *
     * @param userId 用户ID
     * @param username 用户名
     * @param roles 用户角色列表
     * @return JWT Token
     */
    public String generateAccessToken(Long userId, String username, List<String> roles) {
        Date now = new Date();
        Date expiryDate = new Date(now.getTime() + expiration);

        return JWT.create()
                .withSubject(username)
                .withClaim("userId", userId)
                .withClaim("username", username)
                .withClaim("roles", roles)
                .withClaim("type", "access")
                .withIssuedAt(now)
                .withExpiresAt(expiryDate)
                .withIssuer("ideary-gateway")
                .sign(Algorithm.HMAC256(secret));
    }

    /**
     * 生成刷新Token
     *
     * @param userId 用户ID
     * @param username 用户名
     * @return 刷新Token
     */
    public String generateRefreshToken(Long userId, String username) {
        Date now = new Date();
        Date expiryDate = new Date(now.getTime() + refreshExpiration);

        return JWT.create()
                .withSubject(username)
                .withClaim("userId", userId)
                .withClaim("username", username)
                .withClaim("type", "refresh")
                .withIssuedAt(now)
                .withExpiresAt(expiryDate)
                .withIssuer("ideary-gateway")
                .sign(Algorithm.HMAC256(secret));
    }

    /**
     * 验证Token
     *
     * @param token JWT Token
     * @return 是否有效
     */
    public boolean validateToken(String token) {
        try {
            Algorithm algorithm = Algorithm.HMAC256(secret);
            JWTVerifier verifier = JWT.require(algorithm)
                    .withIssuer("ideary-gateway")
                    .build();
            verifier.verify(token);
            return true;
        } catch (JWTVerificationException e) {
            return false;
        }
    }

    /**
     * 解析Token
     *
     * @param token JWT Token
     * @return 解码后的JWT
     */
    public DecodedJWT parseToken(String token) {
        try {
            Algorithm algorithm = Algorithm.HMAC256(secret);
            JWTVerifier verifier = JWT.require(algorithm)
                    .withIssuer("ideary-gateway")
                    .build();
            return verifier.verify(token);
        } catch (JWTVerificationException e) {
            throw new RuntimeException("Token解析失败", e);
        }
    }

    /**
     * 从Token中获取用户ID
     *
     * @param token JWT Token
     * @return 用户ID
     */
    public Long getUserIdFromToken(String token) {
        DecodedJWT decodedJWT = parseToken(token);
        return decodedJWT.getClaim("userId").asLong();
    }

    /**
     * 从Token中获取用户名
     *
     * @param token JWT Token
     * @return 用户名
     */
    public String getUsernameFromToken(String token) {
        DecodedJWT decodedJWT = parseToken(token);
        return decodedJWT.getClaim("username").asString();
    }

    /**
     * 从Token中获取用户角色
     *
     * @param token JWT Token
     * @return 用户角色列表
     */
    public List<String> getRolesFromToken(String token) {
        DecodedJWT decodedJWT = parseToken(token);
        return decodedJWT.getClaim("roles").asList(String.class);
    }

    /**
     * 从Token中获取Token类型
     *
     * @param token JWT Token
     * @return Token类型
     */
    public String getTokenType(String token) {
        DecodedJWT decodedJWT = parseToken(token);
        return decodedJWT.getClaim("type").asString();
    }

    /**
     * 检查Token是否过期
     *
     * @param token JWT Token
     * @return 是否过期
     */
    public boolean isTokenExpired(String token) {
        try {
            DecodedJWT decodedJWT = parseToken(token);
            return decodedJWT.getExpiresAt().before(new Date());
        } catch (Exception e) {
            return true;
        }
    }

    /**
     * 从请求头中提取Token
     *
     * @param authHeader 认证头
     * @return JWT Token
     */
    public String extractToken(String authHeader) {
        if (authHeader != null && authHeader.startsWith(prefix)) {
            return authHeader.substring(prefix.length());
        }
        return null;
    }

    /**
     * 刷新Token
     *
     * @param refreshToken 刷新Token
     * @return 新的访问Token
     */
    public String refreshAccessToken(String refreshToken) {
        if (!validateToken(refreshToken)) {
            throw new RuntimeException("刷新Token无效");
        }

        DecodedJWT decodedJWT = parseToken(refreshToken);
        String tokenType = decodedJWT.getClaim("type").asString();
        
        if (!"refresh".equals(tokenType)) {
            throw new RuntimeException("Token类型错误");
        }

        Long userId = decodedJWT.getClaim("userId").asLong();
        String username = decodedJWT.getClaim("username").asString();
        
        // 注意：这里需要重新获取用户的最新角色信息
        // 在实际应用中，应该调用用户服务获取最新的角色信息
        // 这里暂时返回空列表，实际使用时需要完善
        List<String> roles = List.of(); // TODO: 从用户服务获取最新角色
        
        return generateAccessToken(userId, username, roles);
    }

    /**
     * 获取Token剩余有效时间（秒）
     *
     * @param token JWT Token
     * @return 剩余有效时间
     */
    public long getTokenRemainingTime(String token) {
        try {
            DecodedJWT decodedJWT = parseToken(token);
            Date expiresAt = decodedJWT.getExpiresAt();
            Date now = new Date();
            return (expiresAt.getTime() - now.getTime()) / 1000;
        } catch (Exception e) {
            return 0;
        }
    }

    // Getter方法
    public String getHeader() {
        return header;
    }

    public String getPrefix() {
        return prefix;
    }

    public Long getExpiration() {
        return expiration;
    }

    public Long getRefreshExpiration() {
        return refreshExpiration;
    }
}
