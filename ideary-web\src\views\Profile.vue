<template>
  <div class="profile-page">
    <div class="page-container">
      <!-- 用户信息卡片 -->
      <div class="profile-card">
        <div class="profile-header">
          <div class="avatar-section">
            <el-avatar :src="userInfo?.avatar" :size="120" class="user-avatar">
              {{ userInfo?.nickname?.[0] || userInfo?.username?.[0] }}
            </el-avatar>
            <el-button type="primary" size="small" @click="showAvatarUpload = true">
              更换头像
            </el-button>
          </div>
          
          <div class="user-info">
            <h1 class="user-name">{{ userInfo?.nickname || userInfo?.username }}</h1>
            <p class="user-username">@{{ userInfo?.username }}</p>
            <p v-if="userInfo?.bio" class="user-bio">{{ userInfo.bio }}</p>
            
            <div class="user-stats">
              <div class="stat-item">
                <span class="stat-value">{{ formatNumber(stats.articlesCount) }}</span>
                <span class="stat-label">文章</span>
              </div>
              <div class="stat-item">
                <span class="stat-value">{{ formatNumber(stats.followersCount) }}</span>
                <span class="stat-label">粉丝</span>
              </div>
              <div class="stat-item">
                <span class="stat-value">{{ formatNumber(stats.followingCount) }}</span>
                <span class="stat-label">关注</span>
              </div>
              <div class="stat-item">
                <span class="stat-value">{{ formatNumber(stats.totalLikes) }}</span>
                <span class="stat-label">获赞</span>
              </div>
            </div>
          </div>
          
          <div class="profile-actions">
            <el-button @click="$router.push('/settings')">
              <el-icon><Setting /></el-icon>
              设置
            </el-button>
            <el-button type="primary" @click="$router.push('/write')">
              <el-icon><Edit /></el-icon>
              写文章
            </el-button>
          </div>
        </div>
      </div>

      <!-- 标签页 -->
      <el-tabs v-model="activeTab" class="profile-tabs">
        <el-tab-pane label="我的文章" name="articles">
          <UserArticles :user-id="userStore.userInfo?.id" />
        </el-tab-pane>
        
        <el-tab-pane label="草稿箱" name="drafts">
          <UserDrafts :user-id="userStore.userInfo?.id" />
        </el-tab-pane>
        
        <el-tab-pane label="收藏夹" name="favorites">
          <UserFavorites :user-id="userStore.userInfo?.id" />
        </el-tab-pane>
        
        <el-tab-pane label="关注" name="following">
          <UserFollowing :user-id="userStore.userInfo?.id" />
        </el-tab-pane>
        
        <el-tab-pane label="粉丝" name="followers">
          <UserFollowers :user-id="userStore.userInfo?.id" />
        </el-tab-pane>
      </el-tabs>
    </div>

    <!-- 头像上传对话框 -->
    <el-dialog v-model="showAvatarUpload" title="更换头像" width="400px">
      <div class="avatar-upload">
        <el-upload
          class="avatar-uploader"
          action="#"
          :show-file-list="false"
          :before-upload="beforeAvatarUpload"
          :http-request="handleAvatarUpload"
        >
          <img v-if="newAvatar" :src="newAvatar" class="avatar-preview" />
          <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>
        </el-upload>
        <div class="upload-tips">
          <p>支持 JPG、PNG 格式，文件大小不超过 2MB</p>
          <p>建议上传正方形图片，获得最佳显示效果</p>
        </div>
      </div>
      
      <template #footer>
        <el-button @click="showAvatarUpload = false">取消</el-button>
        <el-button type="primary" @click="confirmAvatarUpload" :loading="uploadLoading">
          确认
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Setting, Edit, Plus } from '@element-plus/icons-vue'
import { useUserStore } from '@/stores/user'
import UserArticles from '@/components/profile/UserArticles.vue'
import UserDrafts from '@/components/profile/UserDrafts.vue'
import UserFavorites from '@/components/profile/UserFavorites.vue'
import UserFollowing from '@/components/profile/UserFollowing.vue'
import UserFollowers from '@/components/profile/UserFollowers.vue'

const userStore = useUserStore()

// 响应式数据
const activeTab = ref('articles')
const showAvatarUpload = ref(false)
const newAvatar = ref('')
const uploadLoading = ref(false)

// 用户统计数据
const stats = ref({
  articlesCount: 0,
  followersCount: 0,
  followingCount: 0,
  totalLikes: 0,
})

// 计算属性
const userInfo = computed(() => userStore.userInfo)

// 格式化数字
const formatNumber = (num: number) => {
  if (num < 1000) return num.toString()
  if (num < 10000) return `${(num / 1000).toFixed(1)}k`
  return `${Math.floor(num / 1000)}k`
}

// 头像上传前检查
const beforeAvatarUpload = (file: File) => {
  const isJPG = file.type === 'image/jpeg' || file.type === 'image/png'
  const isLt2M = file.size / 1024 / 1024 < 2

  if (!isJPG) {
    ElMessage.error('头像只能是 JPG 或 PNG 格式!')
    return false
  }
  if (!isLt2M) {
    ElMessage.error('头像大小不能超过 2MB!')
    return false
  }
  return true
}

// 处理头像上传
const handleAvatarUpload = (options: any) => {
  const file = options.file
  const reader = new FileReader()
  
  reader.onload = (e) => {
    newAvatar.value = e.target?.result as string
  }
  
  reader.readAsDataURL(file)
}

// 确认头像上传
const confirmAvatarUpload = async () => {
  if (!newAvatar.value) {
    ElMessage.warning('请先选择头像')
    return
  }

  try {
    uploadLoading.value = true
    
    // 模拟上传API
    await new Promise(resolve => setTimeout(resolve, 1500))
    
    // 更新用户头像
    userStore.updateUser({ avatar: newAvatar.value })
    
    ElMessage.success('头像更新成功')
    showAvatarUpload.value = false
    newAvatar.value = ''
    
  } catch (error) {
    console.error('头像上传失败:', error)
    ElMessage.error('头像上传失败，请重试')
  } finally {
    uploadLoading.value = false
  }
}

// 获取用户统计数据
const fetchUserStats = async () => {
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500))
    
    // 模拟数据
    stats.value = {
      articlesCount: 42,
      followersCount: 1250,
      followingCount: 186,
      totalLikes: 8900,
    }
  } catch (error) {
    console.error('获取用户统计失败:', error)
  }
}

// 初始化
onMounted(() => {
  fetchUserStats()
  document.title = '个人资料 - Ideary'
})
</script>

<style lang="scss" scoped>
.profile-page {
  min-height: 100vh;
  background: var(--el-bg-color-page);
}

.page-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem 1rem;
}

.profile-card {
  background: var(--el-bg-color);
  border-radius: 1rem;
  padding: 2rem;
  margin-bottom: 2rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  
  .profile-header {
    display: flex;
    gap: 2rem;
    align-items: flex-start;
    
    @media (max-width: 768px) {
      flex-direction: column;
      text-align: center;
    }
    
    .avatar-section {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 1rem;
      
      .user-avatar {
        border: 4px solid var(--el-border-color-light);
      }
    }
    
    .user-info {
      flex: 1;
      min-width: 0;
      
      .user-name {
        font-size: 2rem;
        font-weight: bold;
        color: var(--el-text-color-primary);
        margin-bottom: 0.5rem;
      }
      
      .user-username {
        color: var(--el-text-color-placeholder);
        font-size: 1.125rem;
        margin-bottom: 1rem;
      }
      
      .user-bio {
        color: var(--el-text-color-regular);
        line-height: 1.6;
        margin-bottom: 1.5rem;
      }
      
      .user-stats {
        display: flex;
        gap: 2rem;
        
        @media (max-width: 640px) {
          justify-content: center;
          gap: 1rem;
        }
        
        .stat-item {
          display: flex;
          flex-direction: column;
          align-items: center;
          
          .stat-value {
            font-size: 1.5rem;
            font-weight: bold;
            color: var(--el-text-color-primary);
          }
          
          .stat-label {
            font-size: 0.875rem;
            color: var(--el-text-color-placeholder);
          }
        }
      }
    }
    
    .profile-actions {
      display: flex;
      flex-direction: column;
      gap: 0.75rem;
      
      @media (max-width: 768px) {
        flex-direction: row;
        justify-content: center;
      }
    }
  }
}

.profile-tabs {
  background: var(--el-bg-color);
  border-radius: 1rem;
  padding: 1rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  
  :deep(.el-tabs__header) {
    margin-bottom: 1rem;
  }
  
  :deep(.el-tabs__content) {
    min-height: 400px;
  }
}

.avatar-upload {
  text-align: center;
  
  .avatar-uploader {
    :deep(.el-upload) {
      border: 1px dashed var(--el-border-color);
      border-radius: 6px;
      cursor: pointer;
      position: relative;
      overflow: hidden;
      transition: var(--el-transition-duration-fast);
      
      &:hover {
        border-color: var(--el-color-primary);
      }
    }
    
    .avatar-uploader-icon {
      font-size: 28px;
      color: #8c939d;
      width: 178px;
      height: 178px;
      text-align: center;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    
    .avatar-preview {
      width: 178px;
      height: 178px;
      display: block;
      object-fit: cover;
    }
  }
  
  .upload-tips {
    margin-top: 1rem;
    
    p {
      color: var(--el-text-color-placeholder);
      font-size: 0.875rem;
      margin: 0.25rem 0;
    }
  }
}
</style>
