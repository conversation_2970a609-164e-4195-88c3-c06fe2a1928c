package com.ideary.user.controller;

import com.ideary.user.dto.*;
import com.ideary.user.service.UserService;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 用户认证控制器
 * 
 * 功能说明：
 * 1. 用户注册
 * 2. 用户登录
 * 3. 用户登出
 * 4. Token刷新
 * 5. 密码重置
 * 6. 验证码发送
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 2024-01-01
 */
@RestController
@RequestMapping("/api/v1/auth")
@CrossOrigin(origins = "*")
public class AuthController {

    private static final Logger logger = LoggerFactory.getLogger(AuthController.class);

    @Autowired
    private UserService userService;

    /**
     * 用户注册
     */
    @PostMapping("/register")
    public ResponseEntity<Map<String, Object>> register(@Valid @RequestBody UserRegisterRequest request) {
        logger.info("用户注册请求，用户名: {}, 邮箱: {}", request.getUsername(), request.getEmail());

        try {
            UserLoginResponse response = userService.register(request);
            
            Map<String, Object> result = new HashMap<>();
            result.put("code", 200);
            result.put("message", "注册成功");
            result.put("data", response);
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            logger.error("用户注册失败: {}", e.getMessage());
            
            Map<String, Object> result = new HashMap<>();
            result.put("code", 400);
            result.put("message", e.getMessage());
            result.put("data", null);
            
            return ResponseEntity.badRequest().body(result);
        }
    }

    /**
     * 用户登录
     */
    @PostMapping("/login")
    public ResponseEntity<Map<String, Object>> login(@Valid @RequestBody UserLoginRequest request) {
        logger.info("用户登录请求，账号: {}", request.getAccount());

        try {
            UserLoginResponse response = userService.login(request);
            
            Map<String, Object> result = new HashMap<>();
            result.put("code", 200);
            result.put("message", "登录成功");
            result.put("data", response);
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            logger.error("用户登录失败: {}", e.getMessage());
            
            Map<String, Object> result = new HashMap<>();
            result.put("code", 401);
            result.put("message", e.getMessage());
            result.put("data", null);
            
            return ResponseEntity.status(401).body(result);
        }
    }

    /**
     * 用户登出
     */
    @PostMapping("/logout")
    public ResponseEntity<Map<String, Object>> logout(@RequestHeader("X-User-Id") Long userId,
                                                      @RequestHeader("Authorization") String authHeader) {
        logger.info("用户登出请求，用户ID: {}", userId);

        try {
            // 提取Token
            String token = authHeader.replace("Bearer ", "");
            userService.logout(userId, token);
            
            Map<String, Object> result = new HashMap<>();
            result.put("code", 200);
            result.put("message", "登出成功");
            result.put("data", null);
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            logger.error("用户登出失败: {}", e.getMessage());
            
            Map<String, Object> result = new HashMap<>();
            result.put("code", 400);
            result.put("message", e.getMessage());
            result.put("data", null);
            
            return ResponseEntity.badRequest().body(result);
        }
    }

    /**
     * 刷新Token
     */
    @PostMapping("/refresh")
    public ResponseEntity<Map<String, Object>> refreshToken(@RequestBody Map<String, String> request) {
        String refreshToken = request.get("refreshToken");
        logger.info("Token刷新请求");

        try {
            UserLoginResponse response = userService.refreshToken(refreshToken);
            
            Map<String, Object> result = new HashMap<>();
            result.put("code", 200);
            result.put("message", "Token刷新成功");
            result.put("data", response);
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            logger.error("Token刷新失败: {}", e.getMessage());
            
            Map<String, Object> result = new HashMap<>();
            result.put("code", 401);
            result.put("message", e.getMessage());
            result.put("data", null);
            
            return ResponseEntity.status(401).body(result);
        }
    }

    /**
     * 发送邮箱验证码
     */
    @PostMapping("/send-email-code")
    public ResponseEntity<Map<String, Object>> sendEmailCode(@RequestBody Map<String, String> request) {
        String email = request.get("email");
        String type = request.getOrDefault("type", "register");
        
        logger.info("发送邮箱验证码请求，邮箱: {}, 类型: {}", email, type);

        try {
            userService.sendEmailVerificationCode(email, type);
            
            Map<String, Object> result = new HashMap<>();
            result.put("code", 200);
            result.put("message", "验证码发送成功");
            result.put("data", null);
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            logger.error("发送邮箱验证码失败: {}", e.getMessage());
            
            Map<String, Object> result = new HashMap<>();
            result.put("code", 400);
            result.put("message", e.getMessage());
            result.put("data", null);
            
            return ResponseEntity.badRequest().body(result);
        }
    }

    /**
     * 验证邮箱验证码
     */
    @PostMapping("/verify-email-code")
    public ResponseEntity<Map<String, Object>> verifyEmailCode(@RequestBody Map<String, String> request) {
        String email = request.get("email");
        String code = request.get("code");
        String type = request.getOrDefault("type", "register");
        
        logger.info("验证邮箱验证码请求，邮箱: {}, 类型: {}", email, type);

        try {
            boolean isValid = userService.verifyEmailCode(email, code, type);
            
            Map<String, Object> result = new HashMap<>();
            result.put("code", 200);
            result.put("message", isValid ? "验证码正确" : "验证码错误或已过期");
            result.put("data", Map.of("valid", isValid));
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            logger.error("验证邮箱验证码失败: {}", e.getMessage());
            
            Map<String, Object> result = new HashMap<>();
            result.put("code", 400);
            result.put("message", e.getMessage());
            result.put("data", null);
            
            return ResponseEntity.badRequest().body(result);
        }
    }

    /**
     * 重置密码
     */
    @PostMapping("/reset-password")
    public ResponseEntity<Map<String, Object>> resetPassword(@Valid @RequestBody PasswordResetRequest request) {
        logger.info("密码重置请求，邮箱: {}", request.getEmail());

        try {
            userService.resetPassword(request);
            
            Map<String, Object> result = new HashMap<>();
            result.put("code", 200);
            result.put("message", "密码重置成功");
            result.put("data", null);
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            logger.error("密码重置失败: {}", e.getMessage());
            
            Map<String, Object> result = new HashMap<>();
            result.put("code", 400);
            result.put("message", e.getMessage());
            result.put("data", null);
            
            return ResponseEntity.badRequest().body(result);
        }
    }

    /**
     * 检查用户名是否可用
     */
    @GetMapping("/check-username")
    public ResponseEntity<Map<String, Object>> checkUsername(@RequestParam String username) {
        logger.debug("检查用户名可用性，用户名: {}", username);

        try {
            boolean available = userService.isUsernameAvailable(username);
            
            Map<String, Object> result = new HashMap<>();
            result.put("code", 200);
            result.put("message", "检查完成");
            result.put("data", Map.of("available", available));
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            logger.error("检查用户名失败: {}", e.getMessage());
            
            Map<String, Object> result = new HashMap<>();
            result.put("code", 400);
            result.put("message", e.getMessage());
            result.put("data", null);
            
            return ResponseEntity.badRequest().body(result);
        }
    }

    /**
     * 检查邮箱是否可用
     */
    @GetMapping("/check-email")
    public ResponseEntity<Map<String, Object>> checkEmail(@RequestParam String email) {
        logger.debug("检查邮箱可用性，邮箱: {}", email);

        try {
            boolean available = userService.isEmailAvailable(email);
            
            Map<String, Object> result = new HashMap<>();
            result.put("code", 200);
            result.put("message", "检查完成");
            result.put("data", Map.of("available", available));
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            logger.error("检查邮箱失败: {}", e.getMessage());
            
            Map<String, Object> result = new HashMap<>();
            result.put("code", 400);
            result.put("message", e.getMessage());
            result.put("data", null);
            
            return ResponseEntity.badRequest().body(result);
        }
    }
}
