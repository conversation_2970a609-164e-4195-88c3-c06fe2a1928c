package com.ideary.user.service;

import com.ideary.user.dto.*;
import com.ideary.user.entity.User;

import java.util.List;

/**
 * 用户服务接口
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 2024-01-01
 */
public interface UserService {

    /**
     * 用户注册
     *
     * @param request 注册请求
     * @return 注册结果
     */
    UserLoginResponse register(UserRegisterRequest request);

    /**
     * 用户登录
     *
     * @param request 登录请求
     * @return 登录结果
     */
    UserLoginResponse login(UserLoginRequest request);

    /**
     * 用户登出
     *
     * @param userId 用户ID
     * @param token 访问Token
     */
    void logout(Long userId, String token);

    /**
     * 刷新Token
     *
     * @param refreshToken 刷新Token
     * @return 新的访问Token
     */
    UserLoginResponse refreshToken(String refreshToken);

    /**
     * 根据ID获取用户信息
     *
     * @param userId 用户ID
     * @return 用户信息
     */
    UserInfoResponse getUserInfo(Long userId);

    /**
     * 根据用户名获取用户信息
     *
     * @param username 用户名
     * @return 用户信息
     */
    UserInfoResponse getUserInfoByUsername(String username);

    /**
     * 更新用户基本信息
     *
     * @param userId 用户ID
     * @param request 更新请求
     * @return 更新后的用户信息
     */
    UserInfoResponse updateUserInfo(Long userId, UserUpdateRequest request);

    /**
     * 更新用户详细资料
     *
     * @param userId 用户ID
     * @param request 资料更新请求
     * @return 更新后的用户信息
     */
    UserInfoResponse updateUserProfile(Long userId, UserProfileUpdateRequest request);

    /**
     * 修改密码
     *
     * @param userId 用户ID
     * @param request 密码修改请求
     */
    void changePassword(Long userId, PasswordChangeRequest request);

    /**
     * 重置密码
     *
     * @param request 密码重置请求
     */
    void resetPassword(PasswordResetRequest request);

    /**
     * 发送邮箱验证码
     *
     * @param email 邮箱地址
     * @param type 验证码类型（注册、重置密码等）
     */
    void sendEmailVerificationCode(String email, String type);

    /**
     * 验证邮箱验证码
     *
     * @param email 邮箱地址
     * @param code 验证码
     * @param type 验证码类型
     * @return 是否验证成功
     */
    boolean verifyEmailCode(String email, String code, String type);

    /**
     * 验证邮箱
     *
     * @param userId 用户ID
     * @param code 验证码
     */
    void verifyEmail(Long userId, String code);

    /**
     * 发送手机验证码
     *
     * @param phone 手机号
     * @param type 验证码类型
     */
    void sendSmsVerificationCode(String phone, String type);

    /**
     * 验证手机验证码
     *
     * @param phone 手机号
     * @param code 验证码
     * @param type 验证码类型
     * @return 是否验证成功
     */
    boolean verifySmsCode(String phone, String code, String type);

    /**
     * 验证手机号
     *
     * @param userId 用户ID
     * @param phone 手机号
     * @param code 验证码
     */
    void verifyPhone(Long userId, String phone, String code);

    /**
     * 关注用户
     *
     * @param followerId 关注者ID
     * @param followingId 被关注者ID
     */
    void followUser(Long followerId, Long followingId);

    /**
     * 取消关注用户
     *
     * @param followerId 关注者ID
     * @param followingId 被关注者ID
     */
    void unfollowUser(Long followerId, Long followingId);

    /**
     * 检查是否已关注
     *
     * @param followerId 关注者ID
     * @param followingId 被关注者ID
     * @return 是否已关注
     */
    boolean isFollowing(Long followerId, Long followingId);

    /**
     * 获取关注列表
     *
     * @param userId 用户ID
     * @param page 页码
     * @param size 每页大小
     * @return 关注列表
     */
    List<UserInfoResponse> getFollowingList(Long userId, int page, int size);

    /**
     * 获取粉丝列表
     *
     * @param userId 用户ID
     * @param page 页码
     * @param size 每页大小
     * @return 粉丝列表
     */
    List<UserInfoResponse> getFollowersList(Long userId, int page, int size);

    /**
     * 获取互相关注列表
     *
     * @param userId 用户ID
     * @param page 页码
     * @param size 每页大小
     * @return 互相关注列表
     */
    List<UserInfoResponse> getMutualFollowsList(Long userId, int page, int size);

    /**
     * 上传头像
     *
     * @param userId 用户ID
     * @param avatarData 头像数据
     * @return 头像URL
     */
    String uploadAvatar(Long userId, byte[] avatarData);

    /**
     * 禁用用户
     *
     * @param userId 用户ID
     * @param reason 禁用原因
     */
    void disableUser(Long userId, String reason);

    /**
     * 启用用户
     *
     * @param userId 用户ID
     */
    void enableUser(Long userId);

    /**
     * 注销用户
     *
     * @param userId 用户ID
     * @param password 密码确认
     */
    void deleteUser(Long userId, String password);

    /**
     * 检查用户名是否可用
     *
     * @param username 用户名
     * @return 是否可用
     */
    boolean isUsernameAvailable(String username);

    /**
     * 检查邮箱是否可用
     *
     * @param email 邮箱
     * @return 是否可用
     */
    boolean isEmailAvailable(String email);

    /**
     * 检查手机号是否可用
     *
     * @param phone 手机号
     * @return 是否可用
     */
    boolean isPhoneAvailable(String phone);

    /**
     * 根据关键词搜索用户
     *
     * @param keyword 关键词
     * @param page 页码
     * @param size 每页大小
     * @return 用户列表
     */
    List<UserInfoResponse> searchUsers(String keyword, int page, int size);

    /**
     * 获取用户统计信息
     *
     * @param userId 用户ID
     * @return 统计信息
     */
    UserStatsResponse getUserStats(Long userId);

    /**
     * 批量获取用户信息
     *
     * @param userIds 用户ID列表
     * @return 用户信息列表
     */
    List<UserInfoResponse> getUserInfoBatch(List<Long> userIds);
}
