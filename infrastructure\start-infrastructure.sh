#!/bin/bash

# Ideary项目基础设施启动脚本
# 用于启动所有必需的中间件服务

set -e

echo "=========================================="
echo "  Ideary 基础设施启动脚本"
echo "=========================================="

# 检查Docker是否运行
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker未运行，请先启动Docker"
    exit 1
fi

# 检查Docker Compose是否安装
if ! command -v docker-compose > /dev/null 2>&1; then
    echo "❌ Docker Compose未安装，请先安装Docker Compose"
    exit 1
fi

# 创建必要的目录
echo "📁 创建必要的目录..."
mkdir -p mysql/data mysql/logs
mkdir -p redis/data
mkdir -p nacos/logs
mkdir -p elasticsearch/data elasticsearch/logs
mkdir -p rocketmq/nameserver/logs
mkdir -p rocketmq/broker/logs rocketmq/broker/store
mkdir -p minio/data
mkdir -p nginx/logs nginx/html

# 设置目录权限
echo "🔐 设置目录权限..."
chmod -R 755 mysql redis nacos elasticsearch rocketmq minio nginx

# 检查端口占用
echo "🔍 检查端口占用情况..."
ports=(3306 6379 8848 9200 9876 10909 10911 9000 9001 8080 80)
for port in "${ports[@]}"; do
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
        echo "⚠️  警告: 端口 $port 已被占用"
        read -p "是否继续启动？(y/n): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            echo "❌ 启动已取消"
            exit 1
        fi
        break
    fi
done

# 拉取最新镜像
echo "📥 拉取最新Docker镜像..."
docker-compose pull

# 启动基础设施服务
echo "🚀 启动基础设施服务..."
docker-compose up -d

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 30

# 检查服务状态
echo "🔍 检查服务状态..."
services=(mysql redis nacos elasticsearch rocketmq-nameserver rocketmq-broker minio)
all_healthy=true

for service in "${services[@]}"; do
    if docker-compose ps $service | grep -q "Up"; then
        echo "✅ $service 启动成功"
    else
        echo "❌ $service 启动失败"
        all_healthy=false
    fi
done

if [ "$all_healthy" = true ]; then
    echo ""
    echo "🎉 所有服务启动成功！"
    echo ""
    echo "📋 服务访问地址："
    echo "   MySQL:          localhost:3306"
    echo "   Redis:          localhost:6379"
    echo "   Nacos:          http://localhost:8848/nacos"
    echo "   ElasticSearch:  http://localhost:9200"
    echo "   Kibana:         http://localhost:5601"
    echo "   RocketMQ控制台: http://localhost:8080"
    echo "   MinIO控制台:    http://localhost:9001"
    echo "   Nginx:          http://localhost:80"
    echo ""
    echo "🔑 默认账号密码："
    echo "   MySQL:    用户名: ideary, 密码: ideary123456"
    echo "   Nacos:    用户名: nacos,  密码: nacos"
    echo "   MinIO:    用户名: ideary, 密码: ideary123456"
    echo ""
    echo "📝 查看日志命令："
    echo "   docker-compose logs -f [服务名]"
    echo ""
    echo "🛑 停止服务命令："
    echo "   docker-compose down"
    echo ""
else
    echo ""
    echo "❌ 部分服务启动失败，请检查日志："
    echo "   docker-compose logs"
    exit 1
fi
