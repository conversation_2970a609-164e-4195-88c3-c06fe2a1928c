package com.ideary.user;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;

/**
 * Ideary用户服务启动类
 * 
 * 功能说明：
 * 1. 用户注册、登录功能
 * 2. 用户资料管理
 * 3. 基于RBAC的权限角色管理
 * 4. 关注/粉丝关系管理
 * 5. JWT登录认证机制
 * 6. 用户状态管理
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 2024-01-01
 */
@SpringBootApplication
@EnableDiscoveryClient
@EnableFeignClients
@MapperScan("com.ideary.user.mapper")
public class IdearyUserApplication {

   public static void main(String[] args) {
      SpringApplication.run(IdearyUserApplication.class, args);
      System.out.println("""

            ========================================
                Ideary User Service Started
            ========================================

            🚀 用户服务启动成功！

            📋 服务信息：
               服务名称: ideary-user
               服务端口: 9081
               服务版本: 1.0.0

            🔗 访问地址：
               服务地址: http://localhost:9081
               健康检查: http://localhost:9081/actuator/health
               API文档: http://localhost:9081/doc.html

            📚 主要功能：
               ✅ 用户注册、登录
               ✅ 用户资料管理
               ✅ 权限角色管理(RBAC)
               ✅ 关注/粉丝关系管理
               ✅ JWT认证机制
               ✅ 用户状态管理

            🔑 API接口：
               POST /api/v1/auth/register    - 用户注册
               POST /api/v1/auth/login       - 用户登录
               POST /api/v1/auth/logout      - 用户登出
               POST /api/v1/auth/refresh     - 刷新Token
               GET  /api/v1/users/profile    - 获取用户资料
               PUT  /api/v1/users/profile    - 更新用户资料
               POST /api/v1/users/follow     - 关注用户
               DELETE /api/v1/users/unfollow - 取消关注

            ========================================
            """);
   }
}
