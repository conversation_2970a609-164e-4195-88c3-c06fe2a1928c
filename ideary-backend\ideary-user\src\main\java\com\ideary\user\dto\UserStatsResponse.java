package com.ideary.user.dto;

/**
 * 用户统计信息响应DTO
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 2024-01-01
 */
public class UserStatsResponse {

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 关注数
     */
    private Long followingCount = 0L;

    /**
     * 粉丝数
     */
    private Long followersCount = 0L;

    /**
     * 互相关注数
     */
    private Long mutualFollowsCount = 0L;

    /**
     * 文章总数
     */
    private Long articlesCount = 0L;

    /**
     * 已发布文章数
     */
    private Long publishedArticlesCount = 0L;

    /**
     * 草稿文章数
     */
    private Long draftArticlesCount = 0L;

    /**
     * 获得的总点赞数
     */
    private Long totalLikesCount = 0L;

    /**
     * 获得的总收藏数
     */
    private Long totalFavoritesCount = 0L;

    /**
     * 获得的总评论数
     */
    private Long totalCommentsCount = 0L;

    /**
     * 文章总阅读量
     */
    private Long totalViewsCount = 0L;

    /**
     * 今日新增关注数
     */
    private Long todayFollowersCount = 0L;

    /**
     * 今日文章阅读量
     */
    private Long todayViewsCount = 0L;

    /**
     * 今日获得点赞数
     */
    private Long todayLikesCount = 0L;

    /**
     * 本周新增关注数
     */
    private Long weekFollowersCount = 0L;

    /**
     * 本周文章阅读量
     */
    private Long weekViewsCount = 0L;

    /**
     * 本月新增关注数
     */
    private Long monthFollowersCount = 0L;

    /**
     * 本月文章阅读量
     */
    private Long monthViewsCount = 0L;

    // 构造函数
    public UserStatsResponse() {}

    public UserStatsResponse(Long userId) {
        this.userId = userId;
    }

    // Getter和Setter方法
    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long getFollowingCount() {
        return followingCount;
    }

    public void setFollowingCount(Long followingCount) {
        this.followingCount = followingCount;
    }

    public Long getFollowersCount() {
        return followersCount;
    }

    public void setFollowersCount(Long followersCount) {
        this.followersCount = followersCount;
    }

    public Long getMutualFollowsCount() {
        return mutualFollowsCount;
    }

    public void setMutualFollowsCount(Long mutualFollowsCount) {
        this.mutualFollowsCount = mutualFollowsCount;
    }

    public Long getArticlesCount() {
        return articlesCount;
    }

    public void setArticlesCount(Long articlesCount) {
        this.articlesCount = articlesCount;
    }

    public Long getPublishedArticlesCount() {
        return publishedArticlesCount;
    }

    public void setPublishedArticlesCount(Long publishedArticlesCount) {
        this.publishedArticlesCount = publishedArticlesCount;
    }

    public Long getDraftArticlesCount() {
        return draftArticlesCount;
    }

    public void setDraftArticlesCount(Long draftArticlesCount) {
        this.draftArticlesCount = draftArticlesCount;
    }

    public Long getTotalLikesCount() {
        return totalLikesCount;
    }

    public void setTotalLikesCount(Long totalLikesCount) {
        this.totalLikesCount = totalLikesCount;
    }

    public Long getTotalFavoritesCount() {
        return totalFavoritesCount;
    }

    public void setTotalFavoritesCount(Long totalFavoritesCount) {
        this.totalFavoritesCount = totalFavoritesCount;
    }

    public Long getTotalCommentsCount() {
        return totalCommentsCount;
    }

    public void setTotalCommentsCount(Long totalCommentsCount) {
        this.totalCommentsCount = totalCommentsCount;
    }

    public Long getTotalViewsCount() {
        return totalViewsCount;
    }

    public void setTotalViewsCount(Long totalViewsCount) {
        this.totalViewsCount = totalViewsCount;
    }

    public Long getTodayFollowersCount() {
        return todayFollowersCount;
    }

    public void setTodayFollowersCount(Long todayFollowersCount) {
        this.todayFollowersCount = todayFollowersCount;
    }

    public Long getTodayViewsCount() {
        return todayViewsCount;
    }

    public void setTodayViewsCount(Long todayViewsCount) {
        this.todayViewsCount = todayViewsCount;
    }

    public Long getTodayLikesCount() {
        return todayLikesCount;
    }

    public void setTodayLikesCount(Long todayLikesCount) {
        this.todayLikesCount = todayLikesCount;
    }

    public Long getWeekFollowersCount() {
        return weekFollowersCount;
    }

    public void setWeekFollowersCount(Long weekFollowersCount) {
        this.weekFollowersCount = weekFollowersCount;
    }

    public Long getWeekViewsCount() {
        return weekViewsCount;
    }

    public void setWeekViewsCount(Long weekViewsCount) {
        this.weekViewsCount = weekViewsCount;
    }

    public Long getMonthFollowersCount() {
        return monthFollowersCount;
    }

    public void setMonthFollowersCount(Long monthFollowersCount) {
        this.monthFollowersCount = monthFollowersCount;
    }

    public Long getMonthViewsCount() {
        return monthViewsCount;
    }

    public void setMonthViewsCount(Long monthViewsCount) {
        this.monthViewsCount = monthViewsCount;
    }

    @Override
    public String toString() {
        return "UserStatsResponse{" +
                "userId=" + userId +
                ", followingCount=" + followingCount +
                ", followersCount=" + followersCount +
                ", articlesCount=" + articlesCount +
                ", totalLikesCount=" + totalLikesCount +
                ", totalViewsCount=" + totalViewsCount +
                '}';
    }
}
