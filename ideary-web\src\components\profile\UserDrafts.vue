<template>
  <div class="user-drafts">
    <el-skeleton v-if="loading" :rows="3" animated />
    
    <div v-else-if="drafts.length > 0" class="drafts-list">
      <div
        v-for="draft in drafts"
        :key="draft.id"
        class="draft-item"
      >
        <h3 class="draft-title">{{ draft.title || '无标题草稿' }}</h3>
        <p class="draft-summary">{{ draft.summary || '暂无内容...' }}</p>
        <div class="draft-meta">
          <span class="update-date">{{ formatDate(draft.updatedAt) }}</span>
          <div class="draft-actions">
            <el-button size="small" @click="editDraft(draft.id)">编辑</el-button>
            <el-button size="small" type="danger" @click="deleteDraft(draft.id)">删除</el-button>
          </div>
        </div>
      </div>
    </div>
    
    <div v-else class="empty-state">
      <el-empty description="暂无草稿" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import dayjs from 'dayjs'

interface Props {
  userId?: number
}

const props = defineProps<Props>()
const router = useRouter()

const loading = ref(true)
const drafts = ref<any[]>([])

const formatDate = (date: string) => {
  return dayjs(date).format('YYYY年MM月DD日 HH:mm')
}

const editDraft = (draftId: number) => {
  router.push(`/write?draft=${draftId}`)
}

const deleteDraft = async (draftId: number) => {
  try {
    await ElMessageBox.confirm('确定要删除这篇草稿吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    
    // 模拟删除API
    await new Promise(resolve => setTimeout(resolve, 500))
    
    drafts.value = drafts.value.filter(draft => draft.id !== draftId)
    ElMessage.success('草稿已删除')
  } catch (error) {
    // 用户取消删除
  }
}

const fetchUserDrafts = async () => {
  try {
    loading.value = true
    await new Promise(resolve => setTimeout(resolve, 500))
    
    // 模拟数据
    drafts.value = [
      {
        id: 1,
        title: '未完成的技术文章',
        summary: '这是一篇正在编写中的技术文章草稿...',
        updatedAt: '2024-01-20T15:30:00Z',
      }
    ]
  } catch (error) {
    console.error('获取用户草稿失败:', error)
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  fetchUserDrafts()
})
</script>

<style lang="scss" scoped>
.user-drafts {
  .drafts-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
  }
  
  .draft-item {
    padding: 1rem;
    border: 1px solid var(--el-border-color-light);
    border-radius: 0.5rem;
    background: var(--el-fill-color-lighter);
    
    .draft-title {
      font-size: 1.125rem;
      font-weight: 600;
      margin-bottom: 0.5rem;
      color: var(--el-text-color-primary);
    }
    
    .draft-summary {
      color: var(--el-text-color-regular);
      margin-bottom: 0.75rem;
      font-style: italic;
    }
    
    .draft-meta {
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .update-date {
        font-size: 0.875rem;
        color: var(--el-text-color-placeholder);
      }
      
      .draft-actions {
        display: flex;
        gap: 0.5rem;
      }
    }
  }
  
  .empty-state {
    text-align: center;
    padding: 2rem 0;
  }
}
</style>
