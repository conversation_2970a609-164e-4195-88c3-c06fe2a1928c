<template>
  <div id="app" :class="{ 'dark': isDark }">
    <el-config-provider :locale="locale">
      <!-- 全局加载条 -->
      <div v-if="loading" class="global-loading">
        <el-loading-service />
      </div>
      
      <!-- 主要内容 -->
      <router-view />
      
      <!-- 全局通知 -->
      <Teleport to="body">
        <div id="message-container"></div>
      </Teleport>
    </el-config-provider>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRouter } from 'vue-router'
import { useDark } from '@vueuse/core'
import zhCn from 'element-plus/es/locale/lang/zh-cn'
import { useUserStore } from '@/stores/user'
import { useAppStore } from '@/stores/app'

const router = useRouter()
const userStore = useUserStore()
const appStore = useAppStore()

// 主题切换
const isDark = useDark({
  selector: 'html',
  attribute: 'class',
  valueDark: 'dark',
  valueLight: 'light',
})

// 国际化
const locale = computed(() => zhCn)

// 全局加载状态
const loading = computed(() => appStore.loading)

// 初始化应用
onMounted(async () => {
  // 检查用户登录状态
  await userStore.checkAuth()
  
  // 设置路由守卫
  router.beforeEach((to, from, next) => {
    // 设置页面标题
    if (to.meta.title) {
      document.title = `${to.meta.title} - Ideary`
    }
    
    // 检查是否需要登录
    if (to.meta.requiresAuth && !userStore.isAuthenticated) {
      next('/auth/login')
      return
    }
    
    // 检查是否已登录用户访问登录页
    if (to.path.startsWith('/auth') && userStore.isAuthenticated) {
      next('/')
      return
    }
    
    next()
  })
})
</script>

<style lang="scss">
#app {
  min-height: 100vh;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  transition: all 0.3s ease;
}

.global-loading {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
}

.dark .global-loading {
  background: rgba(0, 0, 0, 0.8);
}

// 自定义滚动条
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: var(--el-bg-color-page);
}

::-webkit-scrollbar-thumb {
  background: var(--el-border-color-light);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--el-border-color);
}

// 响应式断点
@media (max-width: 768px) {
  #app {
    font-size: 14px;
  }
}
</style>
