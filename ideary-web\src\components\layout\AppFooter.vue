<template>
  <footer class="app-footer">
    <div class="footer-container">
      <div class="footer-content">
        <!-- 品牌信息 -->
        <div class="footer-brand">
          <div class="brand-logo">
            <div class="logo-icon">
              <span>I</span>
            </div>
            <span class="logo-text">Ideary</span>
          </div>
          <p class="brand-description">
            一个专注于技术分享和灵感交流的现代化社区平台，汇聚优秀的技术文章、经验分享和创新思维。
          </p>
          
          <!-- 联系信息 -->
          <div class="contact-info">
            <div class="contact-item">
              <el-icon><Message /></el-icon>
              <span><EMAIL></span>
            </div>
            <div class="contact-item">
              <el-icon><Location /></el-icon>
              <span>中国 · 深圳</span>
            </div>
          </div>
        </div>

        <!-- 链接组 -->
        <div class="footer-links">
          <div class="link-group">
            <h3 class="group-title">产品</h3>
            <ul class="link-list">
              <li><router-link to="/">首页</router-link></li>
              <li><router-link to="/articles">文章</router-link></li>
              <li><router-link to="/topics">话题</router-link></li>
              <li><router-link to="/authors">作者</router-link></li>
            </ul>
          </div>

          <div class="link-group">
            <h3 class="group-title">公司</h3>
            <ul class="link-list">
              <li><router-link to="/about">关于我们</router-link></li>
              <li><a href="mailto:<EMAIL>">联系我们</a></li>
              <li><a href="/careers">加入我们</a></li>
              <li><a href="/press">媒体资源</a></li>
            </ul>
          </div>

          <div class="link-group">
            <h3 class="group-title">资源</h3>
            <ul class="link-list">
              <li><a href="/help">帮助中心</a></li>
              <li><a href="/docs/api">API文档</a></li>
              <li><a href="/docs/developers">开发者指南</a></li>
              <li><a href="/status">状态页面</a></li>
            </ul>
          </div>

          <div class="link-group">
            <h3 class="group-title">法律</h3>
            <ul class="link-list">
              <li><a href="/privacy">隐私政策</a></li>
              <li><a href="/terms">服务条款</a></li>
              <li><a href="/cookies">Cookie政策</a></li>
              <li><a href="/copyright">版权声明</a></li>
            </ul>
          </div>
        </div>
      </div>

      <!-- 分割线 -->
      <div class="footer-divider"></div>

      <!-- 底部信息 -->
      <div class="footer-bottom">
        <div class="copyright">
          <span>&copy; {{ currentYear }} Ideary. 保留所有权利.</span>
          <span class="made-with">
            Made with
            <el-icon class="heart-icon"><Promotion /></el-icon>
            in China
          </span>
        </div>

        <!-- 社交媒体链接 -->
        <div class="social-links">
          <a
            v-for="social in socialLinks"
            :key="social.name"
            :href="social.url"
            target="_blank"
            rel="noopener noreferrer"
            class="social-link"
            :title="social.name"
          >
            <component :is="social.icon" />
          </a>
        </div>
      </div>
    </div>
  </footer>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { Message, Location, Promotion } from '@element-plus/icons-vue'

// 当前年份
const currentYear = computed(() => new Date().getFullYear())

// 社交媒体链接
const socialLinks = [
  {
    name: 'GitHub',
    url: 'https://github.com/ideary-team',
    icon: 'github-icon'
  },
  {
    name: 'Twitter',
    url: 'https://twitter.com/ideary_dev',
    icon: 'twitter-icon'
  },
  {
    name: 'Discord',
    url: 'https://discord.gg/ideary',
    icon: 'discord-icon'
  },
  {
    name: '微信',
    url: '#',
    icon: 'wechat-icon'
  }
]
</script>

<style lang="scss" scoped>
.app-footer {
  background: var(--el-fill-color-lighter);
  border-top: 1px solid var(--el-border-color-light);
  margin-top: auto;
}

.footer-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 3rem 1rem 1.5rem;
}

.footer-content {
  display: grid;
  grid-template-columns: 2fr 3fr;
  gap: 3rem;
  margin-bottom: 2rem;
  
  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: 2rem;
  }
}

.footer-brand {
  .brand-logo {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
    
    .logo-icon {
      width: 2rem;
      height: 2rem;
      background: linear-gradient(135deg, #3b82f6, #1d4ed8);
      border-radius: 0.5rem;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-weight: bold;
      font-size: 1.125rem;
    }
    
    .logo-text {
      font-size: 1.25rem;
      font-weight: bold;
      color: var(--el-text-color-primary);
    }
  }
  
  .brand-description {
    color: var(--el-text-color-regular);
    line-height: 1.6;
    margin-bottom: 1.5rem;
    max-width: 400px;
  }
  
  .contact-info {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    
    .contact-item {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      color: var(--el-text-color-regular);
      font-size: 0.875rem;
      
      .el-icon {
        color: var(--el-color-primary);
      }
    }
  }
}

.footer-links {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 2rem;
  
  @media (max-width: 640px) {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }
  
  .link-group {
    .group-title {
      font-size: 0.875rem;
      font-weight: 600;
      color: var(--el-text-color-primary);
      text-transform: uppercase;
      letter-spacing: 0.05em;
      margin-bottom: 1rem;
    }
    
    .link-list {
      list-style: none;
      padding: 0;
      margin: 0;
      
      li {
        margin-bottom: 0.75rem;
        
        a {
          color: var(--el-text-color-regular);
          text-decoration: none;
          transition: color 0.2s ease;
          
          &:hover {
            color: var(--el-color-primary);
          }
        }
      }
    }
  }
}

.footer-divider {
  height: 1px;
  background: var(--el-border-color-light);
  margin: 2rem 0 1.5rem;
}

.footer-bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
  
  @media (max-width: 640px) {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }
  
  .copyright {
    display: flex;
    align-items: center;
    gap: 1rem;
    color: var(--el-text-color-regular);
    font-size: 0.875rem;
    
    @media (max-width: 640px) {
      flex-direction: column;
      gap: 0.5rem;
    }
    
    .made-with {
      display: flex;
      align-items: center;
      gap: 0.25rem;
      
      .heart-icon {
        color: #ef4444;
        font-size: 1rem;
      }
    }
  }
  
  .social-links {
    display: flex;
    gap: 1rem;
    
    .social-link {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 2rem;
      height: 2rem;
      color: var(--el-text-color-regular);
      transition: color 0.2s ease;
      
      &:hover {
        color: var(--el-color-primary);
      }
      
      svg {
        width: 1.25rem;
        height: 1.25rem;
      }
    }
  }
}
</style>
