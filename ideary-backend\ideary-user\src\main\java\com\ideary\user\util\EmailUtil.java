package com.ideary.user.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mail.SimpleMailMessage;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Component;

import jakarta.mail.MessagingException;
import jakarta.mail.internet.MimeMessage;

/**
 * 邮件工具类
 * 
 * 功能说明：
 * 1. 发送简单邮件
 * 2. 发送HTML邮件
 * 3. 发送带附件的邮件
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 2024-01-01
 */
@Component
public class EmailUtil {

    private static final Logger logger = LoggerFactory.getLogger(EmailUtil.class);

    @Autowired
    private JavaMailSender mailSender;

    @Value("${spring.mail.username}")
    private String from;

    /**
     * 发送简单文本邮件
     *
     * @param to 收件人
     * @param subject 主题
     * @param content 内容
     */
    public void sendSimpleMail(String to, String subject, String content) {
        try {
            SimpleMailMessage message = new SimpleMailMessage();
            message.setFrom(from);
            message.setTo(to);
            message.setSubject(subject);
            message.setText(content);
            
            mailSender.send(message);
            logger.info("简单邮件发送成功，收件人: {}, 主题: {}", to, subject);
        } catch (Exception e) {
            logger.error("简单邮件发送失败，收件人: {}, 主题: {}, 错误: {}", to, subject, e.getMessage(), e);
            throw new RuntimeException("邮件发送失败: " + e.getMessage());
        }
    }

    /**
     * 发送HTML邮件
     *
     * @param to 收件人
     * @param subject 主题
     * @param content HTML内容
     */
    public void sendHtmlMail(String to, String subject, String content) {
        try {
            MimeMessage message = mailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(message, true);
            
            helper.setFrom(from);
            helper.setTo(to);
            helper.setSubject(subject);
            helper.setText(content, true);
            
            mailSender.send(message);
            logger.info("HTML邮件发送成功，收件人: {}, 主题: {}", to, subject);
        } catch (MessagingException e) {
            logger.error("HTML邮件发送失败，收件人: {}, 主题: {}, 错误: {}", to, subject, e.getMessage(), e);
            throw new RuntimeException("邮件发送失败: " + e.getMessage());
        }
    }

    /**
     * 发送验证码邮件
     *
     * @param to 收件人
     * @param code 验证码
     * @param type 验证码类型
     */
    public void sendVerificationCode(String to, String code, String type) {
        String subject = getVerificationSubject(type);
        String content = buildVerificationContent(code, type);
        sendHtmlMail(to, subject, content);
    }

    /**
     * 发送欢迎邮件
     *
     * @param to 收件人
     * @param username 用户名
     */
    public void sendWelcomeMail(String to, String username) {
        String subject = "欢迎加入Ideary技术灵感分享社区！";
        String content = buildWelcomeContent(username);
        sendHtmlMail(to, subject, content);
    }

    /**
     * 发送密码重置邮件
     *
     * @param to 收件人
     * @param resetLink 重置链接
     */
    public void sendPasswordResetMail(String to, String resetLink) {
        String subject = "Ideary - 密码重置";
        String content = buildPasswordResetContent(resetLink);
        sendHtmlMail(to, subject, content);
    }

    // 私有辅助方法
    private String getVerificationSubject(String type) {
        return switch (type) {
            case "register" -> "Ideary - 注册验证码";
            case "reset_password" -> "Ideary - 密码重置验证码";
            case "verify_email" -> "Ideary - 邮箱验证码";
            case "change_email" -> "Ideary - 更换邮箱验证码";
            default -> "Ideary - 验证码";
        };
    }

    private String buildVerificationContent(String code, String type) {
        String action = switch (type) {
            case "register" -> "注册";
            case "reset_password" -> "重置密码";
            case "verify_email" -> "验证邮箱";
            case "change_email" -> "更换邮箱";
            default -> "验证";
        };

        return String.format("""
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="UTF-8">
                <title>Ideary - 验证码</title>
                <style>
                    body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
                    .container { max-width: 600px; margin: 0 auto; padding: 20px; }
                    .header { background: #007bff; color: white; padding: 20px; text-align: center; }
                    .content { padding: 30px; background: #f9f9f9; }
                    .code { font-size: 24px; font-weight: bold; color: #007bff; text-align: center; 
                           padding: 20px; background: white; border: 2px dashed #007bff; margin: 20px 0; }
                    .footer { padding: 20px; text-align: center; color: #666; font-size: 12px; }
                </style>
            </head>
            <body>
                <div class="container">
                    <div class="header">
                        <h1>Ideary 技术灵感分享社区</h1>
                    </div>
                    <div class="content">
                        <h2>您好！</h2>
                        <p>您正在进行<strong>%s</strong>操作，验证码为：</p>
                        <div class="code">%s</div>
                        <p><strong>注意事项：</strong></p>
                        <ul>
                            <li>验证码有效期为10分钟，请及时使用</li>
                            <li>请勿将验证码告诉他人</li>
                            <li>如果这不是您的操作，请忽略此邮件</li>
                        </ul>
                    </div>
                    <div class="footer">
                        <p>此邮件由系统自动发送，请勿回复</p>
                        <p>&copy; 2024 Ideary Team. All rights reserved.</p>
                    </div>
                </div>
            </body>
            </html>
            """, action, code);
    }

    private String buildWelcomeContent(String username) {
        return String.format("""
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="UTF-8">
                <title>欢迎加入Ideary</title>
                <style>
                    body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
                    .container { max-width: 600px; margin: 0 auto; padding: 20px; }
                    .header { background: #28a745; color: white; padding: 20px; text-align: center; }
                    .content { padding: 30px; background: #f9f9f9; }
                    .footer { padding: 20px; text-align: center; color: #666; font-size: 12px; }
                </style>
            </head>
            <body>
                <div class="container">
                    <div class="header">
                        <h1>🎉 欢迎加入Ideary！</h1>
                    </div>
                    <div class="content">
                        <h2>亲爱的 %s，</h2>
                        <p>欢迎加入Ideary技术灵感分享社区！我们很高兴您成为我们大家庭的一员。</p>
                        <p><strong>在这里，您可以：</strong></p>
                        <ul>
                            <li>📝 分享您的技术见解和经验</li>
                            <li>💡 发现最新的技术趋势和灵感</li>
                            <li>🤝 与志同道合的技术人员交流</li>
                            <li>📚 学习和成长，提升技术能力</li>
                        </ul>
                        <p>现在就开始您的技术分享之旅吧！</p>
                    </div>
                    <div class="footer">
                        <p>此邮件由系统自动发送，请勿回复</p>
                        <p>&copy; 2024 Ideary Team. All rights reserved.</p>
                    </div>
                </div>
            </body>
            </html>
            """, username);
    }

    private String buildPasswordResetContent(String resetLink) {
        return String.format("""
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="UTF-8">
                <title>Ideary - 密码重置</title>
                <style>
                    body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
                    .container { max-width: 600px; margin: 0 auto; padding: 20px; }
                    .header { background: #dc3545; color: white; padding: 20px; text-align: center; }
                    .content { padding: 30px; background: #f9f9f9; }
                    .button { display: inline-block; padding: 12px 24px; background: #007bff; 
                             color: white; text-decoration: none; border-radius: 4px; margin: 20px 0; }
                    .footer { padding: 20px; text-align: center; color: #666; font-size: 12px; }
                </style>
            </head>
            <body>
                <div class="container">
                    <div class="header">
                        <h1>🔒 密码重置</h1>
                    </div>
                    <div class="content">
                        <h2>您好！</h2>
                        <p>我们收到了您的密码重置请求。请点击下面的链接重置您的密码：</p>
                        <p style="text-align: center;">
                            <a href="%s" class="button">重置密码</a>
                        </p>
                        <p><strong>注意事项：</strong></p>
                        <ul>
                            <li>此链接有效期为30分钟</li>
                            <li>如果这不是您的操作，请忽略此邮件</li>
                            <li>为了您的账户安全，请勿将此链接分享给他人</li>
                        </ul>
                    </div>
                    <div class="footer">
                        <p>此邮件由系统自动发送，请勿回复</p>
                        <p>&copy; 2024 Ideary Team. All rights reserved.</p>
                    </div>
                </div>
            </body>
            </html>
            """, resetLink);
    }
}
