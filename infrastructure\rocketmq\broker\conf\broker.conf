# RocketMQ Broker 配置文件 - Ideary项目专用

# 集群名称
brokerClusterName = DefaultCluster

# Broker名称
brokerName = broker-a

# Broker ID，0表示Master，>0表示Slave
brokerId = 0

# NameServer地址
namesrvAddr = rocketmq-nameserver:9876

# 删除文件时间点，默认凌晨4点
deleteWhen = 04

# 文件保留时间，默认72小时
fileReservedTime = 72

# Broker角色
# - ASYNC_MASTER 异步复制Master
# - SYNC_MASTER 同步双写Master
# - SLAVE
brokerRole = ASYNC_MASTER

# 刷盘方式
# - ASYNC_FLUSH 异步刷盘
# - SYNC_FLUSH 同步刷盘
flushDiskType = ASYNC_FLUSH

# Broker监听端口
listenPort = 10911

# 存储路径
storePathRootDir = /home/<USER>/store
storePathCommitLog = /home/<USER>/store/commitlog
storePathConsumeQueue = /home/<USER>/store/consumequeue
storePathIndex = /home/<USER>/store/index

# 检查点文件存储路径
storeCheckpoint = /home/<USER>/store/checkpoint

# 异常文件存储路径
abortFile = /home/<USER>/store/abort

# 限制消息大小
maxMessageSize = 65536

# 磁盘使用率阈值
diskMaxUsedSpaceRatio = 88

# 发送消息线程池数量
sendMessageThreadPoolNums = 128

# 拉取消息线程池数量
pullMessageThreadPoolNums = 128

# 查询消息线程池数量
queryMessageThreadPoolNums = 8

# 管理Broker线程池数量
adminBrokerThreadPoolNums = 16

# 客户端管理线程池数量
clientManageThreadPoolNums = 32

# 消费者管理线程池数量
consumerManageThreadPoolNums = 32

# 心跳线程池数量
heartbeatThreadPoolNums = 8

# 结束事务线程池数量
endTransactionThreadPoolNums = 8

# 刷盘线程数量
flushCommitLogTimed = true

# 刷盘间隔时间
flushIntervalCommitLog = 500

# 刷盘页数
flushCommitLogThoroughInterval = 10000

# 刷盘等待时间
flushIntervalConsumeQueue = 1000

# 清理资源间隔时间
cleanResourceInterval = 10000

# 删除CommitLog文件间隔时间
deleteCommitLogFilesInterval = 100

# 删除ConsumeQueue文件间隔时间
deleteConsumeQueueFilesInterval = 100

# 销毁MapedFile间隔时间
destroyMapedFileIntervalForcibly = 120000

# 重新删除文件间隔时间
redeleteHangedFileInterval = 120000

# 检查Hanged文件间隔时间
checkTransactionMessageEnable = false

# 发送消息回查最大次数
sendMessageBackQueueId = 0

# 是否开启字节缓冲区重用
transientStorePoolEnable = false

# 字节缓冲区池大小
transientStorePoolSize = 5

# 是否快速失败
fastFailIfNoBufferInStorePool = false

# 是否开启DLeger
enableDLegerCommitLog = false

# DLeger组
dLegerGroup = broker-a

# DLeger节点
dLegerPeers = n0-127.0.0.1:40911

# DLeger自身节点id
dLegerSelfId = n0

# 是否开启消息轨迹
traceTopicEnable = true

# 消息轨迹Topic
msgTraceTopicName = RMQ_SYS_TRACE_TOPIC

# 是否开启ACL
aclEnable = false

# 是否开启Pop消费
enablePopLog = false

# 是否开启定时消息
enableScheduleMessageStats = true

# 是否开启批量拉取
enableBatchPull = false

# 是否开启属性过滤
enablePropertyFilter = false

# 是否开启Calltrace
enableCalltrace = false

# 是否使用可重入锁
useReentrantLockWhenPutMessage = true

# 是否开启事务消息
transactionCheckMax = 15
transactionCheckInterval = 60000

# 是否开启slave读
slaveReadEnable = false

# 网络相关配置
serverWorkerThreads = 8
serverCallbackExecutorThreads = 0
serverSelectorThreads = 3
serverOnewaySemaphoreValue = 256
serverAsyncSemaphoreValue = 64
serverChannelMaxIdleTimeSeconds = 120

# 客户端相关配置
clientWorkerThreads = 4
clientCallbackExecutorThreads = 2
clientOnewaySemaphoreValue = 65535
clientAsyncSemaphoreValue = 65535
clientChannelMaxIdleTimeSeconds = 120

# 是否开启VIP通道
useVIPChannel = true
