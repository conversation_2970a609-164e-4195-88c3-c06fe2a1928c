package com.ideary.user.dto;

import jakarta.validation.constraints.Size;

import java.time.LocalDate;

/**
 * 用户详细资料更新请求DTO
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 2024-01-01
 */
public class UserProfileUpdateRequest {

    /**
     * 真实姓名
     */
    @Size(max = 50, message = "真实姓名长度不能超过50个字符")
    private String realName;

    /**
     * 性别：1-男，2-女，3-其他
     */
    private Integer gender;

    /**
     * 生日
     */
    private LocalDate birthday;

    /**
     * 个人简介
     */
    @Size(max = 500, message = "个人简介长度不能超过500个字符")
    private String bio;

    /**
     * 公司
     */
    @Size(max = 100, message = "公司名称长度不能超过100个字符")
    private String company;

    /**
     * 职位
     */
    @Size(max = 100, message = "职位名称长度不能超过100个字符")
    private String position;

    /**
     * 所在地
     */
    @Size(max = 100, message = "所在地长度不能超过100个字符")
    private String location;

    /**
     * 个人网站
     */
    @Size(max = 200, message = "个人网站URL长度不能超过200个字符")
    private String website;

    /**
     * GitHub用户名
     */
    @Size(max = 100, message = "GitHub用户名长度不能超过100个字符")
    private String github;

    /**
     * 微博用户名
     */
    @Size(max = 100, message = "微博用户名长度不能超过100个字符")
    private String weibo;

    /**
     * 微信号
     */
    @Size(max = 100, message = "微信号长度不能超过100个字符")
    private String wechat;

    // 构造函数
    public UserProfileUpdateRequest() {}

    // Getter和Setter方法
    public String getRealName() {
        return realName;
    }

    public void setRealName(String realName) {
        this.realName = realName;
    }

    public Integer getGender() {
        return gender;
    }

    public void setGender(Integer gender) {
        this.gender = gender;
    }

    public LocalDate getBirthday() {
        return birthday;
    }

    public void setBirthday(LocalDate birthday) {
        this.birthday = birthday;
    }

    public String getBio() {
        return bio;
    }

    public void setBio(String bio) {
        this.bio = bio;
    }

    public String getCompany() {
        return company;
    }

    public void setCompany(String company) {
        this.company = company;
    }

    public String getPosition() {
        return position;
    }

    public void setPosition(String position) {
        this.position = position;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public String getWebsite() {
        return website;
    }

    public void setWebsite(String website) {
        this.website = website;
    }

    public String getGithub() {
        return github;
    }

    public void setGithub(String github) {
        this.github = github;
    }

    public String getWeibo() {
        return weibo;
    }

    public void setWeibo(String weibo) {
        this.weibo = weibo;
    }

    public String getWechat() {
        return wechat;
    }

    public void setWechat(String wechat) {
        this.wechat = wechat;
    }

    @Override
    public String toString() {
        return "UserProfileUpdateRequest{" +
                "realName='" + realName + '\'' +
                ", gender=" + gender +
                ", birthday=" + birthday +
                ", bio='" + bio + '\'' +
                ", company='" + company + '\'' +
                ", position='" + position + '\'' +
                ", location='" + location + '\'' +
                ", website='" + website + '\'' +
                ", github='" + github + '\'' +
                ", weibo='" + weibo + '\'' +
                ", wechat='" + wechat + '\'' +
                '}';
    }
}
