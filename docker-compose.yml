version: '3.8'

services:
  # MySQL 数据库
  mysql:
    image: mysql:8.0
    container_name: ideary-mysql
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: root123456
      MYSQL_DATABASE: ideary
      MYSQL_USER: ideary
      MYSQL_PASSWORD: ideary123456
      TZ: Asia/Shanghai
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./infrastructure/mysql/conf:/etc/mysql/conf.d
      - ./infrastructure/mysql/init:/docker-entrypoint-initdb.d
    command: --default-authentication-plugin=mysql_native_password
    networks:
      - ideary-network

  # Redis 缓存
  redis:
    image: redis:7.0-alpine
    container_name: ideary-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
      - ./infrastructure/redis/redis.conf:/etc/redis/redis.conf
    command: redis-server /etc/redis/redis.conf
    networks:
      - ideary-network

  # Nacos 服务发现
  nacos:
    image: nacos/nacos-server:v2.3.0
    container_name: ideary-nacos
    restart: unless-stopped
    environment:
      MODE: standalone
      SPRING_DATASOURCE_PLATFORM: mysql
      MYSQL_SERVICE_HOST: mysql
      MYSQL_SERVICE_DB_NAME: nacos
      MYSQL_SERVICE_USER: ideary
      MYSQL_SERVICE_PASSWORD: ideary123456
      MYSQL_SERVICE_DB_PARAM: characterEncoding=utf8&connectTimeout=1000&socketTimeout=3000&autoReconnect=true&useSSL=false&allowPublicKeyRetrieval=true
      JVM_XMS: 256m
      JVM_XMX: 256m
    ports:
      - "8848:8848"
      - "9848:9848"
    volumes:
      - nacos_data:/home/<USER>/data
      - ./infrastructure/nacos/logs:/home/<USER>/logs
    depends_on:
      - mysql
    networks:
      - ideary-network

  # RabbitMQ 消息队列
  rabbitmq:
    image: rabbitmq:3.12-management-alpine
    container_name: ideary-rabbitmq
    restart: unless-stopped
    environment:
      RABBITMQ_DEFAULT_USER: ideary
      RABBITMQ_DEFAULT_PASS: ideary123456
    ports:
      - "5672:5672"
      - "15672:15672"
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq
    networks:
      - ideary-network

  # MinIO 对象存储
  minio:
    image: minio/minio:latest
    container_name: ideary-minio
    restart: unless-stopped
    environment:
      MINIO_ROOT_USER: ideary
      MINIO_ROOT_PASSWORD: ideary123456
    ports:
      - "9000:9000"
      - "9001:9001"
    volumes:
      - minio_data:/data
    command: server /data --console-address ":9001"
    networks:
      - ideary-network

  # Elasticsearch 搜索引擎
  elasticsearch:
    image: elasticsearch:8.11.0
    container_name: ideary-elasticsearch
    restart: unless-stopped
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    ports:
      - "9200:9200"
      - "9300:9300"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    networks:
      - ideary-network

  # Kibana 日志分析
  kibana:
    image: kibana:8.11.0
    container_name: ideary-kibana
    restart: unless-stopped
    environment:
      ELASTICSEARCH_HOSTS: http://elasticsearch:9200
    ports:
      - "5601:5601"
    depends_on:
      - elasticsearch
    networks:
      - ideary-network

  # Prometheus 监控
  prometheus:
    image: prom/prometheus:latest
    container_name: ideary-prometheus
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./infrastructure/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - ideary-network

  # Grafana 可视化
  grafana:
    image: grafana/grafana:latest
    container_name: ideary-grafana
    restart: unless-stopped
    environment:
      GF_SECURITY_ADMIN_USER: admin
      GF_SECURITY_ADMIN_PASSWORD: admin123456
    ports:
      - "3001:3000"
    volumes:
      - grafana_data:/var/lib/grafana
      - ./infrastructure/grafana/provisioning:/etc/grafana/provisioning
    depends_on:
      - prometheus
    networks:
      - ideary-network

  # Jaeger 链路追踪
  jaeger:
    image: jaegertracing/all-in-one:latest
    container_name: ideary-jaeger
    restart: unless-stopped
    environment:
      COLLECTOR_OTLP_ENABLED: true
    ports:
      - "16686:16686"
      - "14268:14268"
      - "14250:14250"
    networks:
      - ideary-network

  # API 网关
  gateway:
    build:
      context: ./ideary-backend/ideary-gateway
      dockerfile: Dockerfile
    container_name: ideary-gateway
    restart: unless-stopped
    environment:
      SPRING_PROFILES_ACTIVE: docker
      NACOS_SERVER_ADDR: nacos:8848
    ports:
      - "9080:9080"
    depends_on:
      - nacos
      - redis
    networks:
      - ideary-network

  # 用户服务
  user-service:
    build:
      context: ./ideary-backend/ideary-user
      dockerfile: Dockerfile
    container_name: ideary-user-service
    restart: unless-stopped
    environment:
      SPRING_PROFILES_ACTIVE: docker
      NACOS_SERVER_ADDR: nacos:8848
      MYSQL_HOST: mysql
      REDIS_HOST: redis
    ports:
      - "9081:9081"
    depends_on:
      - mysql
      - redis
      - nacos
    networks:
      - ideary-network

  # Vue 3 前端应用
  web:
    build:
      context: ./ideary-web
      dockerfile: Dockerfile
    container_name: ideary-web
    restart: unless-stopped
    environment:
      VITE_API_BASE_URL: http://localhost:9080/api/v1
      VITE_APP_TITLE: Ideary
      VITE_APP_VERSION: 1.0.0
      VITE_APP_ENV: production
    ports:
      - "3000:3000"
    depends_on:
      - gateway
    networks:
      - ideary-network

volumes:
  mysql_data:
  redis_data:
  nacos_data:
  rabbitmq_data:
  minio_data:
  elasticsearch_data:
  prometheus_data:
  grafana_data:

networks:
  ideary-network:
    driver: bridge
