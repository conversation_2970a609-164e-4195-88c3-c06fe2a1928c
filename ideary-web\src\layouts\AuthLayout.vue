<template>
  <div class="auth-layout">
    <!-- 背景装饰 -->
    <div class="auth-background">
      <div class="bg-pattern"></div>
      <div class="bg-gradient"></div>
    </div>
    
    <!-- 主要内容 -->
    <div class="auth-container">
      <!-- 左侧品牌区域 -->
      <div class="auth-brand">
        <div class="brand-content">
          <router-link to="/" class="brand-logo">
            <div class="logo-icon">
              <span>I</span>
            </div>
            <span class="logo-text">Ideary</span>
          </router-link>
          
          <h1 class="brand-title">技术灵感分享社区</h1>
          <p class="brand-description">
            汇聚优秀的技术文章、经验分享和创新思维，与志同道合的开发者一起成长
          </p>
          
          <!-- 特性列表 -->
          <div class="features">
            <div class="feature-item">
              <el-icon><Document /></el-icon>
              <span>优质技术文章</span>
            </div>
            <div class="feature-item">
              <el-icon><User /></el-icon>
              <span>活跃开发者社区</span>
            </div>
            <div class="feature-item">
              <el-icon><ChatDotRound /></el-icon>
              <span>深度技术讨论</span>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 右侧表单区域 -->
      <div class="auth-form">
        <div class="form-container">
          <router-view v-slot="{ Component, route }">
            <transition name="slide" mode="out-in">
              <component :is="Component" :key="route.path" />
            </transition>
          </router-view>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Document, User, ChatDotRound } from '@element-plus/icons-vue'
</script>

<style lang="scss" scoped>
.auth-layout {
  min-height: 100vh;
  position: relative;
  overflow: hidden;
}

.auth-background {
  position: absolute;
  inset: 0;
  z-index: 0;
  
  .bg-pattern {
    position: absolute;
    inset: 0;
    background-image: 
      radial-gradient(circle at 25% 25%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
      radial-gradient(circle at 75% 75%, rgba(16, 185, 129, 0.1) 0%, transparent 50%);
  }
  
  .bg-gradient {
    position: absolute;
    inset: 0;
    background: linear-gradient(135deg, 
      rgba(59, 130, 246, 0.05) 0%, 
      rgba(255, 255, 255, 0.8) 50%, 
      rgba(16, 185, 129, 0.05) 100%);
  }
}

.auth-container {
  position: relative;
  z-index: 1;
  min-height: 100vh;
  display: grid;
  grid-template-columns: 1fr 1fr;
  
  @media (max-width: 1024px) {
    grid-template-columns: 1fr;
  }
}

.auth-brand {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  background: linear-gradient(135deg, 
    rgba(59, 130, 246, 0.1) 0%, 
    rgba(255, 255, 255, 0.9) 100%);
  
  @media (max-width: 1024px) {
    display: none;
  }
  
  .brand-content {
    max-width: 400px;
    text-align: center;
  }
  
  .brand-logo {
    display: inline-flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 2rem;
    text-decoration: none;
    
    .logo-icon {
      width: 3rem;
      height: 3rem;
      background: linear-gradient(135deg, #3b82f6, #1d4ed8);
      border-radius: 0.75rem;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-weight: bold;
      font-size: 1.5rem;
    }
    
    .logo-text {
      font-size: 1.875rem;
      font-weight: bold;
      color: var(--el-text-color-primary);
    }
  }
  
  .brand-title {
    font-size: 2rem;
    font-weight: bold;
    color: var(--el-text-color-primary);
    margin-bottom: 1rem;
  }
  
  .brand-description {
    font-size: 1.125rem;
    color: var(--el-text-color-regular);
    line-height: 1.6;
    margin-bottom: 2rem;
  }
  
  .features {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    
    .feature-item {
      display: flex;
      align-items: center;
      gap: 0.75rem;
      padding: 0.75rem 1rem;
      background: rgba(255, 255, 255, 0.8);
      border-radius: 0.5rem;
      color: var(--el-text-color-regular);
      
      .el-icon {
        color: var(--el-color-primary);
        font-size: 1.25rem;
      }
    }
  }
}

.auth-form {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  
  .form-container {
    width: 100%;
    max-width: 400px;
  }
}

// 页面切换动画
.slide-enter-active,
.slide-leave-active {
  transition: all 0.3s ease;
}

.slide-enter-from {
  opacity: 0;
  transform: translateX(20px);
}

.slide-leave-to {
  opacity: 0;
  transform: translateX(-20px);
}

// 暗色模式适配
.dark {
  .auth-background {
    .bg-gradient {
      background: linear-gradient(135deg, 
        rgba(59, 130, 246, 0.1) 0%, 
        rgba(0, 0, 0, 0.8) 50%, 
        rgba(16, 185, 129, 0.1) 100%);
    }
  }
  
  .auth-brand {
    background: linear-gradient(135deg, 
      rgba(59, 130, 246, 0.2) 0%, 
      rgba(0, 0, 0, 0.9) 100%);
    
    .features .feature-item {
      background: rgba(0, 0, 0, 0.6);
    }
  }
  
  .auth-form {
    background: rgba(0, 0, 0, 0.8);
  }
}
</style>
