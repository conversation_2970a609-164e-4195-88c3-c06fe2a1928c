<template>
  <div class="user-followers">
    <el-skeleton v-if="loading" :rows="3" animated />
    
    <div v-else-if="followers.length > 0" class="followers-list">
      <div
        v-for="user in followers"
        :key="user.id"
        class="follower-item"
      >
        <div class="user-info">
          <el-avatar :src="user.avatar" :size="40">
            {{ user.nickname?.[0] || user.username?.[0] }}
          </el-avatar>
          <div class="user-details">
            <h4 class="user-name">
              <router-link :to="`/authors/${user.username}`">
                {{ user.nickname || user.username }}
              </router-link>
            </h4>
            <p class="user-bio">{{ user.bio }}</p>
          </div>
        </div>
        <el-button 
          :type="user.isFollowing ? 'default' : 'primary'"
          size="small" 
          @click="toggleFollow(user)"
        >
          {{ user.isFollowing ? '已关注' : '关注' }}
        </el-button>
      </div>
    </div>
    
    <div v-else class="empty-state">
      <el-empty description="暂无粉丝" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'

interface Props {
  userId?: number
}

const props = defineProps<Props>()

const loading = ref(true)
const followers = ref<any[]>([])

const toggleFollow = async (user: any) => {
  try {
    // 模拟关注/取消关注API
    await new Promise(resolve => setTimeout(resolve, 500))
    
    user.isFollowing = !user.isFollowing
    ElMessage.success(user.isFollowing ? '关注成功' : '取消关注成功')
  } catch (error) {
    ElMessage.error('操作失败，请重试')
  }
}

const fetchUserFollowers = async () => {
  try {
    loading.value = true
    await new Promise(resolve => setTimeout(resolve, 500))
    
    // 模拟数据
    followers.value = [
      {
        id: 2,
        username: 'vue_expert',
        nickname: 'Vue.js 专家',
        avatar: 'https://avatars.githubusercontent.com/u/2?v=4',
        bio: 'Vue.js 核心团队成员',
        isFollowing: false,
      }
    ]
  } catch (error) {
    console.error('获取粉丝列表失败:', error)
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  fetchUserFollowers()
})
</script>

<style lang="scss" scoped>
.user-followers {
  .followers-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
  }
  
  .follower-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    border: 1px solid var(--el-border-color-light);
    border-radius: 0.5rem;
    
    .user-info {
      display: flex;
      align-items: center;
      gap: 1rem;
      flex: 1;
      
      .user-details {
        .user-name {
          font-size: 1rem;
          font-weight: 600;
          margin-bottom: 0.25rem;
          
          a {
            color: var(--el-text-color-primary);
            text-decoration: none;
            
            &:hover {
              color: var(--el-color-primary);
            }
          }
        }
        
        .user-bio {
          font-size: 0.875rem;
          color: var(--el-text-color-regular);
          margin: 0;
        }
      }
    }
  }
  
  .empty-state {
    text-align: center;
    padding: 2rem 0;
  }
}
</style>
