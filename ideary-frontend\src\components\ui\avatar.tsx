'use client';

import React from 'react';
import Image from 'next/image';
import { cva, type VariantProps } from 'class-variance-authority';
import { cn } from '@/lib/utils';

const avatarVariants = cva(
  'relative inline-flex items-center justify-center overflow-hidden rounded-full bg-secondary-100 dark:bg-secondary-800',
  {
    variants: {
      size: {
        xs: 'h-6 w-6 text-xs',
        sm: 'h-8 w-8 text-sm',
        default: 'h-10 w-10 text-base',
        lg: 'h-12 w-12 text-lg',
        xl: 'h-16 w-16 text-xl',
        '2xl': 'h-20 w-20 text-2xl',
        '3xl': 'h-24 w-24 text-3xl',
      },
    },
    defaultVariants: {
      size: 'default',
    },
  }
);

export interface AvatarProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof avatarVariants> {
  src?: string | null;
  alt?: string;
  fallback?: string;
  online?: boolean;
}

export const Avatar = React.forwardRef<HTMLDivElement, AvatarProps>(
  ({ className, size, src, alt, fallback, online, ...props }, ref) => {
    const [imageError, setImageError] = React.useState(false);
    const [imageLoaded, setImageLoaded] = React.useState(false);

    // 生成fallback文本
    const getFallbackText = () => {
      if (fallback) return fallback;
      if (alt) {
        return alt
          .split(' ')
          .map(word => word.charAt(0))
          .join('')
          .toUpperCase()
          .slice(0, 2);
      }
      return '?';
    };

    const handleImageError = () => {
      setImageError(true);
    };

    const handleImageLoad = () => {
      setImageLoaded(true);
    };

    const showImage = src && !imageError;

    return (
      <div
        ref={ref}
        className={cn(avatarVariants({ size, className }))}
        {...props}
      >
        {showImage ? (
          <>
            <Image
              src={src}
              alt={alt || ''}
              fill
              className={cn(
                'object-cover transition-opacity duration-200',
                imageLoaded ? 'opacity-100' : 'opacity-0'
              )}
              onError={handleImageError}
              onLoad={handleImageLoad}
              sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
            />
            {!imageLoaded && (
              <div className="absolute inset-0 flex items-center justify-center bg-secondary-100 dark:bg-secondary-800">
                <div className="h-4 w-4 animate-spin rounded-full border-2 border-secondary-300 border-t-primary-600" />
              </div>
            )}
          </>
        ) : (
          <span className="font-medium text-secondary-600 dark:text-secondary-400">
            {getFallbackText()}
          </span>
        )}
        
        {/* 在线状态指示器 */}
        {online !== undefined && (
          <div
            className={cn(
              'absolute bottom-0 right-0 rounded-full border-2 border-white dark:border-secondary-900',
              {
                'h-2 w-2': size === 'xs' || size === 'sm',
                'h-3 w-3': size === 'default' || size === 'lg',
                'h-4 w-4': size === 'xl' || size === '2xl' || size === '3xl',
              },
              online ? 'bg-success-500' : 'bg-secondary-400'
            )}
          />
        )}
      </div>
    );
  }
);

Avatar.displayName = 'Avatar';
