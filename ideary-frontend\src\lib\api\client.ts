import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import { toast } from 'react-hot-toast';

/**
 * API响应接口
 */
interface ApiResponse<T = any> {
  code: number;
  message: string;
  data: T;
}

/**
 * 创建API客户端实例
 */
function createApiClient(): AxiosInstance {
  const client = axios.create({
    baseURL: process.env.NEXT_PUBLIC_API_URL || '/api/v1',
    timeout: 30000,
    headers: {
      'Content-Type': 'application/json',
    },
  });

  // 请求拦截器
  client.interceptors.request.use(
    (config) => {
      // 添加认证token
      const token = localStorage.getItem('accessToken');
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }

      // 添加用户ID到请求头
      const userStr = localStorage.getItem('user');
      if (userStr) {
        try {
          const user = JSON.parse(userStr);
          config.headers['X-User-Id'] = user.id;
        } catch (error) {
          console.error('解析用户信息失败:', error);
        }
      }

      // 添加请求ID用于追踪
      config.headers['X-Request-Id'] = generateRequestId();

      // 记录请求日志
      console.log(`[API Request] ${config.method?.toUpperCase()} ${config.url}`, {
        params: config.params,
        data: config.data,
      });

      return config;
    },
    (error) => {
      console.error('[API Request Error]', error);
      return Promise.reject(error);
    }
  );

  // 响应拦截器
  client.interceptors.response.use(
    (response: AxiosResponse<ApiResponse>) => {
      // 记录响应日志
      console.log(`[API Response] ${response.config.method?.toUpperCase()} ${response.config.url}`, {
        status: response.status,
        data: response.data,
      });

      // 检查业务状态码
      if (response.data.code !== 200) {
        const error = new Error(response.data.message || '请求失败');
        (error as any).code = response.data.code;
        throw error;
      }

      // 返回数据部分
      return {
        ...response,
        data: response.data.data,
      };
    },
    async (error) => {
      console.error('[API Response Error]', error);

      // 网络错误
      if (!error.response) {
        toast.error('网络连接失败，请检查网络设置');
        return Promise.reject(new Error('网络连接失败'));
      }

      const { status, data } = error.response;

      // 处理不同的HTTP状态码
      switch (status) {
        case 401:
          // 未授权，尝试刷新token
          const refreshToken = localStorage.getItem('refreshToken');
          if (refreshToken && !error.config._retry) {
            error.config._retry = true;
            try {
              const response = await client.post('/auth/refresh', { refreshToken });
              const newToken = response.data.accessToken;
              localStorage.setItem('accessToken', newToken);
              
              // 重新发送原请求
              error.config.headers.Authorization = `Bearer ${newToken}`;
              return client.request(error.config);
            } catch (refreshError) {
              // 刷新失败，清除认证信息并跳转到登录页
              localStorage.removeItem('accessToken');
              localStorage.removeItem('refreshToken');
              localStorage.removeItem('user');
              window.location.href = '/auth/login';
              return Promise.reject(new Error('登录已过期，请重新登录'));
            }
          } else {
            // 没有刷新token或刷新失败
            localStorage.removeItem('accessToken');
            localStorage.removeItem('refreshToken');
            localStorage.removeItem('user');
            window.location.href = '/auth/login';
            return Promise.reject(new Error('登录已过期，请重新登录'));
          }

        case 403:
          toast.error('没有权限访问该资源');
          return Promise.reject(new Error('没有权限访问该资源'));

        case 404:
          toast.error('请求的资源不存在');
          return Promise.reject(new Error('请求的资源不存在'));

        case 422:
          // 表单验证错误
          const validationErrors = data?.errors || {};
          const firstError = Object.values(validationErrors)[0];
          const errorMessage = Array.isArray(firstError) ? firstError[0] : firstError || '请求参数错误';
          toast.error(errorMessage as string);
          return Promise.reject(new Error(errorMessage as string));

        case 429:
          toast.error('请求过于频繁，请稍后再试');
          return Promise.reject(new Error('请求过于频繁，请稍后再试'));

        case 500:
          toast.error('服务器内部错误，请稍后再试');
          return Promise.reject(new Error('服务器内部错误'));

        case 502:
        case 503:
        case 504:
          toast.error('服务暂时不可用，请稍后再试');
          return Promise.reject(new Error('服务暂时不可用'));

        default:
          const errorMessage = data?.message || `请求失败 (${status})`;
          toast.error(errorMessage);
          return Promise.reject(new Error(errorMessage));
      }
    }
  );

  return client;
}

/**
 * 生成请求ID
 */
function generateRequestId(): string {
  return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
}

/**
 * API客户端实例
 */
export const apiClient = createApiClient();

/**
 * 通用请求方法
 */
export const api = {
  get: <T = any>(url: string, config?: AxiosRequestConfig) => 
    apiClient.get<T>(url, config),
  
  post: <T = any>(url: string, data?: any, config?: AxiosRequestConfig) => 
    apiClient.post<T>(url, data, config),
  
  put: <T = any>(url: string, data?: any, config?: AxiosRequestConfig) => 
    apiClient.put<T>(url, data, config),
  
  patch: <T = any>(url: string, data?: any, config?: AxiosRequestConfig) => 
    apiClient.patch<T>(url, data, config),
  
  delete: <T = any>(url: string, config?: AxiosRequestConfig) => 
    apiClient.delete<T>(url, config),
};

/**
 * 文件上传
 */
export async function uploadFile(file: File, onProgress?: (progress: number) => void): Promise<string> {
  const formData = new FormData();
  formData.append('file', file);

  const response = await apiClient.post('/upload', formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
    onUploadProgress: (progressEvent) => {
      if (onProgress && progressEvent.total) {
        const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total);
        onProgress(progress);
      }
    },
  });

  return response.data.url;
}

/**
 * 批量上传文件
 */
export async function uploadFiles(
  files: File[], 
  onProgress?: (progress: number) => void
): Promise<string[]> {
  const formData = new FormData();
  files.forEach((file, index) => {
    formData.append(`files[${index}]`, file);
  });

  const response = await apiClient.post('/upload/batch', formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
    onUploadProgress: (progressEvent) => {
      if (onProgress && progressEvent.total) {
        const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total);
        onProgress(progress);
      }
    },
  });

  return response.data.urls;
}

/**
 * 下载文件
 */
export async function downloadFile(url: string, filename?: string): Promise<void> {
  const response = await apiClient.get(url, {
    responseType: 'blob',
  });

  const blob = new Blob([response.data]);
  const downloadUrl = window.URL.createObjectURL(blob);
  
  const link = document.createElement('a');
  link.href = downloadUrl;
  link.download = filename || 'download';
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  
  window.URL.revokeObjectURL(downloadUrl);
}
