@tailwind base;
@tailwind components;
@tailwind utilities;

/* 导入字体 */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@100;200;300;400;500;600;700;800&display=swap');

/* 基础样式 */
@layer base {
  * {
    @apply border-border;
  }
  
  html {
    @apply scroll-smooth;
  }
  
  body {
    @apply bg-background text-foreground font-sans antialiased;
    font-feature-settings: 'rlig' 1, 'calt' 1;
  }
  
  /* 标题样式 */
  h1, h2, h3, h4, h5, h6 {
    @apply font-heading font-semibold tracking-tight;
  }
  
  h1 {
    @apply text-4xl lg:text-5xl;
  }
  
  h2 {
    @apply text-3xl lg:text-4xl;
  }
  
  h3 {
    @apply text-2xl lg:text-3xl;
  }
  
  h4 {
    @apply text-xl lg:text-2xl;
  }
  
  h5 {
    @apply text-lg lg:text-xl;
  }
  
  h6 {
    @apply text-base lg:text-lg;
  }
  
  /* 链接样式 */
  a {
    @apply text-primary-600 hover:text-primary-700 transition-colors duration-200;
  }
  
  /* 代码样式 */
  code {
    @apply font-mono text-sm bg-secondary-100 dark:bg-secondary-800 px-1.5 py-0.5 rounded;
  }
  
  pre {
    @apply font-mono text-sm bg-secondary-100 dark:bg-secondary-800 p-4 rounded-lg overflow-x-auto;
  }
  
  pre code {
    @apply bg-transparent p-0;
  }
  
  /* 引用样式 */
  blockquote {
    @apply border-l-4 border-primary-500 pl-4 italic text-secondary-600 dark:text-secondary-400;
  }
  
  /* 表格样式 */
  table {
    @apply w-full border-collapse;
  }
  
  th, td {
    @apply border border-secondary-200 dark:border-secondary-700 px-4 py-2 text-left;
  }
  
  th {
    @apply bg-secondary-50 dark:bg-secondary-800 font-semibold;
  }
  
  /* 表单样式 */
  input, textarea, select {
    @apply border border-secondary-300 dark:border-secondary-600 rounded-md px-3 py-2 
           bg-white dark:bg-secondary-800 text-secondary-900 dark:text-secondary-100
           focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent
           transition-colors duration-200;
  }
  
  input:disabled, textarea:disabled, select:disabled {
    @apply bg-secondary-100 dark:bg-secondary-700 text-secondary-500 cursor-not-allowed;
  }
  
  /* 按钮基础样式 */
  button {
    @apply font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2;
  }
  
  /* 滚动条样式 */
  ::-webkit-scrollbar {
    @apply w-2 h-2;
  }
  
  ::-webkit-scrollbar-track {
    @apply bg-secondary-100 dark:bg-secondary-800;
  }
  
  ::-webkit-scrollbar-thumb {
    @apply bg-secondary-300 dark:bg-secondary-600 rounded-full;
  }
  
  ::-webkit-scrollbar-thumb:hover {
    @apply bg-secondary-400 dark:bg-secondary-500;
  }
  
  /* 选择文本样式 */
  ::selection {
    @apply bg-primary-100 dark:bg-primary-900 text-primary-900 dark:text-primary-100;
  }
  
  /* 占位符样式 */
  ::placeholder {
    @apply text-secondary-400 dark:text-secondary-500;
  }
}

/* 组件样式 */
@layer components {
  /* 按钮变体 */
  .btn {
    @apply inline-flex items-center justify-center rounded-md px-4 py-2 text-sm font-medium 
           transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2
           disabled:opacity-50 disabled:cursor-not-allowed;
  }
  
  .btn-primary {
    @apply bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500;
  }
  
  .btn-secondary {
    @apply bg-secondary-600 text-white hover:bg-secondary-700 focus:ring-secondary-500;
  }
  
  .btn-outline {
    @apply border border-primary-600 text-primary-600 hover:bg-primary-600 hover:text-white 
           focus:ring-primary-500;
  }
  
  .btn-ghost {
    @apply text-primary-600 hover:bg-primary-50 dark:hover:bg-primary-900/20 focus:ring-primary-500;
  }
  
  .btn-danger {
    @apply bg-error-600 text-white hover:bg-error-700 focus:ring-error-500;
  }
  
  .btn-success {
    @apply bg-success-600 text-white hover:bg-success-700 focus:ring-success-500;
  }
  
  /* 按钮尺寸 */
  .btn-sm {
    @apply px-3 py-1.5 text-xs;
  }
  
  .btn-lg {
    @apply px-6 py-3 text-base;
  }
  
  .btn-xl {
    @apply px-8 py-4 text-lg;
  }
  
  /* 卡片样式 */
  .card {
    @apply bg-white dark:bg-secondary-800 rounded-lg shadow-soft border border-secondary-200 dark:border-secondary-700;
  }
  
  .card-header {
    @apply px-6 py-4 border-b border-secondary-200 dark:border-secondary-700;
  }
  
  .card-body {
    @apply px-6 py-4;
  }
  
  .card-footer {
    @apply px-6 py-4 border-t border-secondary-200 dark:border-secondary-700;
  }
  
  /* 徽章样式 */
  .badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }
  
  .badge-primary {
    @apply bg-primary-100 text-primary-800 dark:bg-primary-900 dark:text-primary-200;
  }
  
  .badge-secondary {
    @apply bg-secondary-100 text-secondary-800 dark:bg-secondary-700 dark:text-secondary-200;
  }
  
  .badge-success {
    @apply bg-success-100 text-success-800 dark:bg-success-900 dark:text-success-200;
  }
  
  .badge-warning {
    @apply bg-warning-100 text-warning-800 dark:bg-warning-900 dark:text-warning-200;
  }
  
  .badge-error {
    @apply bg-error-100 text-error-800 dark:bg-error-900 dark:text-error-200;
  }
  
  /* 输入框样式 */
  .input {
    @apply w-full px-3 py-2 border border-secondary-300 dark:border-secondary-600 rounded-md
           bg-white dark:bg-secondary-800 text-secondary-900 dark:text-secondary-100
           placeholder-secondary-400 dark:placeholder-secondary-500
           focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent
           transition-colors duration-200;
  }
  
  .input-error {
    @apply border-error-500 focus:ring-error-500;
  }
  
  /* 加载动画 */
  .loading {
    @apply animate-spin rounded-full border-2 border-secondary-200 border-t-primary-600;
  }
  
  /* 渐变背景 */
  .gradient-bg {
    @apply bg-gradient-to-br from-primary-50 via-white to-secondary-50 
           dark:from-secondary-900 dark:via-secondary-800 dark:to-secondary-900;
  }
  
  /* 文本渐变 */
  .gradient-text {
    @apply bg-gradient-to-r from-primary-600 to-secondary-600 bg-clip-text text-transparent;
  }
  
  /* 容器样式 */
  .container-sm {
    @apply max-w-2xl mx-auto px-4 sm:px-6;
  }
  
  .container-md {
    @apply max-w-4xl mx-auto px-4 sm:px-6;
  }
  
  .container-lg {
    @apply max-w-6xl mx-auto px-4 sm:px-6;
  }
  
  .container-xl {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }
}

/* 工具类 */
@layer utilities {
  /* 文本省略 */
  .text-ellipsis {
    @apply truncate;
  }
  
  .text-ellipsis-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
  
  .text-ellipsis-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
  
  /* 隐藏滚动条 */
  .hide-scrollbar {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
  
  .hide-scrollbar::-webkit-scrollbar {
    display: none;
  }
  
  /* 玻璃效果 */
  .glass {
    @apply bg-white/80 dark:bg-secondary-800/80 backdrop-blur-sm;
  }
  
  /* 阴影变体 */
  .shadow-glow {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.15);
  }
  
  .shadow-glow-lg {
    box-shadow: 0 0 40px rgba(59, 130, 246, 0.15);
  }
  
  /* 动画延迟 */
  .animation-delay-100 {
    animation-delay: 100ms;
  }
  
  .animation-delay-200 {
    animation-delay: 200ms;
  }
  
  .animation-delay-300 {
    animation-delay: 300ms;
  }
  
  .animation-delay-500 {
    animation-delay: 500ms;
  }
}
