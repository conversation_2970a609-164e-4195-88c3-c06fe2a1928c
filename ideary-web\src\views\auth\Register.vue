<template>
  <div class="register-page">
    <div class="register-form">
      <div class="form-header">
        <h1 class="form-title">加入 Ideary</h1>
        <p class="form-subtitle">创建您的账户，开始技术分享之旅</p>
      </div>

      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        @submit.prevent="handleSubmit"
        class="register-form-content"
        size="large"
      >
        <el-form-item prop="username">
          <el-input
            v-model="form.username"
            placeholder="用户名"
            :prefix-icon="User"
            clearable
          />
        </el-form-item>

        <el-form-item prop="email">
          <el-input
            v-model="form.email"
            type="email"
            placeholder="邮箱地址"
            :prefix-icon="Message"
            clearable
          />
        </el-form-item>

        <el-form-item prop="emailCode">
          <div class="code-input-group">
            <el-input
              v-model="form.emailCode"
              placeholder="邮箱验证码"
              :prefix-icon="Key"
              clearable
            />
            <el-button
              :disabled="codeCountdown > 0 || !form.email"
              @click="sendEmailCode"
              :loading="sendingCode"
              class="code-btn"
            >
              {{ codeCountdown > 0 ? `${codeCountdown}s` : '发送验证码' }}
            </el-button>
          </div>
        </el-form-item>

        <el-form-item prop="password">
          <el-input
            v-model="form.password"
            type="password"
            placeholder="密码"
            :prefix-icon="Lock"
            show-password
            clearable
          />
        </el-form-item>

        <el-form-item prop="confirmPassword">
          <el-input
            v-model="form.confirmPassword"
            type="password"
            placeholder="确认密码"
            :prefix-icon="Lock"
            show-password
            clearable
          />
        </el-form-item>

        <el-form-item prop="nickname">
          <el-input
            v-model="form.nickname"
            placeholder="昵称（可选）"
            :prefix-icon="UserFilled"
            clearable
          />
        </el-form-item>

        <el-form-item prop="agreeTerms">
          <el-checkbox v-model="form.agreeTerms">
            我已阅读并同意
            <a href="/terms" target="_blank" class="terms-link">《服务条款》</a>
            和
            <a href="/privacy" target="_blank" class="terms-link">《隐私政策》</a>
          </el-checkbox>
        </el-form-item>

        <el-form-item>
          <el-button
            type="primary"
            native-type="submit"
            :loading="loading"
            class="submit-btn"
          >
            {{ loading ? '注册中...' : '创建账户' }}
          </el-button>
        </el-form-item>
      </el-form>

      <div class="form-footer">
        <p class="login-link">
          已有账户？
          <router-link to="/auth/login">立即登录</router-link>
        </p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { User, Message, Key, Lock, UserFilled } from '@element-plus/icons-vue'
import { useUserStore } from '@/stores/user'
import { authApi } from '@/api/auth'
import type { RegisterRequest } from '@/types/auth'

const router = useRouter()
const userStore = useUserStore()

// 表单引用
const formRef = ref<FormInstance>()

// 加载状态
const loading = ref(false)
const sendingCode = ref(false)
const codeCountdown = ref(0)

// 表单数据
const form = reactive<RegisterRequest>({
  username: '',
  email: '',
  emailCode: '',
  password: '',
  confirmPassword: '',
  nickname: '',
  agreeTerms: false
})

// 自定义验证器
const validatePassword = (rule: any, value: string, callback: any) => {
  if (value === '') {
    callback(new Error('请输入密码'))
  } else if (value.length < 6) {
    callback(new Error('密码至少6个字符'))
  } else if (!/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(value)) {
    callback(new Error('密码必须包含大小写字母和数字'))
  } else {
    callback()
  }
}

const validateConfirmPassword = (rule: any, value: string, callback: any) => {
  if (value === '') {
    callback(new Error('请确认密码'))
  } else if (value !== form.password) {
    callback(new Error('两次输入的密码不一致'))
  } else {
    callback()
  }
}

const validateUsername = async (rule: any, value: string, callback: any) => {
  if (value === '') {
    callback(new Error('请输入用户名'))
  } else if (value.length < 3) {
    callback(new Error('用户名至少3个字符'))
  } else if (!/^[a-zA-Z0-9_]+$/.test(value)) {
    callback(new Error('用户名只能包含字母、数字和下划线'))
  } else {
    try {
      const result = await authApi.checkUsername(value)
      if (!result.available) {
        callback(new Error('用户名已被使用'))
      } else {
        callback()
      }
    } catch (error) {
      callback()
    }
  }
}

const validateEmail = async (rule: any, value: string, callback: any) => {
  if (value === '') {
    callback(new Error('请输入邮箱地址'))
  } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) {
    callback(new Error('请输入有效的邮箱地址'))
  } else {
    try {
      const result = await authApi.checkEmail(value)
      if (!result.available) {
        callback(new Error('邮箱已被注册'))
      } else {
        callback()
      }
    } catch (error) {
      callback()
    }
  }
}

// 表单验证规则
const rules: FormRules = {
  username: [
    { validator: validateUsername, trigger: 'blur' }
  ],
  email: [
    { validator: validateEmail, trigger: 'blur' }
  ],
  emailCode: [
    { required: true, message: '请输入邮箱验证码', trigger: 'blur' },
    { len: 6, message: '验证码为6位数字', trigger: 'blur' }
  ],
  password: [
    { validator: validatePassword, trigger: 'blur' }
  ],
  confirmPassword: [
    { validator: validateConfirmPassword, trigger: 'blur' }
  ],
  agreeTerms: [
    { 
      validator: (rule: any, value: boolean, callback: any) => {
        if (!value) {
          callback(new Error('请同意服务条款和隐私政策'))
        } else {
          callback()
        }
      }, 
      trigger: 'change' 
    }
  ]
}

// 发送邮箱验证码
const sendEmailCode = async () => {
  if (!form.email) {
    ElMessage.warning('请先输入邮箱地址')
    return
  }

  try {
    sendingCode.value = true
    await authApi.sendEmailCode({
      email: form.email,
      type: 'register'
    })
    
    ElMessage.success('验证码已发送到您的邮箱')
    
    // 开始倒计时
    codeCountdown.value = 60
    const timer = setInterval(() => {
      codeCountdown.value--
      if (codeCountdown.value <= 0) {
        clearInterval(timer)
      }
    }, 1000)
    
  } catch (error: any) {
    ElMessage.error(error.message || '发送验证码失败')
  } finally {
    sendingCode.value = false
  }
}

// 处理表单提交
const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    loading.value = true
    
    await userStore.register(form)
    
    ElMessage.success('注册成功！欢迎加入 Ideary！')
    
    // 跳转到首页
    router.push('/')
    
  } catch (error: any) {
    console.error('注册失败:', error)
    ElMessage.error(error.message || '注册失败，请重试')
  } finally {
    loading.value = false
  }
}

// 页面标题
onMounted(() => {
  document.title = '注册 - Ideary'
})
</script>

<style lang="scss" scoped>
.register-page {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
}

.register-form {
  width: 100%;
  max-width: 400px;
  
  .form-header {
    text-align: center;
    margin-bottom: 2rem;
    
    .form-title {
      font-size: 2rem;
      font-weight: bold;
      color: var(--el-text-color-primary);
      margin-bottom: 0.5rem;
    }
    
    .form-subtitle {
      color: var(--el-text-color-regular);
      font-size: 1rem;
    }
  }
  
  .register-form-content {
    .code-input-group {
      display: flex;
      gap: 0.75rem;
      
      .el-input {
        flex: 1;
      }
      
      .code-btn {
        white-space: nowrap;
        min-width: 100px;
      }
    }
    
    .terms-link {
      color: var(--el-color-primary);
      text-decoration: none;
      
      &:hover {
        text-decoration: underline;
      }
    }
    
    .submit-btn {
      width: 100%;
      height: 48px;
      font-size: 1rem;
      font-weight: 500;
    }
  }
  
  .form-footer {
    text-align: center;
    margin-top: 1.5rem;
    
    .login-link {
      color: var(--el-text-color-regular);
      font-size: 0.875rem;
      
      a {
        color: var(--el-color-primary);
        text-decoration: none;
        font-weight: 500;
        
        &:hover {
          text-decoration: underline;
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 480px) {
  .register-page {
    padding: 1rem;
  }
  
  .register-form {
    .form-header {
      .form-title {
        font-size: 1.75rem;
      }
    }
    
    .register-form-content {
      .code-input-group {
        flex-direction: column;
        
        .code-btn {
          width: 100%;
        }
      }
    }
  }
}
</style>
