{"semi": false, "singleQuote": true, "tabWidth": 2, "trailingComma": "es5", "useTabs": false, "printWidth": 100, "bracketSpacing": true, "bracketSameLine": false, "arrowParens": "avoid", "endOfLine": "lf", "htmlWhitespaceSensitivity": "css", "insertPragma": false, "jsxSingleQuote": true, "proseWrap": "preserve", "quoteProps": "as-needed", "requirePragma": false, "vueIndentScriptAndStyle": false, "embeddedLanguageFormatting": "auto"}