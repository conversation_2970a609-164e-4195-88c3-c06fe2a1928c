import axios, { type AxiosInstance, type AxiosRequestConfig, type AxiosResponse } from 'axios'
import { ElMessage } from 'element-plus'
import { useUserStore } from '@/stores/user'

/**
 * API响应接口
 */
interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
}

/**
 * 创建API客户端实例
 */
function createApiClient(): AxiosInstance {
  const client = axios.create({
    baseURL: import.meta.env.VITE_API_BASE_URL || '/api/v1',
    timeout: 30000,
    headers: {
      'Content-Type': 'application/json',
    },
  })

  // 请求拦截器
  client.interceptors.request.use(
    config => {
      // 添加认证token
      const userStore = useUserStore()
      if (userStore.token) {
        config.headers.Authorization = `Bearer ${userStore.token}`
      }

      // 添加请求ID用于追踪
      config.headers['X-Request-Id'] = generateRequestId()

      // 记录请求日志
      console.log(`[API Request] ${config.method?.toUpperCase()} ${config.url}`, {
        params: config.params,
        data: config.data,
      })

      return config
    },
    error => {
      console.error('[API Request Error]', error)
      return Promise.reject(error)
    }
  )

  // 响应拦截器
  client.interceptors.response.use(
    (response: AxiosResponse<ApiResponse>) => {
      // 记录响应日志
      console.log(
        `[API Response] ${response.config.method?.toUpperCase()} ${response.config.url}`,
        {
          status: response.status,
          data: response.data,
        }
      )

      // 检查业务状态码
      if (response.data.code !== 200) {
        const error = new Error(response.data.message || '请求失败')
        ;(error as any).code = response.data.code
        throw error
      }

      // 返回数据部分
      return {
        ...response,
        data: response.data.data,
      }
    },
    async error => {
      console.error('[API Response Error]', error)

      // 网络错误
      if (!error.response) {
        ElMessage.error('网络连接失败，请检查网络设置')
        return Promise.reject(new Error('网络连接失败'))
      }

      const { status, data } = error.response
      const userStore = useUserStore()

      // 处理不同的HTTP状态码
      switch (status) {
        case 401:
          // 未授权，尝试刷新token
          if (userStore.refreshToken && !error.config._retry) {
            error.config._retry = true
            try {
              const newToken = await userStore.refreshAccessToken()

              // 重新发送原请求
              error.config.headers.Authorization = `Bearer ${newToken}`
              return client.request(error.config)
            } catch (refreshError) {
              // 刷新失败，清除认证信息并跳转到登录页
              userStore.clearUser()
              window.location.href = '/auth/login'
              return Promise.reject(new Error('登录已过期，请重新登录'))
            }
          } else {
            // 没有刷新token或刷新失败
            userStore.clearUser()
            window.location.href = '/auth/login'
            return Promise.reject(new Error('登录已过期，请重新登录'))
          }

        case 403:
          ElMessage.error('没有权限访问该资源')
          return Promise.reject(new Error('没有权限访问该资源'))

        case 404:
          ElMessage.error('请求的资源不存在')
          return Promise.reject(new Error('请求的资源不存在'))

        case 422:
          // 表单验证错误
          const validationErrors = data?.errors || {}
          const firstError = Object.values(validationErrors)[0]
          const errorMessage = Array.isArray(firstError)
            ? firstError[0]
            : firstError || '请求参数错误'
          ElMessage.error(errorMessage as string)
          return Promise.reject(new Error(errorMessage as string))

        case 429:
          ElMessage.error('请求过于频繁，请稍后再试')
          return Promise.reject(new Error('请求过于频繁，请稍后再试'))

        case 500:
          ElMessage.error('服务器内部错误，请稍后再试')
          return Promise.reject(new Error('服务器内部错误'))

        case 502:
        case 503:
        case 504:
          ElMessage.error('服务暂时不可用，请稍后再试')
          return Promise.reject(new Error('服务暂时不可用'))

        default:
          const defaultErrorMessage = data?.message || `请求失败 (${status})`
          ElMessage.error(defaultErrorMessage)
          return Promise.reject(new Error(defaultErrorMessage))
      }
    }
  )

  return client
}

/**
 * 生成请求ID
 */
function generateRequestId(): string {
  return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
}

/**
 * API客户端实例
 */
export const apiClient = createApiClient()

/**
 * 通用请求方法
 */
export const request = {
  get: <T = any>(url: string, config?: AxiosRequestConfig) => apiClient.get<T>(url, config),

  post: <T = any>(url: string, data?: any, config?: AxiosRequestConfig) =>
    apiClient.post<T>(url, data, config),

  put: <T = any>(url: string, data?: any, config?: AxiosRequestConfig) =>
    apiClient.put<T>(url, data, config),

  patch: <T = any>(url: string, data?: any, config?: AxiosRequestConfig) =>
    apiClient.patch<T>(url, data, config),

  delete: <T = any>(url: string, config?: AxiosRequestConfig) => apiClient.delete<T>(url, config),
}

/**
 * 文件上传
 */
export async function uploadFile(
  file: File,
  onProgress?: (progress: number) => void
): Promise<string> {
  const formData = new FormData()
  formData.append('file', file)

  const response = await apiClient.post('/upload', formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
    onUploadProgress: progressEvent => {
      if (onProgress && progressEvent.total) {
        const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total)
        onProgress(progress)
      }
    },
  })

  return response.data.url
}
