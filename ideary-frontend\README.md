# Ideary Frontend

Ideary 技术灵感分享社区的前端应用，基于 Next.js 14 和 React 18 构建。

## 🚀 技术栈

- **框架**: Next.js 14 (App Router)
- **UI库**: React 18
- **语言**: TypeScript 5.0
- **样式**: Tailwind CSS 3.3
- **状态管理**: Zustand
- **HTTP客户端**: Axios + SWR
- **表单处理**: React Hook Form + Zod
- **动画**: Framer Motion
- **图标**: Heroicons
- **主题**: next-themes
- **代码规范**: ESLint + Prettier

## 📁 项目结构

```
src/
├── app/                    # Next.js App Router
│   ├── layout.tsx         # 根布局
│   ├── page.tsx           # 首页
│   ├── auth/              # 认证页面
│   ├── articles/          # 文章页面
│   ├── profile/           # 用户资料页面
│   └── ...
├── components/            # React 组件
│   ├── ui/               # 基础 UI 组件
│   ├── layout/           # 布局组件
│   ├── sections/         # 页面区块组件
│   ├── modals/           # 模态框组件
│   └── providers/        # Context 提供者
├── lib/                  # 工具库
│   ├── api/              # API 客户端
│   ├── utils.ts          # 工具函数
│   └── constants.ts      # 常量定义
├── hooks/                # 自定义 Hooks
├── store/                # 状态管理
├── types/                # TypeScript 类型定义
└── styles/               # 样式文件
    └── globals.css       # 全局样式
```

## 🛠️ 开发指南

### 环境要求
- Node.js 18+
- npm 8+

### 安装依赖
```bash
npm install
```

### 环境配置
复制环境变量文件并配置：
```bash
cp .env.example .env.local
```

### 启动开发服务器
```bash
npm run dev
```

### 构建生产版本
```bash
npm run build
```

### 启动生产服务器
```bash
npm run start
```

## 📝 开发规范

### 代码规范
- 使用 TypeScript 进行类型检查
- 遵循 ESLint 和 Prettier 规则
- 组件名使用 PascalCase
- 文件名使用 kebab-case
- 函数和变量使用 camelCase

### 组件开发
- 使用函数组件和 Hooks
- 优先使用 TypeScript 接口定义 props
- 组件应该是纯函数，避免副作用
- 使用 forwardRef 处理 ref 传递

### 样式规范
- 使用 Tailwind CSS 工具类
- 避免内联样式
- 使用 CSS 变量定义主题色彩
- 响应式设计优先

### 状态管理
- 使用 Zustand 管理全局状态
- 使用 SWR 管理服务器状态
- 本地状态优先使用 useState

## 🧪 测试

### 运行测试
```bash
npm run test
```

### 测试覆盖率
```bash
npm run test:coverage
```

### 端到端测试
```bash
npm run test:e2e
```

## 📦 构建和部署

### 构建优化
- 自动代码分割
- 图片优化
- 字体优化
- CSS 压缩

### 部署选项
- Vercel (推荐)
- Netlify
- 自托管 (Docker)

## 🔧 可用脚本

- `npm run dev` - 启动开发服务器
- `npm run build` - 构建生产版本
- `npm run start` - 启动生产服务器
- `npm run lint` - 代码检查
- `npm run lint:fix` - 自动修复代码问题
- `npm run type-check` - TypeScript 类型检查
- `npm run test` - 运行测试
- `npm run test:watch` - 监听模式运行测试
- `npm run test:coverage` - 生成测试覆盖率报告

## 🌐 环境变量

```bash
# API 配置
NEXT_PUBLIC_API_URL=http://localhost:9080/api/v1
NEXT_PUBLIC_WS_URL=ws://localhost:9080/ws

# 应用配置
NEXT_PUBLIC_APP_NAME=Ideary
NEXT_PUBLIC_APP_VERSION=1.0.0
NEXT_PUBLIC_APP_ENV=development

# 功能开关
NEXT_PUBLIC_ENABLE_PWA=true
NEXT_PUBLIC_ENABLE_ANALYTICS=false
```

## 🎨 设计系统

### 颜色主题
- Primary: 蓝色系 (#3b82f6)
- Secondary: 灰色系 (#64748b)
- Success: 绿色系 (#10b981)
- Warning: 黄色系 (#f59e0b)
- Error: 红色系 (#ef4444)

### 字体
- 主字体: Inter
- 代码字体: JetBrains Mono

### 断点
- xs: 475px
- sm: 640px
- md: 768px
- lg: 1024px
- xl: 1280px
- 2xl: 1536px

## 🔍 性能优化

### 图片优化
- 使用 Next.js Image 组件
- 支持 WebP 和 AVIF 格式
- 自动响应式图片

### 代码分割
- 路由级别代码分割
- 组件级别懒加载
- 第三方库按需加载

### 缓存策略
- 静态资源长期缓存
- API 响应智能缓存
- 页面增量静态生成

## 📱 PWA 支持

- 离线访问
- 应用安装
- 推送通知
- 后台同步

## 🌍 国际化

- 支持中文和英文
- 动态语言切换
- 时区自动检测

## 🔒 安全性

- XSS 防护
- CSRF 防护
- 内容安全策略
- 安全头部配置

## 📊 监控和分析

- 性能监控
- 错误追踪
- 用户行为分析
- 核心 Web 指标

## 🤝 贡献指南

请参考项目根目录的 [CONTRIBUTING.md](../CONTRIBUTING.md) 文件。

## 📄 许可证

MIT License - 查看 [LICENSE](../LICENSE) 文件了解详情。
