package com.ideary.user.util;

import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Component;

/**
 * 密码工具类
 * 
 * 功能说明：
 * 1. 密码加密
 * 2. 密码验证
 * 3. 密码强度检查
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 2024-01-01
 */
@Component
public class PasswordUtil {

    private final PasswordEncoder passwordEncoder;

    public PasswordUtil() {
        this.passwordEncoder = new BCryptPasswordEncoder();
    }

    /**
     * 加密密码
     *
     * @param rawPassword 原始密码
     * @return 加密后的密码
     */
    public String encode(String rawPassword) {
        return passwordEncoder.encode(rawPassword);
    }

    /**
     * 验证密码
     *
     * @param rawPassword 原始密码
     * @param encodedPassword 加密后的密码
     * @return 是否匹配
     */
    public boolean matches(String rawPassword, String encodedPassword) {
        return passwordEncoder.matches(rawPassword, encodedPassword);
    }

    /**
     * 检查密码强度
     *
     * @param password 密码
     * @return 密码强度等级（1-5）
     */
    public int checkPasswordStrength(String password) {
        if (password == null || password.length() < 6) {
            return 1; // 很弱
        }

        int score = 0;
        
        // 长度检查
        if (password.length() >= 8) score++;
        if (password.length() >= 12) score++;
        
        // 字符类型检查
        if (password.matches(".*[a-z].*")) score++; // 包含小写字母
        if (password.matches(".*[A-Z].*")) score++; // 包含大写字母
        if (password.matches(".*\\d.*")) score++; // 包含数字
        if (password.matches(".*[!@#$%^&*()_+\\-=\\[\\]{};':\"\\\\|,.<>\\/?].*")) score++; // 包含特殊字符
        
        // 返回强度等级
        return Math.min(5, Math.max(1, score));
    }

    /**
     * 获取密码强度描述
     *
     * @param strength 密码强度等级
     * @return 强度描述
     */
    public String getPasswordStrengthDescription(int strength) {
        return switch (strength) {
            case 1 -> "很弱";
            case 2 -> "弱";
            case 3 -> "一般";
            case 4 -> "强";
            case 5 -> "很强";
            default -> "未知";
        };
    }

    /**
     * 验证密码是否符合要求
     *
     * @param password 密码
     * @return 是否符合要求
     */
    public boolean isValidPassword(String password) {
        if (password == null || password.length() < 6 || password.length() > 20) {
            return false;
        }
        
        // 至少包含一个数字
        return password.matches(".*\\d.*");
    }

    /**
     * 生成随机密码
     *
     * @param length 密码长度
     * @return 随机密码
     */
    public String generateRandomPassword(int length) {
        String chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*";
        StringBuilder password = new StringBuilder();
        
        for (int i = 0; i < length; i++) {
            int index = (int) (Math.random() * chars.length());
            password.append(chars.charAt(index));
        }
        
        return password.toString();
    }
}
