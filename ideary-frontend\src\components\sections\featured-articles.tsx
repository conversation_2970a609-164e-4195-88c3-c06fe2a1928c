'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { motion } from 'framer-motion';
import { 
  HeartIcon, 
  ChatBubbleLeftIcon, 
  EyeIcon,
  ClockIcon,
  TagIcon,
} from '@heroicons/react/24/outline';
import { HeartIcon as HeartSolidIcon } from '@heroicons/react/24/solid';
import { Avatar } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { formatRelativeTime, formatNumber } from '@/lib/utils';

interface Article {
  id: number;
  title: string;
  summary: string;
  content: string;
  coverImage?: string;
  author: {
    id: number;
    username: string;
    nickname: string;
    avatar?: string;
  };
  tags: string[];
  viewCount: number;
  likeCount: number;
  commentCount: number;
  isLiked: boolean;
  publishedAt: string;
  readingTime: number;
}

export function FeaturedArticles() {
  const [articles, setArticles] = useState<Article[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchFeaturedArticles();
  }, []);

  const fetchFeaturedArticles = async () => {
    try {
      setLoading(true);
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // 模拟数据
      const mockArticles: Article[] = [
        {
          id: 1,
          title: 'React 18 新特性深度解析：并发渲染与自动批处理',
          summary: '深入探讨 React 18 带来的革命性变化，包括并发渲染、自动批处理、Suspense 改进等核心特性，以及它们如何提升应用性能。',
          content: '',
          coverImage: 'https://images.unsplash.com/photo-1633356122544-f134324a6cee?w=800&h=400&fit=crop',
          author: {
            id: 1,
            username: 'react_master',
            nickname: 'React 大师',
            avatar: 'https://avatars.githubusercontent.com/u/1?v=4',
          },
          tags: ['React', 'JavaScript', '前端开发'],
          viewCount: 12500,
          likeCount: 856,
          commentCount: 124,
          isLiked: false,
          publishedAt: '2024-01-15T10:30:00Z',
          readingTime: 8,
        },
        {
          id: 2,
          title: 'TypeScript 5.0 新特性全面解读',
          summary: 'TypeScript 5.0 正式发布，带来了装饰器、const 类型参数、枚举改进等重要特性。本文将详细介绍这些新特性的使用方法和最佳实践。',
          content: '',
          coverImage: 'https://images.unsplash.com/photo-1516116216624-53e697fedbea?w=800&h=400&fit=crop',
          author: {
            id: 2,
            username: 'ts_expert',
            nickname: 'TypeScript 专家',
            avatar: 'https://avatars.githubusercontent.com/u/2?v=4',
          },
          tags: ['TypeScript', 'JavaScript', '类型系统'],
          viewCount: 9800,
          likeCount: 642,
          commentCount: 89,
          isLiked: true,
          publishedAt: '2024-01-14T14:20:00Z',
          readingTime: 12,
        },
        {
          id: 3,
          title: '微前端架构实战：从零到一构建可扩展的前端系统',
          summary: '通过实际项目案例，详细介绍微前端架构的设计思路、技术选型、实现方案以及在大型项目中的应用经验。',
          content: '',
          coverImage: 'https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=800&h=400&fit=crop',
          author: {
            id: 3,
            username: 'frontend_architect',
            nickname: '前端架构师',
            avatar: 'https://avatars.githubusercontent.com/u/3?v=4',
          },
          tags: ['微前端', '架构设计', '前端工程化'],
          viewCount: 15600,
          likeCount: 1024,
          commentCount: 156,
          isLiked: false,
          publishedAt: '2024-01-13T09:15:00Z',
          readingTime: 15,
        },
      ];

      setArticles(mockArticles);
    } catch (err) {
      setError('加载文章失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  const handleLike = async (articleId: number) => {
    try {
      // 模拟API调用
      setArticles(prev => prev.map(article => 
        article.id === articleId 
          ? { 
              ...article, 
              isLiked: !article.isLiked,
              likeCount: article.isLiked ? article.likeCount - 1 : article.likeCount + 1
            }
          : article
      ));
    } catch (error) {
      console.error('点赞失败:', error);
    }
  };

  if (loading) {
    return <LoadingSpinner text="加载精选文章中..." />;
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <p className="text-error-600 mb-4">{error}</p>
        <Button onClick={fetchFeaturedArticles}>重试</Button>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
      {/* 主要文章 */}
      {articles[0] && (
        <motion.article
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="lg:col-span-2"
        >
          <div className="card overflow-hidden group hover:shadow-medium transition-shadow duration-300">
            {/* 封面图片 */}
            {articles[0].coverImage && (
              <div className="relative h-64 lg:h-80 overflow-hidden">
                <Image
                  src={articles[0].coverImage}
                  alt={articles[0].title}
                  fill
                  className="object-cover group-hover:scale-105 transition-transform duration-300"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent" />
                
                {/* 标签 */}
                <div className="absolute top-4 left-4 flex flex-wrap gap-2">
                  {articles[0].tags.slice(0, 2).map((tag) => (
                    <span
                      key={tag}
                      className="badge badge-primary backdrop-blur-sm"
                    >
                      {tag}
                    </span>
                  ))}
                </div>
              </div>
            )}

            <div className="card-body">
              {/* 文章标题 */}
              <h3 className="text-2xl font-bold text-secondary-900 dark:text-secondary-100 mb-3 group-hover:text-primary-600 transition-colors duration-200">
                <Link href={`/articles/${articles[0].id}`}>
                  {articles[0].title}
                </Link>
              </h3>

              {/* 文章摘要 */}
              <p className="text-secondary-600 dark:text-secondary-400 mb-4 line-clamp-3">
                {articles[0].summary}
              </p>

              {/* 作者信息 */}
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <Avatar
                    src={articles[0].author.avatar}
                    alt={articles[0].author.nickname}
                    size="sm"
                  />
                  <div>
                    <p className="text-sm font-medium text-secondary-900 dark:text-secondary-100">
                      {articles[0].author.nickname}
                    </p>
                    <div className="flex items-center space-x-4 text-xs text-secondary-500">
                      <span className="flex items-center">
                        <ClockIcon className="h-3 w-3 mr-1" />
                        {formatRelativeTime(articles[0].publishedAt)}
                      </span>
                      <span>{articles[0].readingTime} 分钟阅读</span>
                    </div>
                  </div>
                </div>

                {/* 互动数据 */}
                <div className="flex items-center space-x-4 text-sm text-secondary-500">
                  <button
                    onClick={() => handleLike(articles[0].id)}
                    className="flex items-center space-x-1 hover:text-red-500 transition-colors"
                  >
                    {articles[0].isLiked ? (
                      <HeartSolidIcon className="h-4 w-4 text-red-500" />
                    ) : (
                      <HeartIcon className="h-4 w-4" />
                    )}
                    <span>{formatNumber(articles[0].likeCount)}</span>
                  </button>
                  <span className="flex items-center space-x-1">
                    <ChatBubbleLeftIcon className="h-4 w-4" />
                    <span>{formatNumber(articles[0].commentCount)}</span>
                  </span>
                  <span className="flex items-center space-x-1">
                    <EyeIcon className="h-4 w-4" />
                    <span>{formatNumber(articles[0].viewCount)}</span>
                  </span>
                </div>
              </div>
            </div>
          </div>
        </motion.article>
      )}

      {/* 侧边文章列表 */}
      <div className="space-y-6">
        {articles.slice(1).map((article, index) => (
          <motion.article
            key={article.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: (index + 1) * 0.1 }}
          >
            <div className="card group hover:shadow-medium transition-shadow duration-300">
              <div className="card-body">
                {/* 标签 */}
                <div className="flex flex-wrap gap-1 mb-2">
                  {article.tags.slice(0, 2).map((tag) => (
                    <span
                      key={tag}
                      className="inline-flex items-center px-2 py-1 text-xs bg-primary-100 dark:bg-primary-900 text-primary-800 dark:text-primary-200 rounded"
                    >
                      <TagIcon className="h-3 w-3 mr-1" />
                      {tag}
                    </span>
                  ))}
                </div>

                {/* 文章标题 */}
                <h4 className="text-lg font-semibold text-secondary-900 dark:text-secondary-100 mb-2 group-hover:text-primary-600 transition-colors duration-200">
                  <Link href={`/articles/${article.id}`} className="line-clamp-2">
                    {article.title}
                  </Link>
                </h4>

                {/* 文章摘要 */}
                <p className="text-sm text-secondary-600 dark:text-secondary-400 mb-3 line-clamp-2">
                  {article.summary}
                </p>

                {/* 作者和互动信息 */}
                <div className="flex items-center justify-between text-xs text-secondary-500">
                  <div className="flex items-center space-x-2">
                    <Avatar
                      src={article.author.avatar}
                      alt={article.author.nickname}
                      size="xs"
                    />
                    <span>{article.author.nickname}</span>
                  </div>
                  
                  <div className="flex items-center space-x-3">
                    <button
                      onClick={() => handleLike(article.id)}
                      className="flex items-center space-x-1 hover:text-red-500 transition-colors"
                    >
                      {article.isLiked ? (
                        <HeartSolidIcon className="h-3 w-3 text-red-500" />
                      ) : (
                        <HeartIcon className="h-3 w-3" />
                      )}
                      <span>{formatNumber(article.likeCount)}</span>
                    </button>
                    <span className="flex items-center space-x-1">
                      <EyeIcon className="h-3 w-3" />
                      <span>{formatNumber(article.viewCount)}</span>
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </motion.article>
        ))}
      </div>
    </div>
  );
}
