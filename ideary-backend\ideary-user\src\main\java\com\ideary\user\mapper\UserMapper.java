package com.ideary.user.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ideary.user.entity.User;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 用户Mapper接口
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 2024-01-01
 */
@Mapper
public interface UserMapper extends BaseMapper<User> {

    /**
     * 根据用户名查找用户
     *
     * @param username 用户名
     * @return 用户信息
     */
    @Select("SELECT * FROM users WHERE username = #{username} AND deleted_at IS NULL")
    User findByUsername(@Param("username") String username);

    /**
     * 根据邮箱查找用户
     *
     * @param email 邮箱
     * @return 用户信息
     */
    @Select("SELECT * FROM users WHERE email = #{email} AND deleted_at IS NULL")
    User findByEmail(@Param("email") String email);

    /**
     * 根据手机号查找用户
     *
     * @param phone 手机号
     * @return 用户信息
     */
    @Select("SELECT * FROM users WHERE phone = #{phone} AND deleted_at IS NULL")
    User findByPhone(@Param("phone") String phone);

    /**
     * 根据账号查找用户（用户名、邮箱或手机号）
     *
     * @param account 账号
     * @return 用户信息
     */
    @Select("SELECT * FROM users WHERE (username = #{account} OR email = #{account} OR phone = #{account}) AND deleted_at IS NULL")
    User findByAccount(@Param("account") String account);

    /**
     * 检查用户名是否存在
     *
     * @param username 用户名
     * @return 是否存在
     */
    @Select("SELECT COUNT(*) > 0 FROM users WHERE username = #{username} AND deleted_at IS NULL")
    boolean existsByUsername(@Param("username") String username);

    /**
     * 检查邮箱是否存在
     *
     * @param email 邮箱
     * @return 是否存在
     */
    @Select("SELECT COUNT(*) > 0 FROM users WHERE email = #{email} AND deleted_at IS NULL")
    boolean existsByEmail(@Param("email") String email);

    /**
     * 检查手机号是否存在
     *
     * @param phone 手机号
     * @return 是否存在
     */
    @Select("SELECT COUNT(*) > 0 FROM users WHERE phone = #{phone} AND deleted_at IS NULL")
    boolean existsByPhone(@Param("phone") String phone);

    /**
     * 更新用户最后登录时间
     *
     * @param userId    用户ID
     * @param loginTime 登录时间
     * @return 更新行数
     */
    @Update("UPDATE users SET updated_at = #{loginTime} WHERE id = #{userId}")
    int updateLastLoginTime(@Param("userId") Long userId, @Param("loginTime") LocalDateTime loginTime);

    /**
     * 更新用户状态
     *
     * @param userId 用户ID
     * @param status 状态
     * @return 更新行数
     */
    @Update("UPDATE users SET status = #{status}, updated_at = NOW() WHERE id = #{userId}")
    int updateUserStatus(@Param("userId") Long userId, @Param("status") Integer status);

    /**
     * 更新邮箱验证状态
     *
     * @param userId   用户ID
     * @param verified 是否验证
     * @return 更新行数
     */
    @Update("UPDATE users SET email_verified = #{verified}, updated_at = NOW() WHERE id = #{userId}")
    int updateEmailVerified(@Param("userId") Long userId, @Param("verified") Boolean verified);

    /**
     * 更新手机验证状态
     *
     * @param userId   用户ID
     * @param verified 是否验证
     * @return 更新行数
     */
    @Update("UPDATE users SET phone_verified = #{verified}, updated_at = NOW() WHERE id = #{userId}")
    int updatePhoneVerified(@Param("userId") Long userId, @Param("verified") Boolean verified);

    /**
     * 更新用户密码
     *
     * @param userId   用户ID
     * @param password 新密码（已加密）
     * @return 更新行数
     */
    @Update("UPDATE users SET password = #{password}, updated_at = NOW() WHERE id = #{userId}")
    int updatePassword(@Param("userId") Long userId, @Param("password") String password);

    /**
     * 软删除用户
     *
     * @param userId 用户ID
     * @return 更新行数
     */
    @Update("UPDATE users SET status = 3, deleted_at = NOW(), updated_at = NOW() WHERE id = #{userId}")
    int softDeleteUser(@Param("userId") Long userId);

    /**
     * 根据状态查询用户列表
     *
     * @param status 用户状态
     * @param limit  限制数量
     * @param offset 偏移量
     * @return 用户列表
     */
    @Select("SELECT * FROM users WHERE status = #{status} AND deleted_at IS NULL ORDER BY created_at DESC LIMIT #{limit} OFFSET #{offset}")
    List<User> findByStatus(@Param("status") Integer status, @Param("limit") Integer limit,
            @Param("offset") Integer offset);

    /**
     * 统计用户总数
     *
     * @return 用户总数
     */
    @Select("SELECT COUNT(*) FROM users WHERE deleted_at IS NULL")
    long countUsers();

    /**
     * 统计指定状态的用户数
     *
     * @param status 用户状态
     * @return 用户数
     */
    @Select("SELECT COUNT(*) FROM users WHERE status = #{status} AND deleted_at IS NULL")
    long countUsersByStatus(@Param("status") Integer status);

    /**
     * 查询最近注册的用户
     *
     * @param days 天数
     * @return 用户列表
     */
    @Select("SELECT * FROM users WHERE created_at >= DATE_SUB(NOW(), INTERVAL #{days} DAY) AND deleted_at IS NULL ORDER BY created_at DESC")
    List<User> findRecentUsers(@Param("days") Integer days);

    /**
     * 查询活跃用户（最近登录）
     *
     * @param days 天数
     * @return 用户列表
     */
    @Select("SELECT * FROM users WHERE updated_at >= DATE_SUB(NOW(), INTERVAL #{days} DAY) AND status = 1 AND deleted_at IS NULL ORDER BY updated_at DESC")
    List<User> findActiveUsers(@Param("days") Integer days);

    /**
     * 搜索用户
     *
     * @param keyword 关键词
     * @param limit   限制数量
     * @param offset  偏移量
     * @return 用户列表
     */
    @Select("SELECT * FROM users WHERE (username LIKE CONCAT('%', #{keyword}, '%') " +
            "OR nickname LIKE CONCAT('%', #{keyword}, '%') " +
            "OR email LIKE CONCAT('%', #{keyword}, '%')) " +
            "AND deleted_at IS NULL " +
            "ORDER BY " +
            "CASE " +
            "  WHEN username = #{keyword} THEN 1 " +
            "  WHEN nickname = #{keyword} THEN 2 " +
            "  WHEN username LIKE CONCAT(#{keyword}, '%') THEN 3 " +
            "  WHEN nickname LIKE CONCAT(#{keyword}, '%') THEN 4 " +
            "  ELSE 5 " +
            "END, created_at DESC " +
            "LIMIT #{limit} OFFSET #{offset}")
    List<User> searchUsers(@Param("keyword") String keyword,
            @Param("limit") Integer limit,
            @Param("offset") Integer offset);

    /**
     * 批量查询用户信息
     *
     * @param userIds 用户ID列表
     * @return 用户列表
     */
    @Select("<script>" +
            "SELECT * FROM users WHERE id IN " +
            "<foreach collection='userIds' item='userId' open='(' separator=',' close=')'>" +
            "#{userId}" +
            "</foreach>" +
            " AND deleted_at IS NULL" +
            "</script>")
    List<User> findByIds(@Param("userIds") List<Long> userIds);
}
