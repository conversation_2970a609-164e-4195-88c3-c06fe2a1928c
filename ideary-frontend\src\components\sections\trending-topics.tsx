'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { motion } from 'framer-motion';
import { 
  HashtagIcon,
  TrendingUpIcon,
  DocumentTextIcon,
  EyeIcon,
  FireIcon,
} from '@heroicons/react/24/outline';
import { Button } from '@/components/ui/button';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { formatNumber, generateColor } from '@/lib/utils';

interface Topic {
  id: number;
  name: string;
  description: string;
  articlesCount: number;
  followersCount: number;
  totalViews: number;
  weeklyGrowth: number;
  isHot: boolean;
  color: string;
}

export function TrendingTopics() {
  const [topics, setTopics] = useState<Topic[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchTrendingTopics();
  }, []);

  const fetchTrendingTopics = async () => {
    try {
      setLoading(true);
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 600));
      
      // 模拟数据
      const mockTopics: Topic[] = [
        {
          id: 1,
          name: 'React',
          description: '用于构建用户界面的 JavaScript 库，由 Facebook 开发维护。',
          articlesCount: 1250,
          followersCount: 8900,
          totalViews: 2500000,
          weeklyGrowth: 15.6,
          isHot: true,
          color: generateColor('React'),
        },
        {
          id: 2,
          name: 'TypeScript',
          description: 'JavaScript 的超集，添加了静态类型定义，提升开发体验。',
          articlesCount: 980,
          followersCount: 7200,
          totalViews: 1800000,
          weeklyGrowth: 12.3,
          isHot: true,
          color: generateColor('TypeScript'),
        },
        {
          id: 3,
          name: 'Vue.js',
          description: '渐进式 JavaScript 框架，易学易用，适合构建现代 Web 应用。',
          articlesCount: 756,
          followersCount: 5600,
          totalViews: 1200000,
          weeklyGrowth: 8.9,
          isHot: false,
          color: generateColor('Vue.js'),
        },
        {
          id: 4,
          name: 'Node.js',
          description: '基于 Chrome V8 引擎的 JavaScript 运行时，用于构建服务端应用。',
          articlesCount: 892,
          followersCount: 6800,
          totalViews: 1600000,
          weeklyGrowth: 10.2,
          isHot: false,
          color: generateColor('Node.js'),
        },
        {
          id: 5,
          name: '微前端',
          description: '将前端应用分解为更小、更简单的能够独立开发、测试、部署的微应用。',
          articlesCount: 234,
          followersCount: 3200,
          totalViews: 580000,
          weeklyGrowth: 25.4,
          isHot: true,
          color: generateColor('微前端'),
        },
        {
          id: 6,
          name: 'WebAssembly',
          description: '一种新的编码方式，可以在现代的网络浏览器中运行。',
          articlesCount: 156,
          followersCount: 2100,
          totalViews: 320000,
          weeklyGrowth: 18.7,
          isHot: true,
          color: generateColor('WebAssembly'),
        },
        {
          id: 7,
          name: 'GraphQL',
          description: 'API 的查询语言，提供了一种更高效、强大和灵活的数据获取方式。',
          articlesCount: 345,
          followersCount: 4100,
          totalViews: 720000,
          weeklyGrowth: 7.8,
          isHot: false,
          color: generateColor('GraphQL'),
        },
        {
          id: 8,
          name: 'Serverless',
          description: '无服务器计算模式，让开发者专注于代码而不用管理服务器。',
          articlesCount: 278,
          followersCount: 3500,
          totalViews: 490000,
          weeklyGrowth: 14.1,
          isHot: false,
          color: generateColor('Serverless'),
        },
      ];

      setTopics(mockTopics);
    } catch (err) {
      setError('加载热门话题失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return <LoadingSpinner text="加载热门话题中..." />;
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <p className="text-error-600 mb-4">{error}</p>
        <Button onClick={fetchTrendingTopics}>重试</Button>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      {topics.map((topic, index) => (
        <motion.div
          key={topic.id}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: index * 0.1 }}
        >
          <Link href={`/topics/${topic.name.toLowerCase()}`}>
            <div className="card group hover:shadow-medium transition-all duration-300 cursor-pointer">
              <div className="card-body">
                {/* 话题标题和热门标识 */}
                <div className="flex items-start justify-between mb-3">
                  <div className="flex items-center space-x-2">
                    <div 
                      className="w-3 h-3 rounded-full"
                      style={{ backgroundColor: topic.color }}
                    />
                    <h3 className="text-lg font-semibold text-secondary-900 dark:text-secondary-100 group-hover:text-primary-600 transition-colors duration-200">
                      {topic.name}
                    </h3>
                  </div>
                  
                  {topic.isHot && (
                    <div className="flex items-center space-x-1 text-orange-500">
                      <FireIcon className="h-4 w-4" />
                      <span className="text-xs font-medium">热门</span>
                    </div>
                  )}
                </div>

                {/* 话题描述 */}
                <p className="text-sm text-secondary-600 dark:text-secondary-400 mb-4 line-clamp-2">
                  {topic.description}
                </p>

                {/* 统计数据 */}
                <div className="space-y-3">
                  <div className="flex items-center justify-between text-sm">
                    <div className="flex items-center space-x-1 text-secondary-500">
                      <DocumentTextIcon className="h-4 w-4" />
                      <span>文章</span>
                    </div>
                    <span className="font-medium text-secondary-900 dark:text-secondary-100">
                      {formatNumber(topic.articlesCount)}
                    </span>
                  </div>

                  <div className="flex items-center justify-between text-sm">
                    <div className="flex items-center space-x-1 text-secondary-500">
                      <HashtagIcon className="h-4 w-4" />
                      <span>关注</span>
                    </div>
                    <span className="font-medium text-secondary-900 dark:text-secondary-100">
                      {formatNumber(topic.followersCount)}
                    </span>
                  </div>

                  <div className="flex items-center justify-between text-sm">
                    <div className="flex items-center space-x-1 text-secondary-500">
                      <EyeIcon className="h-4 w-4" />
                      <span>阅读</span>
                    </div>
                    <span className="font-medium text-secondary-900 dark:text-secondary-100">
                      {formatNumber(topic.totalViews)}
                    </span>
                  </div>

                  {/* 增长趋势 */}
                  <div className="flex items-center justify-between text-sm pt-2 border-t border-secondary-200 dark:border-secondary-700">
                    <div className="flex items-center space-x-1 text-secondary-500">
                      <TrendingUpIcon className="h-4 w-4" />
                      <span>周增长</span>
                    </div>
                    <span className={`font-medium flex items-center space-x-1 ${
                      topic.weeklyGrowth > 0 
                        ? 'text-success-600' 
                        : topic.weeklyGrowth < 0 
                        ? 'text-error-600' 
                        : 'text-secondary-500'
                    }`}>
                      <span>+{topic.weeklyGrowth}%</span>
                    </span>
                  </div>
                </div>

                {/* 进度条显示热度 */}
                <div className="mt-4">
                  <div className="flex items-center justify-between text-xs text-secondary-500 mb-1">
                    <span>热度</span>
                    <span>{Math.round(topic.weeklyGrowth)}%</span>
                  </div>
                  <div className="w-full bg-secondary-200 dark:bg-secondary-700 rounded-full h-1.5">
                    <div 
                      className="h-1.5 rounded-full transition-all duration-300"
                      style={{ 
                        width: `${Math.min(topic.weeklyGrowth * 3, 100)}%`,
                        backgroundColor: topic.color 
                      }}
                    />
                  </div>
                </div>
              </div>
            </div>
          </Link>
        </motion.div>
      ))}
    </div>
  );
}
