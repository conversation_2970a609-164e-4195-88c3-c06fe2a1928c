'use client';

import { useState, useEffect, useRef } from 'react';
import Link from 'next/link';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  MagnifyingGlassIcon,
  XMarkIcon,
  DocumentTextIcon,
  UserIcon,
  HashtagIcon,
  ClockIcon,
} from '@heroicons/react/24/outline';
import { Avatar } from '@/components/ui/avatar';
import { debounce } from '@/lib/utils';

interface SearchResult {
  type: 'article' | 'author' | 'topic';
  id: number;
  title: string;
  subtitle?: string;
  avatar?: string;
  url: string;
}

interface SearchModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export function SearchModal({ isOpen, onClose }: SearchModalProps) {
  const [query, setQuery] = useState('');
  const [results, setResults] = useState<SearchResult[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [recentSearches, setRecentSearches] = useState<string[]>([]);
  const inputRef = useRef<HTMLInputElement>(null);

  // 模拟搜索API
  const searchApi = async (searchQuery: string): Promise<SearchResult[]> => {
    if (!searchQuery.trim()) return [];
    
    // 模拟API延迟
    await new Promise(resolve => setTimeout(resolve, 300));
    
    // 模拟搜索结果
    const mockResults: SearchResult[] = [
      {
        type: 'article',
        id: 1,
        title: 'React 18 新特性深度解析',
        subtitle: '深入探讨 React 18 带来的革命性变化...',
        url: '/articles/1',
      },
      {
        type: 'author',
        id: 1,
        title: 'React 大师',
        subtitle: '@react_master · 156 篇文章',
        avatar: 'https://avatars.githubusercontent.com/u/1?v=4',
        url: '/authors/react_master',
      },
      {
        type: 'topic',
        id: 1,
        title: 'React',
        subtitle: '1,250 篇文章 · 8,900 关注者',
        url: '/topics/react',
      },
      {
        type: 'article',
        id: 2,
        title: 'TypeScript 5.0 新特性全面解读',
        subtitle: 'TypeScript 5.0 正式发布，带来了装饰器...',
        url: '/articles/2',
      },
    ].filter(item => 
      item.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      (item.subtitle && item.subtitle.toLowerCase().includes(searchQuery.toLowerCase()))
    );

    return mockResults;
  };

  // 防抖搜索
  const debouncedSearch = debounce(async (searchQuery: string) => {
    if (!searchQuery.trim()) {
      setResults([]);
      setIsLoading(false);
      return;
    }

    setIsLoading(true);
    try {
      const searchResults = await searchApi(searchQuery);
      setResults(searchResults);
    } catch (error) {
      console.error('搜索失败:', error);
      setResults([]);
    } finally {
      setIsLoading(false);
    }
  }, 300);

  // 处理搜索输入
  const handleSearch = (value: string) => {
    setQuery(value);
    debouncedSearch(value);
  };

  // 处理搜索提交
  const handleSubmit = (searchQuery: string) => {
    if (!searchQuery.trim()) return;
    
    // 添加到最近搜索
    const newRecentSearches = [
      searchQuery,
      ...recentSearches.filter(item => item !== searchQuery)
    ].slice(0, 5);
    setRecentSearches(newRecentSearches);
    localStorage.setItem('recentSearches', JSON.stringify(newRecentSearches));
    
    // 跳转到搜索结果页
    window.location.href = `/search?q=${encodeURIComponent(searchQuery)}`;
    onClose();
  };

  // 获取图标
  const getIcon = (type: string) => {
    switch (type) {
      case 'article':
        return DocumentTextIcon;
      case 'author':
        return UserIcon;
      case 'topic':
        return HashtagIcon;
      default:
        return DocumentTextIcon;
    }
  };

  // 获取类型标签
  const getTypeLabel = (type: string) => {
    switch (type) {
      case 'article':
        return '文章';
      case 'author':
        return '作者';
      case 'topic':
        return '话题';
      default:
        return '';
    }
  };

  // 键盘事件处理
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        onClose();
      }
      if ((e.metaKey || e.ctrlKey) && e.key === 'k') {
        e.preventDefault();
        if (!isOpen) {
          // 这里应该由父组件处理打开
        }
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [isOpen, onClose]);

  // 自动聚焦
  useEffect(() => {
    if (isOpen && inputRef.current) {
      inputRef.current.focus();
    }
  }, [isOpen]);

  // 加载最近搜索
  useEffect(() => {
    const saved = localStorage.getItem('recentSearches');
    if (saved) {
      try {
        setRecentSearches(JSON.parse(saved));
      } catch (error) {
        console.error('解析最近搜索失败:', error);
      }
    }
  }, []);

  return (
    <AnimatePresence>
      {isOpen && (
        <>
          {/* 背景遮罩 */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 z-50 bg-black/50 backdrop-blur-sm"
            onClick={onClose}
          />

          {/* 搜索模态框 */}
          <motion.div
            initial={{ opacity: 0, scale: 0.95, y: -20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.95, y: -20 }}
            className="fixed top-20 left-1/2 transform -translate-x-1/2 z-50 w-full max-w-2xl mx-4"
          >
            <div className="bg-white dark:bg-secondary-800 rounded-xl shadow-2xl border border-secondary-200 dark:border-secondary-700 overflow-hidden">
              {/* 搜索输入框 */}
              <div className="flex items-center px-4 py-3 border-b border-secondary-200 dark:border-secondary-700">
                <MagnifyingGlassIcon className="h-5 w-5 text-secondary-400 mr-3" />
                <input
                  ref={inputRef}
                  type="text"
                  placeholder="搜索文章、作者或话题..."
                  value={query}
                  onChange={(e) => handleSearch(e.target.value)}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter') {
                      handleSubmit(query);
                    }
                  }}
                  className="flex-1 bg-transparent border-0 focus:outline-none text-secondary-900 dark:text-secondary-100 placeholder-secondary-500"
                />
                <button
                  onClick={onClose}
                  className="p-1 hover:bg-secondary-100 dark:hover:bg-secondary-700 rounded transition-colors"
                >
                  <XMarkIcon className="h-5 w-5 text-secondary-400" />
                </button>
              </div>

              {/* 搜索结果 */}
              <div className="max-h-96 overflow-y-auto">
                {isLoading && (
                  <div className="flex items-center justify-center py-8">
                    <div className="animate-spin rounded-full h-6 w-6 border-2 border-secondary-300 border-t-primary-600" />
                    <span className="ml-2 text-secondary-600 dark:text-secondary-400">搜索中...</span>
                  </div>
                )}

                {!isLoading && query && results.length === 0 && (
                  <div className="text-center py-8">
                    <p className="text-secondary-600 dark:text-secondary-400">
                      没有找到相关结果
                    </p>
                  </div>
                )}

                {!isLoading && results.length > 0 && (
                  <div className="py-2">
                    {results.map((result) => {
                      const Icon = getIcon(result.type);
                      return (
                        <Link
                          key={`${result.type}-${result.id}`}
                          href={result.url}
                          onClick={onClose}
                          className="flex items-center px-4 py-3 hover:bg-secondary-50 dark:hover:bg-secondary-700 transition-colors"
                        >
                          <div className="flex items-center space-x-3 flex-1">
                            {result.avatar ? (
                              <Avatar src={result.avatar} alt={result.title} size="sm" />
                            ) : (
                              <div className="w-8 h-8 bg-secondary-100 dark:bg-secondary-700 rounded-full flex items-center justify-center">
                                <Icon className="h-4 w-4 text-secondary-600 dark:text-secondary-400" />
                              </div>
                            )}
                            <div className="flex-1 min-w-0">
                              <p className="text-sm font-medium text-secondary-900 dark:text-secondary-100 truncate">
                                {result.title}
                              </p>
                              {result.subtitle && (
                                <p className="text-xs text-secondary-500 truncate">
                                  {result.subtitle}
                                </p>
                              )}
                            </div>
                          </div>
                          <span className="text-xs text-secondary-400 bg-secondary-100 dark:bg-secondary-700 px-2 py-1 rounded">
                            {getTypeLabel(result.type)}
                          </span>
                        </Link>
                      );
                    })}
                  </div>
                )}

                {/* 最近搜索 */}
                {!query && recentSearches.length > 0 && (
                  <div className="py-2">
                    <div className="px-4 py-2 text-xs font-medium text-secondary-500 uppercase tracking-wider">
                      最近搜索
                    </div>
                    {recentSearches.map((search, index) => (
                      <button
                        key={index}
                        onClick={() => handleSubmit(search)}
                        className="w-full flex items-center px-4 py-2 hover:bg-secondary-50 dark:hover:bg-secondary-700 transition-colors text-left"
                      >
                        <ClockIcon className="h-4 w-4 text-secondary-400 mr-3" />
                        <span className="text-sm text-secondary-700 dark:text-secondary-300">
                          {search}
                        </span>
                      </button>
                    ))}
                  </div>
                )}

                {/* 空状态 */}
                {!query && recentSearches.length === 0 && (
                  <div className="text-center py-8">
                    <MagnifyingGlassIcon className="h-12 w-12 text-secondary-300 mx-auto mb-3" />
                    <p className="text-secondary-600 dark:text-secondary-400">
                      开始搜索文章、作者或话题
                    </p>
                  </div>
                )}
              </div>

              {/* 快捷键提示 */}
              <div className="px-4 py-2 bg-secondary-50 dark:bg-secondary-900 border-t border-secondary-200 dark:border-secondary-700">
                <div className="flex items-center justify-between text-xs text-secondary-500">
                  <span>按 Enter 搜索</span>
                  <span>按 Esc 关闭</span>
                </div>
              </div>
            </div>
          </motion.div>
        </>
      )}
    </AnimatePresence>
  );
}
