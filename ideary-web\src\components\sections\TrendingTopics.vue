<template>
  <div class="trending-topics">
    <el-skeleton v-if="loading" :rows="2" animated />
    
    <div v-else-if="topics.length > 0" class="topics-grid">
      <div
        v-for="topic in topics"
        :key="topic.id"
        class="topic-card"
        @click="$router.push(`/topics/${topic.name.toLowerCase()}`)"
      >
        <div class="topic-header">
          <div class="topic-icon" :style="{ backgroundColor: topic.color }">
            <span class="topic-initial">{{ topic.name[0] }}</span>
          </div>
          
          <div class="topic-info">
            <h3 class="topic-name">{{ topic.name }}</h3>
            <div class="topic-badges">
              <el-tag v-if="topic.isHot" type="danger" effect="light" size="small">
                <el-icon><TrendChartsFilled /></el-icon>
                热门
              </el-tag>
            </div>
          </div>
        </div>
        
        <p class="topic-description">{{ topic.description }}</p>
        
        <div class="topic-stats">
          <div class="stat-item">
            <el-icon><Document /></el-icon>
            <span class="stat-label">文章</span>
            <span class="stat-value">{{ formatNumber(topic.articlesCount) }}</span>
          </div>
          
          <div class="stat-item">
            <el-icon><UserFilled /></el-icon>
            <span class="stat-label">关注</span>
            <span class="stat-value">{{ formatNumber(topic.followersCount) }}</span>
          </div>
          
          <div class="stat-item">
            <el-icon><View /></el-icon>
            <span class="stat-label">阅读</span>
            <span class="stat-value">{{ formatNumber(topic.totalViews) }}</span>
          </div>
        </div>
        
        <div class="topic-growth">
          <div class="growth-header">
            <span class="growth-label">周增长</span>
            <span class="growth-value" :class="getGrowthClass(topic.weeklyGrowth)">
              <el-icon><TrendCharts /></el-icon>
              +{{ topic.weeklyGrowth }}%
            </span>
          </div>
          
          <div class="growth-bar">
            <div 
              class="growth-progress"
              :style="{ 
                width: `${Math.min(topic.weeklyGrowth * 3, 100)}%`,
                backgroundColor: topic.color 
              }"
            />
          </div>
        </div>
      </div>
    </div>
    
    <div v-else class="empty-state">
      <el-empty description="暂无热门话题" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { 
  TrendChartsFilled, 
  TrendCharts, 
  Document, 
  UserFilled, 
  View 
} from '@element-plus/icons-vue'

interface Topic {
  id: number
  name: string
  description: string
  articlesCount: number
  followersCount: number
  totalViews: number
  weeklyGrowth: number
  isHot: boolean
  color: string
}

const loading = ref(true)
const topics = ref<Topic[]>([])

// 格式化数字
const formatNumber = (num: number) => {
  if (num < 1000) return num.toString()
  if (num < 10000) return `${(num / 1000).toFixed(1)}k`
  return `${Math.floor(num / 1000)}k`
}

// 生成颜色
const generateColor = (name: string) => {
  const colors = [
    '#3b82f6', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6',
    '#06b6d4', '#84cc16', '#f97316', '#ec4899', '#6366f1'
  ]
  let hash = 0
  for (let i = 0; i < name.length; i++) {
    hash = name.charCodeAt(i) + ((hash << 5) - hash)
  }
  return colors[Math.abs(hash) % colors.length]
}

// 获取增长率样式类
const getGrowthClass = (growth: number) => {
  if (growth > 20) return 'growth-high'
  if (growth > 10) return 'growth-medium'
  return 'growth-low'
}

// 获取热门话题
const fetchTrendingTopics = async () => {
  try {
    loading.value = true
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 600))
    
    // 模拟数据
    const mockTopics = [
      {
        id: 1,
        name: 'React',
        description: '用于构建用户界面的 JavaScript 库，由 Facebook 开发维护。',
        articlesCount: 1250,
        followersCount: 8900,
        totalViews: 2500000,
        weeklyGrowth: 15.6,
        isHot: true,
      },
      {
        id: 2,
        name: 'TypeScript',
        description: 'JavaScript 的超集，添加了静态类型定义，提升开发体验。',
        articlesCount: 980,
        followersCount: 7200,
        totalViews: 1800000,
        weeklyGrowth: 12.3,
        isHot: true,
      },
      {
        id: 3,
        name: 'Vue.js',
        description: '渐进式 JavaScript 框架，易学易用，适合构建现代 Web 应用。',
        articlesCount: 756,
        followersCount: 5600,
        totalViews: 1200000,
        weeklyGrowth: 8.9,
        isHot: false,
      },
      {
        id: 4,
        name: 'Node.js',
        description: '基于 Chrome V8 引擎的 JavaScript 运行时，用于构建服务端应用。',
        articlesCount: 892,
        followersCount: 6800,
        totalViews: 1600000,
        weeklyGrowth: 10.2,
        isHot: false,
      },
      {
        id: 5,
        name: '微前端',
        description: '将前端应用分解为更小、更简单的能够独立开发、测试、部署的微应用。',
        articlesCount: 234,
        followersCount: 3200,
        totalViews: 580000,
        weeklyGrowth: 25.4,
        isHot: true,
      },
      {
        id: 6,
        name: 'WebAssembly',
        description: '一种新的编码方式，可以在现代的网络浏览器中运行。',
        articlesCount: 156,
        followersCount: 2100,
        totalViews: 320000,
        weeklyGrowth: 18.7,
        isHot: true,
      },
      {
        id: 7,
        name: 'GraphQL',
        description: 'API 的查询语言，提供了一种更高效、强大和灵活的数据获取方式。',
        articlesCount: 345,
        followersCount: 4100,
        totalViews: 720000,
        weeklyGrowth: 7.8,
        isHot: false,
      },
      {
        id: 8,
        name: 'Serverless',
        description: '无服务器计算模式，让开发者专注于代码而不用管理服务器。',
        articlesCount: 278,
        followersCount: 3500,
        totalViews: 490000,
        weeklyGrowth: 14.1,
        isHot: false,
      },
    ]

    topics.value = mockTopics.map(topic => ({
      ...topic,
      color: generateColor(topic.name)
    }))
  } catch (error) {
    console.error('获取热门话题失败:', error)
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  fetchTrendingTopics()
})
</script>

<style lang="scss" scoped>
.trending-topics {
  .topics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
    
    @media (max-width: 640px) {
      grid-template-columns: 1fr;
    }
  }
  
  .topic-card {
    background: var(--el-bg-color);
    border-radius: 1rem;
    padding: 1.5rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    cursor: pointer;
    
    &:hover {
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
      transform: translateY(-2px);
    }
    
    .topic-header {
      display: flex;
      align-items: flex-start;
      gap: 1rem;
      margin-bottom: 1rem;
      
      .topic-icon {
        width: 3rem;
        height: 3rem;
        border-radius: 0.75rem;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-shrink: 0;
        
        .topic-initial {
          color: white;
          font-weight: bold;
          font-size: 1.25rem;
        }
      }
      
      .topic-info {
        flex: 1;
        min-width: 0;
        
        .topic-name {
          font-size: 1.25rem;
          font-weight: 600;
          color: var(--el-text-color-primary);
          margin-bottom: 0.5rem;
        }
        
        .topic-badges {
          display: flex;
          gap: 0.5rem;
          
          .el-tag {
            .el-icon {
              margin-right: 0.25rem;
            }
          }
        }
      }
    }
    
    .topic-description {
      color: var(--el-text-color-regular);
      font-size: 0.875rem;
      line-height: 1.5;
      margin-bottom: 1.5rem;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }
    
    .topic-stats {
      display: flex;
      justify-content: space-between;
      margin-bottom: 1rem;
      
      .stat-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 0.25rem;
        flex: 1;
        
        .el-icon {
          color: var(--el-text-color-placeholder);
          font-size: 1rem;
        }
        
        .stat-label {
          font-size: 0.75rem;
          color: var(--el-text-color-placeholder);
        }
        
        .stat-value {
          font-size: 0.875rem;
          font-weight: 600;
          color: var(--el-text-color-primary);
        }
      }
    }
    
    .topic-growth {
      .growth-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 0.5rem;
        
        .growth-label {
          font-size: 0.75rem;
          color: var(--el-text-color-placeholder);
        }
        
        .growth-value {
          display: flex;
          align-items: center;
          gap: 0.25rem;
          font-size: 0.75rem;
          font-weight: 600;
          
          &.growth-high {
            color: #ef4444;
          }
          
          &.growth-medium {
            color: #f59e0b;
          }
          
          &.growth-low {
            color: #10b981;
          }
          
          .el-icon {
            font-size: 0.875rem;
          }
        }
      }
      
      .growth-bar {
        height: 4px;
        background: var(--el-fill-color-light);
        border-radius: 2px;
        overflow: hidden;
        
        .growth-progress {
          height: 100%;
          border-radius: 2px;
          transition: width 0.3s ease;
        }
      }
    }
  }
  
  .empty-state {
    text-align: center;
    padding: 3rem 0;
  }
}
</style>
