{"semi": true, "trailingComma": "all", "singleQuote": true, "printWidth": 100, "tabWidth": 2, "useTabs": false, "quoteProps": "as-needed", "jsxSingleQuote": true, "bracketSpacing": true, "bracketSameLine": false, "arrowParens": "avoid", "endOfLine": "lf", "embeddedLanguageFormatting": "auto", "htmlWhitespaceSensitivity": "css", "insertPragma": false, "jsxBracketSameLine": false, "proseWrap": "preserve", "requirePragma": false, "vueIndentScriptAndStyle": false, "plugins": ["prettier-plugin-tailwindcss"], "tailwindConfig": "./tailwind.config.js", "overrides": [{"files": "*.json", "options": {"printWidth": 80}}, {"files": "*.md", "options": {"printWidth": 80, "proseWrap": "always"}}, {"files": "*.yaml", "options": {"printWidth": 80}}]}