package com.ideary.user.service.impl;

import com.ideary.user.dto.*;
import com.ideary.user.entity.User;
import com.ideary.user.entity.UserProfile;
import com.ideary.user.entity.UserFollow;
import com.ideary.user.mapper.UserMapper;
import com.ideary.user.mapper.UserProfileMapper;
import com.ideary.user.mapper.UserFollowMapper;
import com.ideary.user.service.UserService;
import com.ideary.user.util.JwtUtil;
import com.ideary.user.util.PasswordUtil;
import com.ideary.user.util.RedisUtil;
import com.ideary.user.util.EmailUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 用户服务实现类
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 2024-01-01
 */
@Service
@Transactional
public class UserServiceImpl implements UserService {

    private static final Logger logger = LoggerFactory.getLogger(UserServiceImpl.class);

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private UserProfileMapper userProfileMapper;

    @Autowired
    private UserFollowMapper userFollowMapper;

    @Autowired
    private JwtUtil jwtUtil;

    @Autowired
    private PasswordUtil passwordUtil;

    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private EmailUtil emailUtil;

    // Redis Key前缀
    private static final String EMAIL_CODE_PREFIX = "email_code:";
    private static final String SMS_CODE_PREFIX = "sms_code:";
    private static final String LOGIN_ATTEMPTS_PREFIX = "login_attempts:";
    private static final String TOKEN_BLACKLIST_PREFIX = "token_blacklist:";
    private static final String TOKEN_PREFIX = "user_token:";
    private static final String REFRESH_TOKEN_PREFIX = "refresh_token:";

    @Override
    public UserLoginResponse register(UserRegisterRequest request) {
        logger.info("用户注册开始，用户名: {}, 邮箱: {}", request.getUsername(), request.getEmail());

        // 验证密码一致性
        if (!request.isPasswordMatch()) {
            throw new RuntimeException("两次输入的密码不一致");
        }

        // 验证用户协议
        if (!request.isAgreeTerms()) {
            throw new RuntimeException("请同意用户协议");
        }

        // 验证邮箱验证码
        if (!verifyEmailCode(request.getEmail(), request.getEmailCode(), "register")) {
            throw new RuntimeException("邮箱验证码错误或已过期");
        }

        // 检查用户名是否已存在
        if (userMapper.existsByUsername(request.getUsername())) {
            throw new RuntimeException("用户名已存在");
        }

        // 检查邮箱是否已存在
        if (userMapper.existsByEmail(request.getEmail())) {
            throw new RuntimeException("邮箱已被注册");
        }

        // 检查手机号是否已存在（如果提供）
        if (request.getPhone() != null && !request.getPhone().trim().isEmpty()) {
            if (userMapper.existsByPhone(request.getPhone())) {
                throw new RuntimeException("手机号已被注册");
            }
        }

        try {
            // 创建用户
            User user = new User();
            user.setUsername(request.getUsername());
            user.setEmail(request.getEmail());
            user.setPhone(request.getPhone());
            user.setPassword(passwordUtil.encode(request.getPassword()));
            user.setNickname(request.getNickname());
            user.setStatus(1); // 正常状态
            user.setEmailVerified(true); // 注册时已验证邮箱
            user.setPhoneVerified(false);
            user.setCreatedAt(LocalDateTime.now());
            user.setUpdatedAt(LocalDateTime.now());

            userMapper.insert(user);

            // 创建用户详细资料
            UserProfile profile = new UserProfile(user.getId());
            userProfileMapper.insert(profile);

            // 生成Token
            List<String> roles = List.of("USER"); // 默认角色
            String accessToken = jwtUtil.generateAccessToken(user.getId(), user.getUsername(), roles);
            String refreshToken = jwtUtil.generateRefreshToken(user.getId(), user.getUsername());

            // 构建响应
            UserLoginResponse response = new UserLoginResponse();
            response.setUserId(user.getId());
            response.setUsername(user.getUsername());
            response.setEmail(user.getEmail());
            response.setNickname(user.getNickname());
            response.setAvatar(user.getAvatar());
            response.setRoles(roles);
            response.setAccessToken(accessToken);
            response.setRefreshToken(refreshToken);
            response.setExpiresIn(jwtUtil.getExpiration() / 1000);
            response.setLoginTime(LocalDateTime.now());
            response.setFirstLogin(true);

            // 清除邮箱验证码
            redisUtil.delete(EMAIL_CODE_PREFIX + request.getEmail() + ":register");

            logger.info("用户注册成功，用户ID: {}, 用户名: {}", user.getId(), user.getUsername());
            return response;

        } catch (Exception e) {
            logger.error("用户注册失败，用户名: {}, 错误: {}", request.getUsername(), e.getMessage(), e);
            throw new RuntimeException("注册失败: " + e.getMessage());
        }
    }

    @Override
    public UserLoginResponse login(UserLoginRequest request) {
        logger.info("用户登录开始，账号: {}", request.getAccount());

        // 检查登录尝试次数
        String attemptsKey = LOGIN_ATTEMPTS_PREFIX + request.getAccount();
        Integer attempts = (Integer) redisUtil.get(attemptsKey);
        if (attempts != null && attempts >= 5) {
            throw new RuntimeException("登录尝试次数过多，请30分钟后再试");
        }

        try {
            // 查找用户
            User user = userMapper.findByAccount(request.getAccount());
            if (user == null) {
                // 增加登录尝试次数
                incrementLoginAttempts(request.getAccount());
                throw new RuntimeException("用户不存在");
            }

            // 检查用户状态
            if (!user.isActive()) {
                throw new RuntimeException("用户账号已被禁用或注销");
            }

            // 验证密码
            if (!passwordUtil.matches(request.getPassword(), user.getPassword())) {
                // 增加登录尝试次数
                incrementLoginAttempts(request.getAccount());
                throw new RuntimeException("密码错误");
            }

            // 验证验证码（如果需要）
            if (request.needCaptcha()) {
                // TODO: 实现验证码验证逻辑
            }

            // 获取用户角色
            List<String> roles = getUserRoles(user.getId());

            // 生成Token
            String accessToken = jwtUtil.generateAccessToken(user.getId(), user.getUsername(), roles);
            String refreshToken = jwtUtil.generateRefreshToken(user.getId(), user.getUsername());

            // 更新最后登录时间
            userMapper.updateLastLoginTime(user.getId(), LocalDateTime.now());

            // 构建响应
            UserLoginResponse response = new UserLoginResponse();
            response.setUserId(user.getId());
            response.setUsername(user.getUsername());
            response.setEmail(user.getEmail());
            response.setNickname(user.getNickname());
            response.setAvatar(user.getAvatar());
            response.setRoles(roles);
            response.setAccessToken(accessToken);
            response.setRefreshToken(refreshToken);
            response.setExpiresIn(jwtUtil.getExpiration() / 1000);
            response.setLoginTime(LocalDateTime.now());
            response.setFirstLogin(false);

            // 清除登录尝试次数
            redisUtil.delete(attemptsKey);

            logger.info("用户登录成功，用户ID: {}, 用户名: {}", user.getId(), user.getUsername());
            return response;

        } catch (Exception e) {
            logger.error("用户登录失败，账号: {}, 错误: {}", request.getAccount(), e.getMessage());
            throw e;
        }
    }

    @Override
    public void logout(Long userId, String token) {
        logger.info("用户登出，用户ID: {}", userId);

        try {
            // 将Token加入黑名单
            long remainingTime = jwtUtil.getTokenRemainingTime(token);
            if (remainingTime > 0) {
                redisUtil.set(TOKEN_BLACKLIST_PREFIX + token, "1", remainingTime, TimeUnit.SECONDS);
            }

            logger.info("用户登出成功，用户ID: {}", userId);
        } catch (Exception e) {
            logger.error("用户登出失败，用户ID: {}, 错误: {}", userId, e.getMessage());
            throw new RuntimeException("登出失败: " + e.getMessage());
        }
    }

    @Override
    public UserLoginResponse refreshToken(String refreshToken) {
        logger.info("刷新Token开始");

        try {
            // 验证刷新Token
            if (!jwtUtil.validateToken(refreshToken)) {
                throw new RuntimeException("刷新Token无效");
            }

            // 获取用户信息
            Long userId = jwtUtil.getUserIdFromToken(refreshToken);
            String username = jwtUtil.getUsernameFromToken(refreshToken);

            // 检查用户是否存在且状态正常
            User user = userMapper.selectById(userId);
            if (user == null || !user.isActive()) {
                throw new RuntimeException("用户不存在或已被禁用");
            }

            // 获取用户角色
            List<String> roles = getUserRoles(userId);

            // 生成新的访问Token
            String newAccessToken = jwtUtil.generateAccessToken(userId, username, roles);

            // 构建响应
            UserLoginResponse response = new UserLoginResponse();
            response.setUserId(userId);
            response.setUsername(username);
            response.setEmail(user.getEmail());
            response.setNickname(user.getNickname());
            response.setAvatar(user.getAvatar());
            response.setRoles(roles);
            response.setAccessToken(newAccessToken);
            response.setRefreshToken(refreshToken); // 保持原刷新Token
            response.setExpiresIn(jwtUtil.getExpiration() / 1000);
            response.setLoginTime(LocalDateTime.now());

            logger.info("Token刷新成功，用户ID: {}", userId);
            return response;

        } catch (Exception e) {
            logger.error("Token刷新失败，错误: {}", e.getMessage());
            throw new RuntimeException("Token刷新失败: " + e.getMessage());
        }
    }

    @Override
    public UserInfoResponse getUserInfo(Long userId) {
        logger.debug("获取用户信息，用户ID: {}", userId);

        User user = userMapper.selectById(userId);
        if (user == null) {
            throw new RuntimeException("用户不存在");
        }

        return buildUserInfoResponse(user);
    }

    @Override
    public UserInfoResponse getUserInfoByUsername(String username) {
        logger.debug("根据用户名获取用户信息，用户名: {}", username);

        User user = userMapper.findByUsername(username);
        if (user == null) {
            throw new RuntimeException("用户不存在");
        }

        return buildUserInfoResponse(user);
    }

    // 私有辅助方法
    private void incrementLoginAttempts(String account) {
        String key = LOGIN_ATTEMPTS_PREFIX + account;
        Integer attempts = (Integer) redisUtil.get(key);
        attempts = attempts == null ? 1 : attempts + 1;
        redisUtil.set(key, attempts, 30, TimeUnit.MINUTES);
    }

    private List<String> getUserRoles(Long userId) {
        // TODO: 实现获取用户角色逻辑
        return List.of("USER");
    }

    private UserInfoResponse buildUserInfoResponse(User user) {
        UserInfoResponse response = new UserInfoResponse();
        BeanUtils.copyProperties(user, response);

        // 获取用户详细资料
        UserProfile profile = userProfileMapper.findByUserId(user.getId());
        if (profile != null) {
            BeanUtils.copyProperties(profile, response);
        }

        // 获取统计信息
        response.setFollowingCount(userFollowMapper.countFollowing(user.getId()));
        response.setFollowersCount(userFollowMapper.countFollowers(user.getId()));

        // 设置状态描述
        response.setStatusDescription(User.Status.fromCode(user.getStatus()).getDescription());

        // 设置性别描述
        if (response.getGender() != null) {
            response.setGenderDescription(UserProfile.Gender.fromCode(response.getGender()).getDescription());
        }

        return response;
    }

    @Override
    public UserInfoResponse updateUserInfo(Long userId, UserUpdateRequest request) {
        logger.info("更新用户基本信息，用户ID: {}", userId);

        User user = userMapper.selectById(userId);
        if (user == null) {
            throw new RuntimeException("用户不存在");
        }

        // 更新用户信息
        if (request.getNickname() != null) {
            user.setNickname(request.getNickname());
        }
        if (request.getAvatar() != null) {
            user.setAvatar(request.getAvatar());
        }
        user.setUpdatedAt(LocalDateTime.now());

        userMapper.updateById(user);

        logger.info("用户基本信息更新成功，用户ID: {}", userId);
        return getUserInfo(userId);
    }

    @Override
    public UserInfoResponse updateUserProfile(Long userId, UserProfileUpdateRequest request) {
        logger.info("更新用户详细资料，用户ID: {}", userId);

        UserProfile profile = userProfileMapper.findByUserId(userId);
        if (profile == null) {
            profile = new UserProfile(userId);
        }

        // 更新详细资料
        BeanUtils.copyProperties(request, profile, "id", "userId", "createdAt");
        profile.setUpdatedAt(LocalDateTime.now());

        if (profile.getId() == null) {
            userProfileMapper.insert(profile);
        } else {
            userProfileMapper.updateById(profile);
        }

        logger.info("用户详细资料更新成功，用户ID: {}", userId);
        return getUserInfo(userId);
    }

    @Override
    public void changePassword(Long userId, PasswordChangeRequest request) {
        logger.info("修改密码，用户ID: {}", userId);

        User user = userMapper.selectById(userId);
        if (user == null) {
            throw new RuntimeException("用户不存在");
        }

        // 验证原密码
        if (!passwordUtil.matches(request.getOldPassword(), user.getPassword())) {
            throw new RuntimeException("原密码错误");
        }

        // 验证新密码一致性
        if (!request.getNewPassword().equals(request.getConfirmPassword())) {
            throw new RuntimeException("两次输入的新密码不一致");
        }

        // 更新密码
        String encodedPassword = passwordUtil.encode(request.getNewPassword());
        userMapper.updatePassword(userId, encodedPassword);

        logger.info("密码修改成功，用户ID: {}", userId);
    }

    @Override
    public void sendEmailVerificationCode(String email, String type) {
        logger.info("发送邮箱验证码，邮箱: {}, 类型: {}", email, type);

        // 生成6位数字验证码
        String code = String.format("%06d", (int) (Math.random() * 1000000));

        // 存储到Redis，有效期10分钟
        String key = EMAIL_CODE_PREFIX + email + ":" + type;
        redisUtil.set(key, code, 10, TimeUnit.MINUTES);

        // 发送邮件
        String subject = getEmailSubject(type);
        String content = getEmailContent(type, code);
        emailUtil.sendSimpleMail(email, subject, content);

        logger.info("邮箱验证码发送成功，邮箱: {}", email);
    }

    @Override
    public boolean verifyEmailCode(String email, String code, String type) {
        String key = EMAIL_CODE_PREFIX + email + ":" + type;
        String storedCode = (String) redisUtil.get(key);
        return storedCode != null && storedCode.equals(code);
    }

    @Override
    public void followUser(Long followerId, Long followingId) {
        logger.info("关注用户，关注者ID: {}, 被关注者ID: {}", followerId, followingId);

        if (followerId.equals(followingId)) {
            throw new RuntimeException("不能关注自己");
        }

        // 检查被关注用户是否存在
        User followingUser = userMapper.selectById(followingId);
        if (followingUser == null || !followingUser.isActive()) {
            throw new RuntimeException("被关注的用户不存在或已被禁用");
        }

        // 检查是否已关注
        if (userFollowMapper.isFollowing(followerId, followingId)) {
            throw new RuntimeException("已经关注了该用户");
        }

        // 创建关注关系
        UserFollow follow = new UserFollow(followerId, followingId);
        follow.setCreatedAt(LocalDateTime.now());
        userFollowMapper.insert(follow);

        logger.info("关注成功，关注者ID: {}, 被关注者ID: {}", followerId, followingId);
    }

    @Override
    public void unfollowUser(Long followerId, Long followingId) {
        logger.info("取消关注，关注者ID: {}, 被关注者ID: {}", followerId, followingId);

        int result = userFollowMapper.unfollow(followerId, followingId);
        if (result == 0) {
            throw new RuntimeException("未关注该用户");
        }

        logger.info("取消关注成功，关注者ID: {}, 被关注者ID: {}", followerId, followingId);
    }

    @Override
    public boolean isFollowing(Long followerId, Long followingId) {
        return userFollowMapper.isFollowing(followerId, followingId);
    }

    @Override
    public boolean isUsernameAvailable(String username) {
        return !userMapper.existsByUsername(username);
    }

    @Override
    public boolean isEmailAvailable(String email) {
        return !userMapper.existsByEmail(email);
    }

    @Override
    public boolean isPhoneAvailable(String phone) {
        return !userMapper.existsByPhone(phone);
    }

    // 辅助方法
    private String getEmailSubject(String type) {
        return switch (type) {
            case "register" -> "Ideary - 注册验证码";
            case "reset_password" -> "Ideary - 密码重置验证码";
            case "verify_email" -> "Ideary - 邮箱验证码";
            default -> "Ideary - 验证码";
        };
    }

    private String getEmailContent(String type, String code) {
        String action = switch (type) {
            case "register" -> "注册";
            case "reset_password" -> "重置密码";
            case "verify_email" -> "验证邮箱";
            default -> "验证";
        };

        return String.format("""
                您好！

                您正在进行%s操作，验证码为：%s

                验证码有效期为10分钟，请及时使用。
                如果这不是您的操作，请忽略此邮件。

                Ideary团队
                """, action, code);
    }

    // 其他方法的实现...
    @Override
    @Transactional
    public void resetPassword(PasswordResetRequest request) {
        logger.info("密码重置开始，邮箱: {}", request.getEmail());

        // 验证新密码一致性
        if (!request.isPasswordMatch()) {
            throw new RuntimeException("两次输入的密码不一致");
        }

        // 验证邮箱验证码
        if (!verifyEmailCode(request.getEmail(), request.getCode(), "reset_password")) {
            throw new RuntimeException("验证码错误或已过期");
        }

        // 查找用户
        User user = userMapper.findByEmail(request.getEmail());
        if (user == null) {
            throw new RuntimeException("用户不存在");
        }

        // 检查用户状态
        if (user.getStatus() != 1) {
            throw new RuntimeException("用户状态异常，无法重置密码");
        }

        try {
            // 更新密码
            user.setPassword(passwordUtil.encode(request.getNewPassword()));
            user.setUpdatedAt(LocalDateTime.now());
            userMapper.updateById(user);

            // 清除验证码
            String key = EMAIL_CODE_PREFIX + request.getEmail() + ":reset_password";
            redisUtil.delete(key);

            // 清除该用户的所有登录token（强制重新登录）
            // 注意：这里简化处理，实际生产环境可能需要更复杂的token管理
            String userTokenKey = TOKEN_PREFIX + user.getId();
            redisUtil.delete(userTokenKey);

            logger.info("密码重置成功，用户ID: {}", user.getId());

        } catch (Exception e) {
            logger.error("密码重置失败，邮箱: {}, 错误: {}", request.getEmail(), e.getMessage(), e);
            throw new RuntimeException("密码重置失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public void verifyEmail(Long userId, String code) {
        logger.info("邮箱验证开始，用户ID: {}", userId);

        // 查找用户
        User user = userMapper.selectById(userId);
        if (user == null) {
            throw new RuntimeException("用户不存在");
        }

        // 检查用户状态
        if (user.getStatus() != 1) {
            throw new RuntimeException("用户状态异常，无法验证邮箱");
        }

        // 检查邮箱是否已验证
        if (user.getEmailVerified()) {
            throw new RuntimeException("邮箱已验证，无需重复验证");
        }

        // 验证邮箱验证码
        if (!verifyEmailCode(user.getEmail(), code, "verify_email")) {
            throw new RuntimeException("验证码错误或已过期");
        }

        try {
            // 更新邮箱验证状态
            user.setEmailVerified(true);
            user.setUpdatedAt(LocalDateTime.now());
            userMapper.updateById(user);

            // 清除验证码
            String key = EMAIL_CODE_PREFIX + user.getEmail() + ":verify_email";
            redisUtil.delete(key);

            logger.info("邮箱验证成功，用户ID: {}", userId);

        } catch (Exception e) {
            logger.error("邮箱验证失败，用户ID: {}, 错误: {}", userId, e.getMessage(), e);
            throw new RuntimeException("邮箱验证失败: " + e.getMessage());
        }
    }

    @Override
    public void sendSmsVerificationCode(String phone, String type) {
        logger.info("发送短信验证码，手机号: {}, 类型: {}", phone, type);

        // 验证手机号格式
        if (!phone.matches("^1[3-9]\\d{9}$")) {
            throw new RuntimeException("手机号格式不正确");
        }

        // 检查发送频率限制
        String sendCountKey = SMS_CODE_PREFIX + phone + ":" + type + ":count";
        Integer sendCount = (Integer) redisUtil.get(sendCountKey);
        if (sendCount != null && sendCount >= 3) {
            throw new RuntimeException("今日发送次数已达上限，请明天再试");
        }

        // 生成6位数字验证码
        String code = String.format("%06d", (int) (Math.random() * 1000000));

        // 存储到Redis，有效期5分钟
        String key = SMS_CODE_PREFIX + phone + ":" + type;
        redisUtil.set(key, code, 5, TimeUnit.MINUTES);

        // 更新发送次数计数器（24小时过期）
        sendCount = sendCount == null ? 1 : sendCount + 1;
        redisUtil.set(sendCountKey, sendCount, 24, TimeUnit.HOURS);

        // 这里应该调用短信服务提供商的API发送短信
        // 由于是演示项目，这里只记录日志
        logger.info("短信验证码: {} (仅用于开发测试，生产环境请通过短信发送)", code);
        logger.info("短信验证码发送成功，手机号: {}", phone);
    }

    @Override
    public boolean verifySmsCode(String phone, String code, String type) {
        String key = SMS_CODE_PREFIX + phone + ":" + type;
        String storedCode = (String) redisUtil.get(key);
        return storedCode != null && storedCode.equals(code);
    }

    @Override
    @Transactional
    public void verifyPhone(Long userId, String phone, String code) {
        logger.info("手机号验证开始，用户ID: {}, 手机号: {}", userId, phone);

        // 验证手机号格式
        if (!phone.matches("^1[3-9]\\d{9}$")) {
            throw new RuntimeException("手机号格式不正确");
        }

        // 查找用户
        User user = userMapper.selectById(userId);
        if (user == null) {
            throw new RuntimeException("用户不存在");
        }

        // 检查用户状态
        if (user.getStatus() != 1) {
            throw new RuntimeException("用户状态异常，无法验证手机号");
        }

        // 检查手机号是否已被其他用户使用
        User existingUser = userMapper.findByPhone(phone);
        if (existingUser != null && !existingUser.getId().equals(userId)) {
            throw new RuntimeException("该手机号已被其他用户绑定");
        }

        // 验证短信验证码
        if (!verifySmsCode(phone, code, "verify_phone")) {
            throw new RuntimeException("验证码错误或已过期");
        }

        try {
            // 更新手机号和验证状态
            user.setPhone(phone);
            user.setPhoneVerified(true);
            user.setUpdatedAt(LocalDateTime.now());
            userMapper.updateById(user);

            // 清除验证码
            String key = SMS_CODE_PREFIX + phone + ":verify_phone";
            redisUtil.delete(key);

            logger.info("手机号验证成功，用户ID: {}, 手机号: {}", userId, phone);

        } catch (Exception e) {
            logger.error("手机号验证失败，用户ID: {}, 手机号: {}, 错误: {}", userId, phone, e.getMessage(), e);
            throw new RuntimeException("手机号验证失败: " + e.getMessage());
        }
    }

    @Override
    public List<UserInfoResponse> getFollowingList(Long userId, int page, int size) {
        logger.info("获取关注列表，用户ID: {}, 页码: {}, 每页大小: {}", userId, page, size);

        // 参数验证
        if (page < 1)
            page = 1;
        if (size < 1 || size > 100)
            size = 20;

        int offset = (page - 1) * size;

        try {
            // 获取关注列表
            List<UserFollow> followList = userFollowMapper.findFollowingList(userId, size, offset);
            List<UserInfoResponse> result = new ArrayList<>();

            for (UserFollow follow : followList) {
                User followingUser = userMapper.selectById(follow.getFollowingId());
                if (followingUser != null && followingUser.getStatus() == 1) {
                    UserInfoResponse userInfo = buildUserInfoResponse(followingUser);
                    userInfo.setFollowTime(follow.getCreatedAt());
                    result.add(userInfo);
                }
            }

            logger.info("获取关注列表成功，用户ID: {}, 返回数量: {}", userId, result.size());
            return result;

        } catch (Exception e) {
            logger.error("获取关注列表失败，用户ID: {}, 错误: {}", userId, e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<UserInfoResponse> getFollowersList(Long userId, int page, int size) {
        logger.info("获取粉丝列表，用户ID: {}, 页码: {}, 每页大小: {}", userId, page, size);

        // 参数验证
        if (page < 1)
            page = 1;
        if (size < 1 || size > 100)
            size = 20;

        int offset = (page - 1) * size;

        try {
            // 获取粉丝列表
            List<UserFollow> followerList = userFollowMapper.findFollowersList(userId, size, offset);
            List<UserInfoResponse> result = new ArrayList<>();

            for (UserFollow follow : followerList) {
                User followerUser = userMapper.selectById(follow.getFollowerId());
                if (followerUser != null && followerUser.getStatus() == 1) {
                    UserInfoResponse userInfo = buildUserInfoResponse(followerUser);
                    userInfo.setFollowTime(follow.getCreatedAt());
                    result.add(userInfo);
                }
            }

            logger.info("获取粉丝列表成功，用户ID: {}, 返回数量: {}", userId, result.size());
            return result;

        } catch (Exception e) {
            logger.error("获取粉丝列表失败，用户ID: {}, 错误: {}", userId, e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<UserInfoResponse> getMutualFollowsList(Long userId, int page, int size) {
        logger.info("获取互相关注列表，用户ID: {}, 页码: {}, 每页大小: {}", userId, page, size);

        // 参数验证
        if (page < 1)
            page = 1;
        if (size < 1 || size > 100)
            size = 20;

        int offset = (page - 1) * size;

        try {
            // 获取互相关注列表
            List<UserFollow> mutualFollowList = userFollowMapper.findMutualFollows(userId, size, offset);
            List<UserInfoResponse> result = new ArrayList<>();

            for (UserFollow follow : mutualFollowList) {
                User mutualUser = userMapper.selectById(follow.getFollowingId());
                if (mutualUser != null && mutualUser.getStatus() == 1) {
                    UserInfoResponse userInfo = buildUserInfoResponse(mutualUser);
                    userInfo.setFollowTime(follow.getCreatedAt());
                    result.add(userInfo);
                }
            }

            logger.info("获取互相关注列表成功，用户ID: {}, 返回数量: {}", userId, result.size());
            return result;

        } catch (Exception e) {
            logger.error("获取互相关注列表失败，用户ID: {}, 错误: {}", userId, e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    @Override
    @Transactional
    public String uploadAvatar(Long userId, byte[] avatarData) {
        logger.info("头像上传开始，用户ID: {}, 文件大小: {} bytes", userId, avatarData.length);

        // 查找用户
        User user = userMapper.selectById(userId);
        if (user == null) {
            throw new RuntimeException("用户不存在");
        }

        // 检查用户状态
        if (user.getStatus() != 1) {
            throw new RuntimeException("用户状态异常，无法上传头像");
        }

        // 检查文件大小（5MB限制）
        if (avatarData.length > 5 * 1024 * 1024) {
            throw new RuntimeException("头像文件大小不能超过5MB");
        }

        try {
            // 生成文件名
            String fileName = "avatar_" + userId + "_" + System.currentTimeMillis() + ".jpg";
            String avatarUrl = "/uploads/avatars/" + fileName;

            // 这里应该将文件保存到文件系统或对象存储
            // 由于是演示项目，这里只是模拟保存过程
            logger.info("模拟保存头像文件: {}", fileName);

            // 更新用户头像URL
            user.setAvatar(avatarUrl);
            user.setUpdatedAt(LocalDateTime.now());
            userMapper.updateById(user);

            logger.info("头像上传成功，用户ID: {}, 头像URL: {}", userId, avatarUrl);
            return avatarUrl;

        } catch (Exception e) {
            logger.error("头像上传失败，用户ID: {}, 错误: {}", userId, e.getMessage(), e);
            throw new RuntimeException("头像上传失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public void disableUser(Long userId, String reason) {
        logger.info("禁用用户开始，用户ID: {}, 原因: {}", userId, reason);

        // 查找用户
        User user = userMapper.selectById(userId);
        if (user == null) {
            throw new RuntimeException("用户不存在");
        }

        // 检查用户当前状态
        if (user.getStatus() == 2) {
            throw new RuntimeException("用户已被禁用");
        }

        if (user.getStatus() == 3) {
            throw new RuntimeException("用户已注销，无法禁用");
        }

        try {
            // 更新用户状态为禁用
            user.setStatus(2);
            user.setUpdatedAt(LocalDateTime.now());
            userMapper.updateById(user);

            // 清除该用户的所有登录token
            String userTokenKey = TOKEN_PREFIX + userId;
            redisUtil.delete(userTokenKey);

            // 记录禁用原因（这里可以扩展为单独的日志表）
            logger.info("用户禁用成功，用户ID: {}, 原因: {}", userId, reason);

        } catch (Exception e) {
            logger.error("禁用用户失败，用户ID: {}, 错误: {}", userId, e.getMessage(), e);
            throw new RuntimeException("禁用用户失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public void enableUser(Long userId) {
        logger.info("启用用户开始，用户ID: {}", userId);

        // 查找用户
        User user = userMapper.selectById(userId);
        if (user == null) {
            throw new RuntimeException("用户不存在");
        }

        // 检查用户当前状态
        if (user.getStatus() == 1) {
            throw new RuntimeException("用户已是正常状态");
        }

        if (user.getStatus() == 3) {
            throw new RuntimeException("用户已注销，无法启用");
        }

        try {
            // 更新用户状态为正常
            user.setStatus(1);
            user.setUpdatedAt(LocalDateTime.now());
            userMapper.updateById(user);

            logger.info("用户启用成功，用户ID: {}", userId);

        } catch (Exception e) {
            logger.error("启用用户失败，用户ID: {}, 错误: {}", userId, e.getMessage(), e);
            throw new RuntimeException("启用用户失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public void deleteUser(Long userId, String password) {
        logger.info("注销用户开始，用户ID: {}", userId);

        // 查找用户
        User user = userMapper.selectById(userId);
        if (user == null) {
            throw new RuntimeException("用户不存在");
        }

        // 检查用户当前状态
        if (user.getStatus() == 3) {
            throw new RuntimeException("用户已注销");
        }

        // 验证密码
        if (!passwordUtil.matches(password, user.getPassword())) {
            throw new RuntimeException("密码错误");
        }

        try {
            // 软删除：更新用户状态为注销，设置删除时间
            user.setStatus(3);
            user.setDeletedAt(LocalDateTime.now());
            user.setUpdatedAt(LocalDateTime.now());
            userMapper.updateById(user);

            // 清除该用户的所有登录token
            String userTokenKey = TOKEN_PREFIX + userId;
            redisUtil.delete(userTokenKey);

            // 清除相关缓存数据
            String emailCodeKey = EMAIL_CODE_PREFIX + user.getEmail() + ":*";
            String smsCodeKey = SMS_CODE_PREFIX + user.getPhone() + ":*";
            // 注意：这里简化处理，实际可能需要更复杂的缓存清理逻辑

            logger.info("用户注销成功，用户ID: {}", userId);

        } catch (Exception e) {
            logger.error("注销用户失败，用户ID: {}, 错误: {}", userId, e.getMessage(), e);
            throw new RuntimeException("注销用户失败: " + e.getMessage());
        }
    }

    @Override
    public List<UserInfoResponse> searchUsers(String keyword, int page, int size) {
        logger.info("搜索用户，关键词: {}, 页码: {}, 每页大小: {}", keyword, page, size);

        // 参数验证
        if (keyword == null || keyword.trim().isEmpty()) {
            return new ArrayList<>();
        }
        if (page < 1)
            page = 1;
        if (size < 1 || size > 100)
            size = 20;

        int offset = (page - 1) * size;

        try {
            // 搜索用户（按用户名、昵称、邮箱搜索）
            List<User> users = userMapper.searchUsers(keyword.trim(), size, offset);
            List<UserInfoResponse> result = new ArrayList<>();

            for (User user : users) {
                if (user.getStatus() == 1) { // 只返回正常状态的用户
                    UserInfoResponse userInfo = buildUserInfoResponse(user);
                    result.add(userInfo);
                }
            }

            logger.info("搜索用户成功，关键词: {}, 返回数量: {}", keyword, result.size());
            return result;

        } catch (Exception e) {
            logger.error("搜索用户失败，关键词: {}, 错误: {}", keyword, e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    @Override
    public UserStatsResponse getUserStats(Long userId) {
        logger.info("获取用户统计信息，用户ID: {}", userId);

        try {
            UserStatsResponse stats = new UserStatsResponse(userId);

            // 获取关注统计
            stats.setFollowingCount(userFollowMapper.countFollowing(userId));
            stats.setFollowersCount(userFollowMapper.countFollowers(userId));
            stats.setMutualFollowsCount(userFollowMapper.countMutualFollows(userId));

            // 这里应该调用内容服务获取文章统计，暂时设置为0
            stats.setArticlesCount(0L);
            stats.setPublishedArticlesCount(0L);
            stats.setDraftArticlesCount(0L);
            stats.setTotalLikesCount(0L);
            stats.setTotalFavoritesCount(0L);
            stats.setTotalCommentsCount(0L);
            stats.setTotalViewsCount(0L);

            // 今日统计（这里简化处理，实际应该查询具体的统计数据）
            stats.setTodayFollowersCount(0L);
            stats.setTodayViewsCount(0L);
            stats.setTodayLikesCount(0L);

            // 本周统计
            stats.setWeekFollowersCount(0L);
            stats.setWeekViewsCount(0L);

            // 本月统计
            stats.setMonthFollowersCount(0L);
            stats.setMonthViewsCount(0L);

            logger.info("获取用户统计信息成功，用户ID: {}", userId);
            return stats;

        } catch (Exception e) {
            logger.error("获取用户统计信息失败，用户ID: {}, 错误: {}", userId, e.getMessage(), e);
            return new UserStatsResponse(userId);
        }
    }

    @Override
    public List<UserInfoResponse> getUserInfoBatch(List<Long> userIds) {
        logger.info("批量获取用户信息，用户ID数量: {}", userIds.size());

        if (userIds == null || userIds.isEmpty()) {
            return new ArrayList<>();
        }

        // 限制批量查询的数量
        if (userIds.size() > 100) {
            userIds = userIds.subList(0, 100);
        }

        try {
            List<UserInfoResponse> result = new ArrayList<>();

            for (Long userId : userIds) {
                User user = userMapper.selectById(userId);
                if (user != null && user.getStatus() == 1) {
                    UserInfoResponse userInfo = buildUserInfoResponse(user);
                    result.add(userInfo);
                }
            }

            logger.info("批量获取用户信息成功，请求数量: {}, 返回数量: {}", userIds.size(), result.size());
            return result;

        } catch (Exception e) {
            logger.error("批量获取用户信息失败，错误: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }
}
