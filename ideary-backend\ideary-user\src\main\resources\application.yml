# Ideary用户服务配置文件
server:
  port: 9081
  servlet:
    context-path: /

spring:
  application:
    name: ideary-user
  config:
    import: optional:nacos:
  
  # 数据源配置
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: *****************************************************************************************************************************************************************************
    username: ideary
    password: ideary123456
    hikari:
      minimum-idle: 5
      maximum-pool-size: 20
      auto-commit: true
      idle-timeout: 30000
      pool-name: IdearyUserHikariCP
      max-lifetime: 1800000
      connection-timeout: 30000
      connection-test-query: SELECT 1
  
  # JPA配置
  jpa:
    hibernate:
      ddl-auto: validate
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQL8Dialect
        format_sql: true
        use_sql_comments: true
  
  # Redis配置
  data:
    redis:
      host: localhost
      port: 6379
      database: 1
      timeout: 3000ms
      lettuce:
        pool:
          max-active: 8
          max-idle: 8
          min-idle: 0
          max-wait: -1ms
  
  # 邮件配置
  mail:
    host: smtp.qq.com
    port: 587
    username: <EMAIL>
    password: your-email-password
    properties:
      mail:
        smtp:
          auth: true
          starttls:
            enable: true
            required: true
  
  # 配置中心
  cloud:
    nacos:
      discovery:
        server-addr: localhost:8848
        namespace: public
        group: DEFAULT_GROUP
        service: ${spring.application.name}
        metadata:
          version: 1.0.0
          author: Ideary Team
      config:
        server-addr: localhost:8848
        namespace: public
        group: DEFAULT_GROUP
        file-extension: yml
        shared-configs:
          - data-id: common-config.yml
            group: DEFAULT_GROUP
            refresh: true

# MyBatis Plus配置
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    call-setters-on-nulls: true
    jdbc-type-for-null: 'null'
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      id-type: ASSIGN_ID
      logic-delete-field: deleted_at
      logic-delete-value: 'NOW()'
      logic-not-delete-value: 'NULL'
      update-strategy: NOT_NULL
      insert-strategy: NOT_NULL
      select-strategy: NOT_EMPTY
  mapper-locations: classpath*:/mapper/**/*.xml
  type-aliases-package: com.ideary.user.entity

# 管理端点配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always
  info:
    env:
      enabled: true

# 应用信息
info:
  app:
    name: ${spring.application.name}
    description: Ideary技术灵感分享社区 - 用户服务
    version: 1.0.0
    author: Ideary Team
  build:
    java-version: ${java.version}
    spring-boot-version: ${spring-boot.version}

# 日志配置
logging:
  level:
    com.ideary.user: DEBUG
    com.baomidou.mybatisplus: DEBUG
    org.springframework.security: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%logger{50}] - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%logger{50}] - %msg%n"
  file:
    name: logs/ideary-user.log
    max-size: 100MB
    max-history: 30

# JWT配置
jwt:
  secret: ideary-jwt-secret-key-2024
  expiration: 86400000  # 24小时
  refresh-expiration: 604800000  # 7天
  header: Authorization
  prefix: "Bearer "

# 用户配置
user:
  # 密码配置
  password:
    min-length: 6
    max-length: 20
    require-uppercase: false
    require-lowercase: false
    require-digit: true
    require-special-char: false
  
  # 验证码配置
  verification:
    email:
      expire-minutes: 10
      max-send-times: 5
    sms:
      expire-minutes: 5
      max-send-times: 3
  
  # 头像配置
  avatar:
    max-size: 5MB
    allowed-types: jpg,jpeg,png,gif
    default-avatar: /static/images/default-avatar.png
  
  # 关注配置
  follow:
    max-following: 1000
    max-followers: 10000

# 安全配置
security:
  # 登录配置
  login:
    max-attempts: 5
    lock-duration-minutes: 30
    session-timeout-minutes: 30
  
  # 密码重置配置
  password-reset:
    token-expire-minutes: 30
    max-attempts-per-day: 3

# 文件上传配置
file:
  upload:
    path: /data/ideary/uploads/
    max-size: 10MB
    allowed-types: jpg,jpeg,png,gif,pdf,doc,docx

# 第三方服务配置
third-party:
  # 短信服务配置
  sms:
    provider: aliyun
    access-key: your-access-key
    secret-key: your-secret-key
    sign-name: Ideary
    template-code: SMS_123456789
  
  # 对象存储配置
  oss:
    provider: minio
    endpoint: http://localhost:9000
    access-key: ideary
    secret-key: ideary123456
    bucket-name: ideary-files
