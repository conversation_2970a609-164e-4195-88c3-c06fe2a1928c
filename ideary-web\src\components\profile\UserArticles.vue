<template>
  <div class="user-articles">
    <el-skeleton v-if="loading" :rows="3" animated />
    
    <div v-else-if="articles.length > 0" class="articles-list">
      <div
        v-for="article in articles"
        :key="article.id"
        class="article-item"
      >
        <h3 class="article-title">
          <router-link :to="`/articles/${article.id}`">
            {{ article.title }}
          </router-link>
        </h3>
        <p class="article-summary">{{ article.summary }}</p>
        <div class="article-meta">
          <span class="publish-date">{{ formatDate(article.publishedAt) }}</span>
          <div class="article-stats">
            <span><el-icon><View /></el-icon> {{ article.viewCount }}</span>
            <span><el-icon><Star /></el-icon> {{ article.likeCount }}</span>
            <span><el-icon><ChatDotRound /></el-icon> {{ article.commentCount }}</span>
          </div>
        </div>
      </div>
    </div>
    
    <div v-else class="empty-state">
      <el-empty description="还没有发布文章" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { View, Star, ChatDotRound } from '@element-plus/icons-vue'
import dayjs from 'dayjs'

interface Props {
  userId?: number
}

const props = defineProps<Props>()

const loading = ref(true)
const articles = ref<any[]>([])

const formatDate = (date: string) => {
  return dayjs(date).format('YYYY年MM月DD日')
}

const fetchUserArticles = async () => {
  try {
    loading.value = true
    await new Promise(resolve => setTimeout(resolve, 500))
    
    // 模拟数据
    articles.value = [
      {
        id: 1,
        title: '我的第一篇技术文章',
        summary: '这是我在Ideary平台发布的第一篇技术文章...',
        viewCount: 1250,
        likeCount: 89,
        commentCount: 23,
        publishedAt: '2024-01-15T10:30:00Z',
      }
    ]
  } catch (error) {
    console.error('获取用户文章失败:', error)
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  fetchUserArticles()
})
</script>

<style lang="scss" scoped>
.user-articles {
  .articles-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
  }
  
  .article-item {
    padding: 1rem;
    border: 1px solid var(--el-border-color-light);
    border-radius: 0.5rem;
    
    .article-title {
      font-size: 1.125rem;
      font-weight: 600;
      margin-bottom: 0.5rem;
      
      a {
        color: var(--el-text-color-primary);
        text-decoration: none;
        
        &:hover {
          color: var(--el-color-primary);
        }
      }
    }
    
    .article-summary {
      color: var(--el-text-color-regular);
      margin-bottom: 0.75rem;
    }
    
    .article-meta {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 0.875rem;
      color: var(--el-text-color-placeholder);
      
      .article-stats {
        display: flex;
        gap: 1rem;
        
        span {
          display: flex;
          align-items: center;
          gap: 0.25rem;
        }
      }
    }
  }
  
  .empty-state {
    text-align: center;
    padding: 2rem 0;
  }
}
</style>
