# 贡献指南

感谢您对 Ideary 项目的关注和贡献！本文档将帮助您了解如何参与项目开发。

## 🤝 贡献方式

### 报告问题
- 使用 GitHub Issues 报告 Bug
- 提供详细的问题描述和复现步骤
- 包含系统环境信息

### 功能建议
- 在 Issues 中提出新功能建议
- 详细描述功能需求和使用场景
- 讨论实现方案的可行性

### 代码贡献
- Fork 项目到您的 GitHub 账户
- 创建功能分支进行开发
- 提交 Pull Request

## 🔧 开发环境搭建

### 环境要求
- **Java**: JDK 17+
- **Node.js**: 18+
- **Docker**: 20.10+
- **Git**: 2.30+

### 克隆项目
```bash
git clone https://github.com/ideary-team/ideary-blog.git
cd ideary-blog
```

### 启动开发环境
```bash
# 启动基础设施
cd infrastructure
./start-infrastructure.sh

# 启动后端服务
cd ../ideary-backend/ideary-gateway
./start-gateway.sh

# 启动前端应用
cd ../../ideary-frontend
./start-frontend.sh
```

## 📝 代码规范

### Java 代码规范
- 遵循阿里巴巴 Java 开发手册
- 使用 4 个空格缩进
- 类名使用 PascalCase
- 方法名和变量名使用 camelCase
- 常量使用 UPPER_SNAKE_CASE

### TypeScript 代码规范
- 使用 ESLint + Prettier
- 使用 2 个空格缩进
- 组件名使用 PascalCase
- 函数名和变量名使用 camelCase
- 类型定义使用 PascalCase

### 注释规范
- 所有公共方法必须有 JavaDoc/JSDoc 注释
- 复杂逻辑必须有行内注释
- 注释使用中文，简洁明了

## 🌿 Git 工作流

### 分支命名规范
- `feature/功能名称` - 新功能开发
- `bugfix/问题描述` - Bug 修复
- `hotfix/紧急修复` - 紧急修复
- `docs/文档更新` - 文档更新

### 提交信息规范
```
类型(范围): 简短描述

详细描述（可选）

关联 Issue: #123
```

**类型说明：**
- `feat`: 新功能
- `fix`: Bug 修复
- `docs`: 文档更新
- `style`: 代码格式调整
- `refactor`: 代码重构
- `test`: 测试相关
- `chore`: 构建过程或辅助工具的变动

### Pull Request 流程
1. **创建分支**
   ```bash
   git checkout -b feature/your-feature-name
   ```

2. **开发和测试**
   - 编写代码
   - 添加测试
   - 确保所有测试通过

3. **提交代码**
   ```bash
   git add .
   git commit -m "feat(user): 添加用户注册功能"
   git push origin feature/your-feature-name
   ```

4. **创建 Pull Request**
   - 填写详细的 PR 描述
   - 关联相关 Issue
   - 等待代码审查

## 🧪 测试要求

### 单元测试
- 新功能必须包含单元测试
- 测试覆盖率不低于 80%
- 使用 JUnit 5 (Java) 和 Jest (TypeScript)

### 集成测试
- 关键功能需要集成测试
- 使用 TestContainers 进行数据库测试

### 端到端测试
- 核心用户流程需要 E2E 测试
- 使用 Playwright 进行前端测试

## 📋 代码审查

### 审查要点
- 代码功能是否正确
- 是否遵循代码规范
- 是否有充分的测试
- 是否有安全问题
- 性能是否合理

### 审查流程
1. 自动化检查（CI/CD）
2. 同行代码审查
3. 维护者最终审查
4. 合并到主分支

## 🚀 发布流程

### 版本号规范
使用语义化版本号：`主版本号.次版本号.修订号`

- **主版本号**: 不兼容的 API 修改
- **次版本号**: 向下兼容的功能性新增
- **修订号**: 向下兼容的问题修正

### 发布步骤
1. 更新版本号
2. 更新 CHANGELOG
3. 创建 Release Tag
4. 自动化部署

## 📚 文档贡献

### 文档类型
- API 文档
- 用户指南
- 开发文档
- 部署文档

### 文档规范
- 使用 Markdown 格式
- 包含代码示例
- 保持内容更新

## 🎯 Issue 管理

### Issue 标签
- `bug`: Bug 报告
- `enhancement`: 功能增强
- `documentation`: 文档相关
- `good first issue`: 适合新手
- `help wanted`: 需要帮助

### Issue 模板
项目提供了 Issue 模板，请按照模板填写相关信息。

## 💬 社区交流

### 沟通渠道
- **GitHub Discussions**: 技术讨论
- **Discord**: 实时交流
- **邮件**: <EMAIL>

### 行为准则
- 尊重他人，友善交流
- 专注于技术讨论
- 避免政治和宗教话题
- 遵循开源社区最佳实践

## 🏆 贡献者认可

### 贡献统计
- 代码贡献会在 README 中展示
- 重要贡献者会获得特殊徽章
- 定期发布贡献者报告

### 奖励机制
- 优秀贡献者可获得推荐信
- 有机会参与项目决策
- 技术分享和演讲机会

## 📞 联系我们

如果您有任何问题或建议，请通过以下方式联系我们：

- **GitHub Issues**: 技术问题和 Bug 报告
- **GitHub Discussions**: 功能讨论和建议
- **邮箱**: <EMAIL>
- **Discord**: https://discord.gg/ideary

感谢您的贡献！🎉
