import { apiClient } from './client';
import type {
  LoginRequest,
  LoginResponse,
  RegisterRequest,
  PasswordChangeRequest,
  PasswordResetRequest,
  EmailVerificationRequest,
  UserProfile,
  UserUpdateRequest,
  UserProfileUpdateRequest,
  UserStats,
} from '@/types/auth';

/**
 * 认证API
 */
export const authApi = {
  /**
   * 用户登录
   */
  async login(data: LoginRequest): Promise<LoginResponse> {
    const response = await apiClient.post('/auth/login', data);
    return response.data;
  },

  /**
   * 用户注册
   */
  async register(data: RegisterRequest): Promise<LoginResponse> {
    const response = await apiClient.post('/auth/register', data);
    return response.data;
  },

  /**
   * 用户登出
   */
  async logout(): Promise<void> {
    await apiClient.post('/auth/logout');
  },

  /**
   * 刷新Token
   */
  async refreshToken(refreshToken: string): Promise<LoginResponse> {
    const response = await apiClient.post('/auth/refresh', { refreshToken });
    return response.data;
  },

  /**
   * 发送邮箱验证码
   */
  async sendEmailCode(data: EmailVerificationRequest): Promise<void> {
    await apiClient.post('/auth/send-email-code', data);
  },

  /**
   * 验证邮箱验证码
   */
  async verifyEmailCode(email: string, code: string, type: string): Promise<{ valid: boolean }> {
    const response = await apiClient.post('/auth/verify-email-code', { email, code, type });
    return response.data;
  },

  /**
   * 重置密码
   */
  async resetPassword(data: PasswordResetRequest): Promise<void> {
    await apiClient.post('/auth/reset-password', data);
  },

  /**
   * 检查用户名是否可用
   */
  async checkUsername(username: string): Promise<{ available: boolean }> {
    const response = await apiClient.get(`/auth/check-username?username=${username}`);
    return response.data;
  },

  /**
   * 检查邮箱是否可用
   */
  async checkEmail(email: string): Promise<{ available: boolean }> {
    const response = await apiClient.get(`/auth/check-email?email=${email}`);
    return response.data;
  },

  /**
   * 获取当前用户信息
   */
  async getCurrentUser(): Promise<UserProfile> {
    const response = await apiClient.get('/users/profile');
    return response.data;
  },

  /**
   * 根据用户名获取用户信息
   */
  async getUserByUsername(username: string): Promise<UserProfile> {
    const response = await apiClient.get(`/users/${username}`);
    return response.data;
  },

  /**
   * 更新用户基本信息
   */
  async updateUser(data: UserUpdateRequest): Promise<UserProfile> {
    const response = await apiClient.put('/users/profile', data);
    return response.data;
  },

  /**
   * 更新用户详细资料
   */
  async updateUserProfile(data: UserProfileUpdateRequest): Promise<UserProfile> {
    const response = await apiClient.put('/users/profile/detail', data);
    return response.data;
  },

  /**
   * 修改密码
   */
  async changePassword(data: PasswordChangeRequest): Promise<void> {
    await apiClient.put('/users/password', data);
  },

  /**
   * 关注用户
   */
  async followUser(userId: number): Promise<void> {
    await apiClient.post(`/users/${userId}/follow`);
  },

  /**
   * 取消关注用户
   */
  async unfollowUser(userId: number): Promise<void> {
    await apiClient.delete(`/users/${userId}/follow`);
  },

  /**
   * 检查是否已关注
   */
  async checkFollowStatus(userId: number): Promise<{ isFollowing: boolean }> {
    const response = await apiClient.get(`/users/${userId}/follow/status`);
    return response.data;
  },

  /**
   * 获取关注列表
   */
  async getFollowingList(userId: number, page = 1, size = 20): Promise<UserProfile[]> {
    const response = await apiClient.get(`/users/${userId}/following?page=${page}&size=${size}`);
    return response.data;
  },

  /**
   * 获取粉丝列表
   */
  async getFollowersList(userId: number, page = 1, size = 20): Promise<UserProfile[]> {
    const response = await apiClient.get(`/users/${userId}/followers?page=${page}&size=${size}`);
    return response.data;
  },

  /**
   * 搜索用户
   */
  async searchUsers(keyword: string, page = 1, size = 20): Promise<UserProfile[]> {
    const response = await apiClient.get(`/users/search?keyword=${keyword}&page=${page}&size=${size}`);
    return response.data;
  },

  /**
   * 获取用户统计信息
   */
  async getUserStats(userId: number): Promise<UserStats> {
    const response = await apiClient.get(`/users/${userId}/stats`);
    return response.data;
  },

  /**
   * 批量获取用户信息
   */
  async getUsersBatch(userIds: number[]): Promise<UserProfile[]> {
    const response = await apiClient.post('/users/batch', { userIds });
    return response.data;
  },
};
