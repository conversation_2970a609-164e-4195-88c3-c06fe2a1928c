<template>
  <div class="topic-detail-page">
    <div class="page-container">
      <el-skeleton v-if="loading" :rows="5" animated />
      
      <div v-else-if="topic" class="topic-content">
        <!-- 话题头部 -->
        <div class="topic-header">
          <div class="topic-icon" :style="{ backgroundColor: topic.color }">
            <span>{{ topic.name[0] }}</span>
          </div>
          
          <div class="topic-info">
            <h1 class="topic-name">{{ topic.name }}</h1>
            <p class="topic-description">{{ topic.description }}</p>
            
            <div class="topic-stats">
              <span>{{ formatNumber(topic.articlesCount) }} 篇文章</span>
              <span>{{ formatNumber(topic.followersCount) }} 关注者</span>
              <span>{{ formatNumber(topic.totalViews) }} 总阅读</span>
            </div>
            
            <el-button 
              :type="topic.isFollowing ? 'default' : 'primary'"
              @click="toggleFollow"
              :loading="followLoading"
            >
              {{ topic.isFollowing ? '已关注' : '关注话题' }}
            </el-button>
          </div>
        </div>
        
        <!-- 文章列表 -->
        <div class="articles-section">
          <h2>相关文章</h2>
          
          <div v-if="articles.length > 0" class="articles-list">
            <div
              v-for="article in articles"
              :key="article.id"
              class="article-item"
            >
              <h3 class="article-title">
                <router-link :to="`/articles/${article.id}`">
                  {{ article.title }}
                </router-link>
              </h3>
              
              <p class="article-summary">{{ article.summary }}</p>
              
              <div class="article-meta">
                <div class="author-info">
                  <el-avatar :src="article.author.avatar" :size="24">
                    {{ article.author.nickname?.[0] || article.author.username?.[0] }}
                  </el-avatar>
                  <span>{{ article.author.nickname || article.author.username }}</span>
                </div>
                
                <div class="article-stats">
                  <span>{{ formatDate(article.publishedAt) }}</span>
                  <span><el-icon><View /></el-icon> {{ formatNumber(article.viewCount) }}</span>
                </div>
              </div>
            </div>
          </div>
          
          <div v-else class="empty-articles">
            <el-empty description="暂无相关文章" />
          </div>
        </div>
      </div>
      
      <div v-else class="not-found">
        <el-empty description="话题不存在" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { View } from '@element-plus/icons-vue'
import dayjs from 'dayjs'

const route = useRoute()

const loading = ref(true)
const followLoading = ref(false)
const topic = ref<any>(null)
const articles = ref<any[]>([])

const formatDate = (date: string) => {
  return dayjs(date).format('MM月DD日')
}

const formatNumber = (num: number) => {
  if (num < 1000) return num.toString()
  if (num < 10000) return `${(num / 1000).toFixed(1)}k`
  return `${Math.floor(num / 1000)}k`
}

const toggleFollow = async () => {
  try {
    followLoading.value = true
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500))
    
    topic.value.isFollowing = !topic.value.isFollowing
    topic.value.followersCount += topic.value.isFollowing ? 1 : -1
    
    ElMessage.success(topic.value.isFollowing ? '关注成功' : '取消关注成功')
  } catch (error) {
    ElMessage.error('操作失败，请重试')
  } finally {
    followLoading.value = false
  }
}

const fetchTopicDetail = async () => {
  try {
    loading.value = true
    const topicName = route.params.name as string
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 800))
    
    // 模拟数据
    topic.value = {
      id: 1,
      name: topicName.charAt(0).toUpperCase() + topicName.slice(1),
      description: `关于${topicName}技术的深度讨论和最佳实践分享`,
      articlesCount: 1250,
      followersCount: 8900,
      totalViews: 2500000,
      color: '#3b82f6',
      isFollowing: false
    }
    
    articles.value = [
      {
        id: 1,
        title: `${topic.value.name} 最佳实践指南`,
        summary: `深入探讨${topic.value.name}的核心概念和实际应用...`,
        author: {
          username: 'expert1',
          nickname: '技术专家',
          avatar: 'https://avatars.githubusercontent.com/u/1?v=4'
        },
        viewCount: 1250,
        publishedAt: '2024-01-15T10:30:00Z'
      },
      {
        id: 2,
        title: `${topic.value.name} 进阶教程`,
        summary: `从基础到高级，全面掌握${topic.value.name}技术栈...`,
        author: {
          username: 'expert2',
          nickname: '高级开发者',
          avatar: 'https://avatars.githubusercontent.com/u/2?v=4'
        },
        viewCount: 980,
        publishedAt: '2024-01-14T14:20:00Z'
      }
    ]
  } catch (error) {
    console.error('获取话题详情失败:', error)
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  fetchTopicDetail()
  document.title = `${topic.value?.name || '话题详情'} - Ideary`
})
</script>

<style lang="scss" scoped>
.topic-detail-page {
  min-height: 100vh;
  background: var(--el-bg-color-page);
}

.page-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem 1rem;
}

.topic-content {
  .topic-header {
    background: var(--el-bg-color);
    border-radius: 1rem;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    display: flex;
    gap: 2rem;
    align-items: flex-start;
    
    @media (max-width: 768px) {
      flex-direction: column;
      text-align: center;
    }
    
    .topic-icon {
      width: 5rem;
      height: 5rem;
      border-radius: 1rem;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-size: 2rem;
      font-weight: bold;
      flex-shrink: 0;
    }
    
    .topic-info {
      flex: 1;
      
      .topic-name {
        font-size: 2.5rem;
        font-weight: bold;
        color: var(--el-text-color-primary);
        margin-bottom: 1rem;
      }
      
      .topic-description {
        font-size: 1.125rem;
        color: var(--el-text-color-regular);
        margin-bottom: 1.5rem;
        line-height: 1.6;
      }
      
      .topic-stats {
        display: flex;
        gap: 2rem;
        margin-bottom: 1.5rem;
        color: var(--el-text-color-placeholder);
        
        @media (max-width: 640px) {
          justify-content: center;
          gap: 1rem;
        }
      }
    }
  }
  
  .articles-section {
    background: var(--el-bg-color);
    border-radius: 1rem;
    padding: 2rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    
    h2 {
      font-size: 1.5rem;
      font-weight: 600;
      color: var(--el-text-color-primary);
      margin-bottom: 1.5rem;
    }
    
    .articles-list {
      display: flex;
      flex-direction: column;
      gap: 1.5rem;
    }
    
    .article-item {
      padding: 1.5rem;
      border: 1px solid var(--el-border-color-light);
      border-radius: 0.75rem;
      transition: all 0.3s ease;
      
      &:hover {
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        transform: translateY(-2px);
      }
      
      .article-title {
        font-size: 1.25rem;
        font-weight: 600;
        margin-bottom: 0.75rem;
        
        a {
          color: var(--el-text-color-primary);
          text-decoration: none;
          
          &:hover {
            color: var(--el-color-primary);
          }
        }
      }
      
      .article-summary {
        color: var(--el-text-color-regular);
        line-height: 1.6;
        margin-bottom: 1rem;
      }
      
      .article-meta {
        display: flex;
        justify-content: space-between;
        align-items: center;
        
        .author-info {
          display: flex;
          align-items: center;
          gap: 0.5rem;
          color: var(--el-text-color-regular);
          font-size: 0.875rem;
        }
        
        .article-stats {
          display: flex;
          gap: 1rem;
          color: var(--el-text-color-placeholder);
          font-size: 0.875rem;
          
          span {
            display: flex;
            align-items: center;
            gap: 0.25rem;
          }
        }
      }
    }
    
    .empty-articles {
      text-align: center;
      padding: 2rem 0;
    }
  }
}

.not-found {
  text-align: center;
  padding: 3rem 0;
}
</style>
