package com.ideary.user.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ideary.user.entity.UserProfile;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 用户详细资料Mapper接口
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 2024-01-01
 */
@Mapper
public interface UserProfileMapper extends BaseMapper<UserProfile> {

    /**
     * 根据用户ID查找用户详细资料
     *
     * @param userId 用户ID
     * @return 用户详细资料
     */
    @Select("SELECT * FROM user_profiles WHERE user_id = #{userId}")
    UserProfile findByUserId(@Param("userId") Long userId);

    /**
     * 检查用户是否已有详细资料
     *
     * @param userId 用户ID
     * @return 是否存在
     */
    @Select("SELECT COUNT(*) > 0 FROM user_profiles WHERE user_id = #{userId}")
    boolean existsByUserId(@Param("userId") Long userId);
}
