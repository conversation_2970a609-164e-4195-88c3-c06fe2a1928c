<template>
  <div class="popular-authors">
    <el-skeleton v-if="loading" :rows="2" animated />
    
    <div v-else-if="authors.length > 0" class="authors-grid">
      <div
        v-for="author in authors"
        :key="author.id"
        class="author-card"
      >
        <div class="author-header">
          <div class="author-avatar-wrapper">
            <el-avatar
              :src="author.avatar"
              :size="64"
              class="author-avatar"
            >
              {{ author.nickname?.[0] || author.username?.[0] }}
            </el-avatar>
            <el-icon v-if="author.isVerified" class="verified-icon">
              <CircleCheckFilled />
            </el-icon>
          </div>
          
          <div class="author-info">
            <h3 class="author-name">
              <router-link :to="`/authors/${author.username}`">
                {{ author.nickname || author.username }}
              </router-link>
            </h3>
            <p class="author-username">@{{ author.username }}</p>
            <p class="author-bio">{{ author.bio }}</p>
          </div>
        </div>
        
        <div class="author-specialties">
          <el-tag
            v-for="specialty in author.specialties.slice(0, 3)"
            :key="specialty"
            type="primary"
            effect="plain"
            size="small"
          >
            {{ specialty }}
          </el-tag>
        </div>
        
        <div class="author-stats">
          <div class="stat-row">
            <div class="stat-item">
              <span class="stat-label">文章</span>
              <span class="stat-value">{{ formatNumber(author.articlesCount) }}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">获赞</span>
              <span class="stat-value">{{ formatNumber(author.totalLikes) }}</span>
            </div>
          </div>
          <div class="stat-row">
            <div class="stat-item">
              <span class="stat-label">粉丝</span>
              <span class="stat-value">{{ formatNumber(author.followersCount) }}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">阅读</span>
              <span class="stat-value">{{ formatNumber(author.totalViews) }}</span>
            </div>
          </div>
        </div>
        
        <div class="author-actions">
          <el-button
            :type="author.isFollowing ? 'default' : 'primary'"
            size="small"
            @click="handleFollow(author)"
            :loading="author.followLoading"
            class="follow-btn"
          >
            <el-icon><UserFilled /></el-icon>
            {{ author.isFollowing ? '已关注' : '关注' }}
          </el-button>
        </div>
      </div>
    </div>
    
    <div v-else class="empty-state">
      <el-empty description="暂无热门作者" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { CircleCheckFilled, UserFilled } from '@element-plus/icons-vue'
import { useUserStore } from '@/stores/user'

interface Author {
  id: number
  username: string
  nickname?: string
  avatar?: string
  bio?: string
  isVerified: boolean
  isFollowing: boolean
  followersCount: number
  articlesCount: number
  totalLikes: number
  totalViews: number
  specialties: string[]
  followLoading?: boolean
}

const userStore = useUserStore()
const loading = ref(true)
const authors = ref<Author[]>([])

// 格式化数字
const formatNumber = (num: number) => {
  if (num < 1000) return num.toString()
  if (num < 10000) return `${(num / 1000).toFixed(1)}k`
  return `${Math.floor(num / 1000)}k`
}

// 处理关注/取消关注
const handleFollow = async (author: Author) => {
  if (!userStore.isAuthenticated) {
    ElMessage.warning('请先登录')
    return
  }
  
  try {
    author.followLoading = true
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500))
    
    author.isFollowing = !author.isFollowing
    author.followersCount += author.isFollowing ? 1 : -1
    
    ElMessage.success(author.isFollowing ? '关注成功' : '取消关注成功')
  } catch (error) {
    console.error('关注操作失败:', error)
    ElMessage.error('操作失败，请重试')
  } finally {
    author.followLoading = false
  }
}

// 获取热门作者
const fetchPopularAuthors = async () => {
  try {
    loading.value = true
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 800))
    
    // 模拟数据
    authors.value = [
      {
        id: 1,
        username: 'react_master',
        nickname: 'React 大师',
        avatar: 'https://avatars.githubusercontent.com/u/1?v=4',
        bio: '专注于 React 生态系统开发，拥有 8 年前端开发经验，热爱分享技术心得。',
        isVerified: true,
        isFollowing: false,
        followersCount: 12500,
        articlesCount: 156,
        totalLikes: 45600,
        totalViews: 890000,
        specialties: ['React', 'TypeScript', '前端架构'],
      },
      {
        id: 2,
        username: 'vue_expert',
        nickname: 'Vue.js 专家',
        avatar: 'https://avatars.githubusercontent.com/u/2?v=4',
        bio: 'Vue.js 核心团队成员，致力于推广 Vue 生态系统，分享最佳实践和设计模式。',
        isVerified: true,
        isFollowing: true,
        followersCount: 9800,
        articlesCount: 98,
        totalLikes: 32400,
        totalViews: 650000,
        specialties: ['Vue.js', 'Nuxt.js', '组件设计'],
      },
      {
        id: 3,
        username: 'fullstack_dev',
        nickname: '全栈开发者',
        avatar: 'https://avatars.githubusercontent.com/u/3?v=4',
        bio: '全栈工程师，专注于现代 Web 技术栈，从前端到后端，从设计到部署。',
        isVerified: false,
        isFollowing: false,
        followersCount: 7600,
        articlesCount: 124,
        totalLikes: 28900,
        totalViews: 520000,
        specialties: ['Node.js', 'Python', 'DevOps'],
      },
      {
        id: 4,
        username: 'mobile_dev',
        nickname: '移动端开发',
        avatar: 'https://avatars.githubusercontent.com/u/4?v=4',
        bio: 'React Native 和 Flutter 开发专家，专注于跨平台移动应用开发。',
        isVerified: true,
        isFollowing: false,
        followersCount: 6200,
        articlesCount: 87,
        totalLikes: 21500,
        totalViews: 380000,
        specialties: ['React Native', 'Flutter', '移动开发'],
      },
      {
        id: 5,
        username: 'ai_researcher',
        nickname: 'AI 研究员',
        avatar: 'https://avatars.githubusercontent.com/u/5?v=4',
        bio: '人工智能研究员，专注于机器学习和深度学习在 Web 开发中的应用。',
        isVerified: true,
        isFollowing: false,
        followersCount: 8900,
        articlesCount: 76,
        totalLikes: 35600,
        totalViews: 720000,
        specialties: ['AI/ML', 'TensorFlow', 'Python'],
      },
      {
        id: 6,
        username: 'ui_designer',
        nickname: 'UI/UX 设计师',
        avatar: 'https://avatars.githubusercontent.com/u/6?v=4',
        bio: '资深 UI/UX 设计师，专注于用户体验设计和前端实现的完美结合。',
        isVerified: false,
        isFollowing: true,
        followersCount: 5400,
        articlesCount: 65,
        totalLikes: 18700,
        totalViews: 290000,
        specialties: ['UI/UX', '设计系统', 'Figma'],
      },
    ]
  } catch (error) {
    console.error('获取热门作者失败:', error)
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  fetchPopularAuthors()
})
</script>

<style lang="scss" scoped>
.popular-authors {
  .authors-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
    
    @media (max-width: 640px) {
      grid-template-columns: 1fr;
    }
  }
  
  .author-card {
    background: var(--el-bg-color);
    border-radius: 1rem;
    padding: 1.5rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    
    &:hover {
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
      transform: translateY(-2px);
    }
    
    .author-header {
      display: flex;
      gap: 1rem;
      margin-bottom: 1rem;
      
      .author-avatar-wrapper {
        position: relative;
        flex-shrink: 0;
        
        .verified-icon {
          position: absolute;
          bottom: -2px;
          right: -2px;
          color: var(--el-color-primary);
          background: var(--el-bg-color);
          border-radius: 50%;
          font-size: 1.25rem;
        }
      }
      
      .author-info {
        flex: 1;
        min-width: 0;
        
        .author-name {
          font-size: 1.125rem;
          font-weight: 600;
          margin-bottom: 0.25rem;
          
          a {
            color: var(--el-text-color-primary);
            text-decoration: none;
            
            &:hover {
              color: var(--el-color-primary);
            }
          }
        }
        
        .author-username {
          color: var(--el-text-color-placeholder);
          font-size: 0.875rem;
          margin-bottom: 0.5rem;
        }
        
        .author-bio {
          color: var(--el-text-color-regular);
          font-size: 0.875rem;
          line-height: 1.4;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          overflow: hidden;
        }
      }
    }
    
    .author-specialties {
      display: flex;
      gap: 0.5rem;
      margin-bottom: 1rem;
      flex-wrap: wrap;
    }
    
    .author-stats {
      margin-bottom: 1rem;
      
      .stat-row {
        display: flex;
        justify-content: space-between;
        margin-bottom: 0.5rem;
        
        &:last-child {
          margin-bottom: 0;
        }
        
        .stat-item {
          display: flex;
          flex-direction: column;
          align-items: center;
          flex: 1;
          
          .stat-label {
            font-size: 0.75rem;
            color: var(--el-text-color-placeholder);
            margin-bottom: 0.25rem;
          }
          
          .stat-value {
            font-size: 0.875rem;
            font-weight: 600;
            color: var(--el-text-color-primary);
          }
        }
      }
    }
    
    .author-actions {
      .follow-btn {
        width: 100%;
        
        .el-icon {
          margin-right: 0.25rem;
        }
      }
    }
  }
  
  .empty-state {
    text-align: center;
    padding: 3rem 0;
  }
}
</style>
