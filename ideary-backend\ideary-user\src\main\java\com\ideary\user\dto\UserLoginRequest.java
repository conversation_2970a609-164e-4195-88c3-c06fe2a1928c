package com.ideary.user.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

/**
 * 用户登录请求DTO
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 2024-01-01
 */
public class UserLoginRequest {

    /**
     * 登录账号（用户名、邮箱或手机号）
     */
    @NotBlank(message = "登录账号不能为空")
    @Size(max = 100, message = "登录账号长度不能超过100个字符")
    private String account;

    /**
     * 密码
     */
    @NotBlank(message = "密码不能为空")
    @Size(min = 6, max = 20, message = "密码长度必须在6-20个字符之间")
    private String password;

    /**
     * 验证码（可选，用于防止暴力破解）
     */
    private String captcha;

    /**
     * 验证码Key（与验证码对应）
     */
    private String captchaKey;

    /**
     * 是否记住我
     */
    private Boolean rememberMe = false;

    // 构造函数
    public UserLoginRequest() {}

    public UserLoginRequest(String account, String password) {
        this.account = account;
        this.password = password;
    }

    // Getter和Setter方法
    public String getAccount() {
        return account;
    }

    public void setAccount(String account) {
        this.account = account;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getCaptcha() {
        return captcha;
    }

    public void setCaptcha(String captcha) {
        this.captcha = captcha;
    }

    public String getCaptchaKey() {
        return captchaKey;
    }

    public void setCaptchaKey(String captchaKey) {
        this.captchaKey = captchaKey;
    }

    public Boolean getRememberMe() {
        return rememberMe;
    }

    public void setRememberMe(Boolean rememberMe) {
        this.rememberMe = rememberMe;
    }

    /**
     * 检查是否需要验证码
     */
    public boolean needCaptcha() {
        return captcha != null && !captcha.trim().isEmpty() &&
               captchaKey != null && !captchaKey.trim().isEmpty();
    }

    /**
     * 检查是否记住我
     */
    public boolean isRememberMe() {
        return rememberMe != null && rememberMe;
    }

    @Override
    public String toString() {
        return "UserLoginRequest{" +
                "account='" + account + '\'' +
                ", captcha='" + captcha + '\'' +
                ", captchaKey='" + captchaKey + '\'' +
                ", rememberMe=" + rememberMe +
                '}';
    }
}
