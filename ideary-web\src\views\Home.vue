<template>
  <div class="home-page">
    <!-- 英雄区域 -->
    <section class="hero-section">
      <div class="hero-container">
        <div class="hero-content">
          <h1 class="hero-title">
            <span class="gradient-text">技术灵感</span>
            <br>
            分享社区
          </h1>
          <p class="hero-description">
            汇聚优秀的技术文章、经验分享和创新思维，与志同道合的开发者一起成长
          </p>
          
          <!-- 搜索框 -->
          <div class="hero-search">
            <el-input
              v-model="searchQuery"
              size="large"
              placeholder="搜索技术文章、话题或作者..."
              @keyup.enter="handleSearch"
              class="search-input"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
              <template #suffix>
                <el-button type="primary" @click="handleSearch">
                  搜索
                </el-button>
              </template>
            </el-input>
          </div>
          
          <!-- 行动按钮 -->
          <div class="hero-actions">
            <el-button
              v-if="userStore.isAuthenticated"
              type="primary"
              size="large"
              @click="$router.push('/write')"
            >
              <el-icon><Edit /></el-icon>
              开始写作
            </el-button>
            <el-button
              v-else
              type="primary"
              size="large"
              @click="$router.push('/auth/register')"
            >
              <el-icon><UserFilled /></el-icon>
              立即加入
            </el-button>
            
            <el-button
              size="large"
              @click="$router.push('/articles')"
            >
              <el-icon><Document /></el-icon>
              浏览文章
            </el-button>
          </div>
          
          <!-- 统计数据 -->
          <div class="hero-stats">
            <div class="stat-item">
              <div class="stat-number">10,000+</div>
              <div class="stat-label">技术文章</div>
            </div>
            <div class="stat-item">
              <div class="stat-number">5,000+</div>
              <div class="stat-label">活跃用户</div>
            </div>
            <div class="stat-item">
              <div class="stat-number">500+</div>
              <div class="stat-label">技术话题</div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 精选文章 -->
    <section class="featured-section">
      <div class="section-container">
        <div class="section-header">
          <h2 class="section-title">精选文章</h2>
          <p class="section-subtitle">发现最有价值的技术内容</p>
        </div>
        
        <FeaturedArticles />
        
        <div class="section-footer">
          <el-button @click="$router.push('/articles')">
            查看更多文章
            <el-icon><ArrowRight /></el-icon>
          </el-button>
        </div>
      </div>
    </section>

    <!-- 热门作者 -->
    <section class="authors-section">
      <div class="section-container">
        <div class="section-header">
          <h2 class="section-title">热门作者</h2>
          <p class="section-subtitle">关注优秀的技术分享者</p>
        </div>
        
        <PopularAuthors />
        
        <div class="section-footer">
          <el-button @click="$router.push('/authors')">
            发现更多作者
            <el-icon><ArrowRight /></el-icon>
          </el-button>
        </div>
      </div>
    </section>

    <!-- 热门话题 -->
    <section class="topics-section">
      <div class="section-container">
        <div class="section-header">
          <h2 class="section-title">热门话题</h2>
          <p class="section-subtitle">探索技术趋势和热点</p>
        </div>
        
        <TrendingTopics />
        
        <div class="section-footer">
          <el-button @click="$router.push('/topics')">
            浏览所有话题
            <el-icon><ArrowRight /></el-icon>
          </el-button>
        </div>
      </div>
    </section>

    <!-- 邮件订阅 -->
    <section class="newsletter-section">
      <div class="section-container">
        <NewsletterSection />
      </div>
    </section>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { Search, Edit, UserFilled, Document, ArrowRight } from '@element-plus/icons-vue'
import { useUserStore } from '@/stores/user'
import FeaturedArticles from '@/components/sections/FeaturedArticles.vue'
import PopularAuthors from '@/components/sections/PopularAuthors.vue'
import TrendingTopics from '@/components/sections/TrendingTopics.vue'
import NewsletterSection from '@/components/sections/NewsletterSection.vue'

const router = useRouter()
const userStore = useUserStore()

// 搜索查询
const searchQuery = ref('')

// 处理搜索
const handleSearch = () => {
  if (searchQuery.value.trim()) {
    router.push(`/search?q=${encodeURIComponent(searchQuery.value)}`)
  }
}

// 页面标题
onMounted(() => {
  document.title = 'Ideary - 技术灵感分享社区'
})
</script>

<style lang="scss" scoped>
.home-page {
  min-height: 100vh;
}

.hero-section {
  background: linear-gradient(135deg, 
    rgba(59, 130, 246, 0.1) 0%, 
    rgba(255, 255, 255, 0.9) 50%, 
    rgba(16, 185, 129, 0.1) 100%);
  padding: 4rem 0 6rem;
  position: relative;
  overflow: hidden;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: 
      radial-gradient(circle at 25% 25%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
      radial-gradient(circle at 75% 75%, rgba(16, 185, 129, 0.1) 0%, transparent 50%);
    pointer-events: none;
  }
  
  .hero-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
    position: relative;
    z-index: 1;
  }
  
  .hero-content {
    text-align: center;
    max-width: 800px;
    margin: 0 auto;
    
    .hero-title {
      font-size: 3.5rem;
      font-weight: bold;
      color: var(--el-text-color-primary);
      margin-bottom: 1.5rem;
      line-height: 1.2;
      
      @media (max-width: 768px) {
        font-size: 2.5rem;
      }
      
      .gradient-text {
        background: linear-gradient(135deg, #3b82f6, #1d4ed8);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
      }
    }
    
    .hero-description {
      font-size: 1.25rem;
      color: var(--el-text-color-regular);
      margin-bottom: 2.5rem;
      line-height: 1.6;
      
      @media (max-width: 768px) {
        font-size: 1.125rem;
      }
    }
    
    .hero-search {
      margin-bottom: 2rem;
      
      .search-input {
        max-width: 600px;
        
        :deep(.el-input__wrapper) {
          border-radius: 2rem;
          padding: 0.5rem 1rem;
          box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }
      }
    }
    
    .hero-actions {
      display: flex;
      gap: 1rem;
      justify-content: center;
      margin-bottom: 3rem;
      
      @media (max-width: 640px) {
        flex-direction: column;
        align-items: center;
      }
    }
    
    .hero-stats {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 2rem;
      max-width: 600px;
      margin: 0 auto;
      
      @media (max-width: 640px) {
        gap: 1rem;
      }
      
      .stat-item {
        text-align: center;
        padding: 1.5rem;
        background: rgba(255, 255, 255, 0.8);
        border-radius: 1rem;
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        
        .stat-number {
          font-size: 2rem;
          font-weight: bold;
          color: var(--el-color-primary);
          margin-bottom: 0.5rem;
          
          @media (max-width: 640px) {
            font-size: 1.5rem;
          }
        }
        
        .stat-label {
          color: var(--el-text-color-regular);
          font-size: 0.875rem;
        }
      }
    }
  }
}

.featured-section,
.authors-section,
.topics-section {
  padding: 4rem 0;
  
  &:nth-child(even) {
    background: var(--el-fill-color-lighter);
  }
}

.newsletter-section {
  padding: 4rem 0;
  background: var(--el-fill-color-light);
}

.section-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.section-header {
  text-align: center;
  margin-bottom: 3rem;
  
  .section-title {
    font-size: 2.5rem;
    font-weight: bold;
    color: var(--el-text-color-primary);
    margin-bottom: 1rem;
    
    @media (max-width: 768px) {
      font-size: 2rem;
    }
  }
  
  .section-subtitle {
    font-size: 1.125rem;
    color: var(--el-text-color-regular);
  }
}

.section-footer {
  text-align: center;
  margin-top: 3rem;
}

// 暗色模式适配
.dark {
  .hero-section {
    background: linear-gradient(135deg, 
      rgba(59, 130, 246, 0.2) 0%, 
      rgba(0, 0, 0, 0.9) 50%, 
      rgba(16, 185, 129, 0.2) 100%);
    
    .hero-stats .stat-item {
      background: rgba(0, 0, 0, 0.6);
      border: 1px solid rgba(255, 255, 255, 0.1);
    }
  }
}
</style>
