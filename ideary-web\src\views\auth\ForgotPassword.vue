<template>
  <div class="forgot-password-page">
    <div class="forgot-password-form">
      <div class="form-header">
        <h1 class="form-title">重置密码</h1>
        <p class="form-subtitle">输入您的邮箱地址，我们将发送重置密码的链接</p>
      </div>

      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        @submit.prevent="handleSubmit"
        class="forgot-password-form-content"
        size="large"
      >
        <el-form-item prop="email">
          <el-input
            v-model="form.email"
            type="email"
            placeholder="邮箱地址"
            :prefix-icon="Message"
            clearable
          />
        </el-form-item>

        <el-form-item>
          <el-button
            type="primary"
            native-type="submit"
            :loading="loading"
            class="submit-btn"
          >
            {{ loading ? '发送中...' : '发送重置链接' }}
          </el-button>
        </el-form-item>
      </el-form>

      <div class="form-footer">
        <p class="back-link">
          想起密码了？
          <router-link to="/auth/login">返回登录</router-link>
        </p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { Message } from '@element-plus/icons-vue'

const router = useRouter()

// 表单引用
const formRef = ref<FormInstance>()

// 加载状态
const loading = ref(false)

// 表单数据
const form = reactive({
  email: ''
})

// 表单验证规则
const rules: FormRules = {
  email: [
    { required: true, message: '请输入邮箱地址', trigger: 'blur' },
    { type: 'email', message: '请输入有效的邮箱地址', trigger: 'blur' }
  ]
}

// 处理表单提交
const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    loading.value = true
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    ElMessage.success('重置密码链接已发送到您的邮箱，请查收')
    
    // 跳转到登录页面
    router.push('/auth/login')
    
  } catch (error: any) {
    console.error('发送重置链接失败:', error)
    ElMessage.error(error.message || '发送失败，请重试')
  } finally {
    loading.value = false
  }
}

// 页面标题
onMounted(() => {
  document.title = '重置密码 - Ideary'
})
</script>

<style lang="scss" scoped>
.forgot-password-page {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
}

.forgot-password-form {
  width: 100%;
  max-width: 400px;
  
  .form-header {
    text-align: center;
    margin-bottom: 2rem;
    
    .form-title {
      font-size: 2rem;
      font-weight: bold;
      color: var(--el-text-color-primary);
      margin-bottom: 0.5rem;
    }
    
    .form-subtitle {
      color: var(--el-text-color-regular);
      font-size: 1rem;
      line-height: 1.5;
    }
  }
  
  .forgot-password-form-content {
    .submit-btn {
      width: 100%;
      height: 48px;
      font-size: 1rem;
      font-weight: 500;
    }
  }
  
  .form-footer {
    text-align: center;
    margin-top: 1.5rem;
    
    .back-link {
      color: var(--el-text-color-regular);
      font-size: 0.875rem;
      
      a {
        color: var(--el-color-primary);
        text-decoration: none;
        font-weight: 500;
        
        &:hover {
          text-decoration: underline;
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 480px) {
  .forgot-password-page {
    padding: 1rem;
  }
  
  .forgot-password-form {
    .form-header {
      .form-title {
        font-size: 1.75rem;
      }
    }
  }
}
</style>
