# 更新日志

本文档记录了 Ideary 项目的所有重要变更。

格式基于 [Keep a Changelog](https://keepachangelog.com/zh-CN/1.0.0/)，
版本号遵循 [语义化版本](https://semver.org/lang/zh-CN/)。

## [未发布]

### 新增
- 完整的微服务架构设计
- 用户注册、登录、权限管理系统
- 文章发布、编辑、管理功能
- 评论系统和互动功能
- 搜索和推荐系统
- 实时通知系统
- 响应式前端界面
- 暗色模式支持
- PWA 支持
- 国际化支持

### 技术特性
- Spring Boot 3.2 + Spring Cloud 2023 微服务架构
- Next.js 14 + React 18 现代前端技术栈
- MySQL 8.0 + Redis 7.0 数据存储
- Docker 容器化部署
- Nacos 服务发现和配置管理
- JWT 认证和授权
- 完整的监控和日志系统

## [1.0.0] - 2024-01-01

### 新增
- 项目初始化
- 基础架构搭建
- 核心功能开发

---

## 版本说明

### 版本号格式
- **主版本号**: 不兼容的 API 修改
- **次版本号**: 向下兼容的功能性新增  
- **修订号**: 向下兼容的问题修正

### 变更类型
- **新增**: 新功能
- **变更**: 对现有功能的变更
- **弃用**: 不久将移除的功能
- **移除**: 已移除的功能
- **修复**: 问题修复
- **安全**: 安全相关的修复
