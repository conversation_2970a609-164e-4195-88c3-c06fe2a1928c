# Ideary - 技术灵感分享社区

## 项目简介

Ideary 是一个现代化的技术灵感分享社区，采用分布式微服务架构，为技术人员提供高质量的内容分享和交流平台。

## 技术架构

### 后端技术栈
- **构建工具**: Maven 3.8+
- **核心框架**: Spring Boot 3.2+ + Spring Cloud Alibaba 2023.x
- **服务发现**: Nacos 2.3+
- **API网关**: Spring Cloud Gateway 4.x
- **数据库**: MySQL 8.0+
- **缓存**: Redis 7.0+
- **搜索引擎**: ElasticSearch 8.x
- **消息队列**: RocketMQ 5.x
- **对象存储**: MinIO
- **容器化**: Docker + Docker Compose

### 前端技术栈
- **框架**: Vue 3.4+
- **构建工具**: Vite 5.x
- **状态管理**: Pinia 2.x
- **开发语言**: TypeScript 5.x
- **UI组件库**: Element Plus 2.x
- **路由**: Vue Router 4.x
- **HTTP客户端**: Axios

## 项目结构

```
ideary-blog/
├── ideary-backend/          # 后端微服务
│   ├── ideary-gateway/      # 网关服务 (端口: 9080)
│   ├── ideary-user/         # 用户服务 (端口: 9081)
│   ├── ideary-content/      # 内容服务 (端口: 9082)
│   ├── ideary-search/       # 搜索服务 (端口: 9083)
│   ├── ideary-admin/        # 管理服务 (端口: 9084)
│   └── ideary-common/       # 公共服务 (端口: 9085)
├── ideary-web/              # 前端Vue 3项目
├── infrastructure/          # 基础设施配置
│   ├── docker-compose.yml   # Docker编排文件
│   ├── mysql/              # MySQL配置
│   ├── redis/              # Redis配置
│   ├── nacos/              # Nacos配置
│   ├── elasticsearch/      # ES配置
│   ├── rocketmq/           # RocketMQ配置
│   └── minio/              # MinIO配置
└── docs/                   # 项目文档
    ├── 系统架构设计文档.md
    ├── API接口文档.md
    ├── 数据库设计文档.md
    ├── 部署运维文档.md
    ├── 开发环境搭建指南.md
    └── 代码规范文档.md
```

## 微服务模块

### 1. 网关服务 (ideary-gateway)
- 统一请求入口和路由分发
- 用户身份认证与授权
- 接口限流和监控
- 跨域处理

### 2. 用户服务 (ideary-user)
- 用户注册、登录
- 用户资料管理
- 权限角色管理
- 关注/粉丝关系

### 3. 内容服务 (ideary-content)
- 文章发布、编辑
- 评论系统
- 点赞、收藏功能
- 内容审核

### 4. 搜索服务 (ideary-search)
- 全文搜索
- 内容推荐
- 搜索历史

### 5. 管理服务 (ideary-admin)
- 用户管理
- 内容管理
- 系统配置
- 数据统计

### 6. 公共服务 (ideary-common)
- 文件上传下载
- 短信邮件发送
- 系统通知

## 快速开始

### 环境要求
- JDK 17+
- Node.js 18+
- Docker & Docker Compose
- Maven 3.8+

### 启动步骤

1. **启动基础设施**
```bash
cd infrastructure
docker-compose up -d
```

2. **启动后端服务**
```bash
cd ideary-backend
# 按顺序启动各个微服务
```

3. **启动前端项目**
```bash
cd ideary-web
npm install
npm run dev
```

### 访问地址
- 前端应用: http://localhost:3000
- API网关: http://localhost:9080
- Nacos控制台: http://localhost:8848/nacos

## 开发规范

### 代码规范
- 所有代码必须包含详细的中文注释
- 单元测试覆盖率不低于80%
- 遵循RESTful API设计规范
- 统一的错误处理和响应格式

### Git工作流
- 功能分支开发
- 代码审查机制
- 自动化CI/CD

## 文档

详细文档请查看 [docs](./docs/) 目录：

- [系统架构设计文档](./docs/系统架构设计文档.md)
- [API接口文档](./docs/API接口文档.md)
- [数据库设计文档](./docs/数据库设计文档.md)
- [部署运维文档](./docs/部署运维文档.md)
- [开发环境搭建指南](./docs/开发环境搭建指南.md)
- [代码规范文档](./docs/代码规范文档.md)

## 贡献指南

1. Fork 本仓库
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 联系方式

- 项目维护者: [Your Name]
- 邮箱: [<EMAIL>]
- 项目地址: [https://github.com/your-username/ideary-blog]

---

⭐ 如果这个项目对你有帮助，请给它一个星标！
