'use client';

import { useState } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useTheme } from 'next-themes';
import { 
  Bars3Icon, 
  XMarkIcon, 
  MagnifyingGlassIcon,
  SunIcon,
  MoonIcon,
  UserCircleIcon,
  PencilSquareIcon,
  Cog6ToothIcon,
  ArrowRightOnRectangleIcon,
} from '@heroicons/react/24/outline';
import { useAuth } from '@/components/providers/auth-provider';
import { Button } from '@/components/ui/button';
import { Avatar } from '@/components/ui/avatar';
import { Dropdown } from '@/components/ui/dropdown';
import { SearchModal } from '@/components/modals/search-modal';

export function Header() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isSearchOpen, setIsSearchOpen] = useState(false);
  const { theme, setTheme } = useTheme();
  const { user, isAuthenticated, logout } = useAuth();
  const router = useRouter();

  const navigation = [
    { name: '首页', href: '/' },
    { name: '文章', href: '/articles' },
    { name: '话题', href: '/topics' },
    { name: '作者', href: '/authors' },
    { name: '关于', href: '/about' },
  ];

  const userMenuItems = [
    {
      label: '个人资料',
      icon: UserCircleIcon,
      href: '/profile',
    },
    {
      label: '写文章',
      icon: PencilSquareIcon,
      href: '/write',
    },
    {
      label: '设置',
      icon: Cog6ToothIcon,
      href: '/settings',
    },
    {
      label: '登出',
      icon: ArrowRightOnRectangleIcon,
      onClick: logout,
    },
  ];

  const handleSearch = () => {
    setIsSearchOpen(true);
  };

  const toggleTheme = () => {
    setTheme(theme === 'dark' ? 'light' : 'dark');
  };

  return (
    <>
      <header className="sticky top-0 z-50 w-full border-b border-secondary-200 dark:border-secondary-800 bg-white/80 dark:bg-secondary-900/80 backdrop-blur-sm">
        <div className="container-xl">
          <div className="flex h-16 items-center justify-between">
            {/* Logo */}
            <div className="flex items-center">
              <Link href="/" className="flex items-center space-x-2">
                <div className="h-8 w-8 rounded-lg bg-gradient-to-br from-primary-500 to-primary-600 flex items-center justify-center">
                  <span className="text-white font-bold text-lg">I</span>
                </div>
                <span className="text-xl font-bold text-secondary-900 dark:text-secondary-100">
                  Ideary
                </span>
              </Link>
            </div>

            {/* 桌面端导航 */}
            <nav className="hidden md:flex items-center space-x-8">
              {navigation.map((item) => (
                <Link
                  key={item.name}
                  href={item.href}
                  className="text-secondary-600 hover:text-secondary-900 dark:text-secondary-400 dark:hover:text-secondary-100 transition-colors duration-200"
                >
                  {item.name}
                </Link>
              ))}
            </nav>

            {/* 右侧操作区 */}
            <div className="flex items-center space-x-4">
              {/* 搜索按钮 */}
              <Button
                variant="ghost"
                size="sm"
                onClick={handleSearch}
                className="hidden sm:flex"
              >
                <MagnifyingGlassIcon className="h-5 w-5" />
                <span className="ml-2 text-sm text-secondary-500">搜索...</span>
                <kbd className="ml-2 hidden lg:inline-block px-2 py-1 text-xs bg-secondary-100 dark:bg-secondary-800 rounded">
                  ⌘K
                </kbd>
              </Button>

              {/* 主题切换 */}
              <Button
                variant="ghost"
                size="sm"
                onClick={toggleTheme}
                className="p-2"
              >
                {theme === 'dark' ? (
                  <SunIcon className="h-5 w-5" />
                ) : (
                  <MoonIcon className="h-5 w-5" />
                )}
              </Button>

              {/* 用户菜单或登录按钮 */}
              {isAuthenticated ? (
                <div className="flex items-center space-x-3">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => router.push('/write')}
                    className="hidden sm:flex"
                  >
                    <PencilSquareIcon className="h-4 w-4 mr-2" />
                    写文章
                  </Button>
                  
                  <Dropdown
                    trigger={
                      <button className="flex items-center space-x-2 p-1 rounded-full hover:bg-secondary-100 dark:hover:bg-secondary-800 transition-colors">
                        <Avatar
                          src={user?.avatar}
                          alt={user?.nickname || user?.username}
                          size="sm"
                        />
                      </button>
                    }
                    items={userMenuItems}
                  />
                </div>
              ) : (
                <div className="flex items-center space-x-3">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => router.push('/auth/login')}
                  >
                    登录
                  </Button>
                  <Button
                    variant="primary"
                    size="sm"
                    onClick={() => router.push('/auth/register')}
                  >
                    注册
                  </Button>
                </div>
              )}

              {/* 移动端菜单按钮 */}
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsMenuOpen(!isMenuOpen)}
                className="md:hidden p-2"
              >
                {isMenuOpen ? (
                  <XMarkIcon className="h-6 w-6" />
                ) : (
                  <Bars3Icon className="h-6 w-6" />
                )}
              </Button>
            </div>
          </div>
        </div>

        {/* 移动端菜单 */}
        {isMenuOpen && (
          <div className="md:hidden border-t border-secondary-200 dark:border-secondary-800 bg-white dark:bg-secondary-900">
            <div className="container-xl py-4">
              <nav className="space-y-4">
                {navigation.map((item) => (
                  <Link
                    key={item.name}
                    href={item.href}
                    className="block text-secondary-600 hover:text-secondary-900 dark:text-secondary-400 dark:hover:text-secondary-100 transition-colors duration-200"
                    onClick={() => setIsMenuOpen(false)}
                  >
                    {item.name}
                  </Link>
                ))}
                
                {/* 移动端搜索 */}
                <button
                  onClick={() => {
                    handleSearch();
                    setIsMenuOpen(false);
                  }}
                  className="flex items-center space-x-2 text-secondary-600 hover:text-secondary-900 dark:text-secondary-400 dark:hover:text-secondary-100 transition-colors duration-200"
                >
                  <MagnifyingGlassIcon className="h-5 w-5" />
                  <span>搜索</span>
                </button>

                {/* 移动端写文章按钮 */}
                {isAuthenticated && (
                  <Link
                    href="/write"
                    className="flex items-center space-x-2 text-primary-600 hover:text-primary-700 transition-colors duration-200"
                    onClick={() => setIsMenuOpen(false)}
                  >
                    <PencilSquareIcon className="h-5 w-5" />
                    <span>写文章</span>
                  </Link>
                )}
              </nav>
            </div>
          </div>
        )}
      </header>

      {/* 搜索模态框 */}
      <SearchModal
        isOpen={isSearchOpen}
        onClose={() => setIsSearchOpen(false)}
      />
    </>
  );
}
