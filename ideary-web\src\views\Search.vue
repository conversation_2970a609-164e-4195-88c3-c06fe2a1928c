<template>
  <div class="search-page">
    <div class="page-container">
      <!-- 搜索头部 -->
      <div class="search-header">
        <h1 class="page-title">搜索结果</h1>
        <div class="search-box">
          <el-input
            v-model="searchQuery"
            size="large"
            placeholder="搜索文章、作者或话题..."
            @keyup.enter="handleSearch"
            clearable
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
            <template #append>
              <el-button @click="handleSearch">搜索</el-button>
            </template>
          </el-input>
        </div>
        
        <!-- 搜索统计 -->
        <div v-if="searchQuery && !loading" class="search-stats">
          <p>找到 <strong>{{ totalResults }}</strong> 个结果，用时 {{ searchTime }}ms</p>
        </div>
      </div>

      <!-- 筛选选项 -->
      <div class="search-filters">
        <el-radio-group v-model="activeFilter" @change="handleFilterChange">
          <el-radio-button label="all">全部</el-radio-button>
          <el-radio-button label="articles">文章</el-radio-button>
          <el-radio-button label="authors">作者</el-radio-button>
          <el-radio-button label="topics">话题</el-radio-button>
        </el-radio-group>
      </div>

      <!-- 搜索结果 -->
      <div class="search-results">
        <el-skeleton v-if="loading" :rows="5" animated />
        
        <div v-else-if="results.length > 0" class="results-list">
          <!-- 文章结果 -->
          <div
            v-for="result in results"
            :key="`${result.type}-${result.id}`"
            class="result-item"
          >
            <div class="result-type">
              <el-tag :type="getResultTypeColor(result.type)" size="small">
                {{ getResultTypeLabel(result.type) }}
              </el-tag>
            </div>
            
            <h3 class="result-title">
              <router-link :to="result.url" v-html="highlightText(result.title)"></router-link>
            </h3>
            
            <p class="result-summary" v-html="highlightText(result.summary)"></p>
            
            <div class="result-meta">
              <div v-if="result.author" class="author-info">
                <el-avatar :src="result.author.avatar" :size="24">
                  {{ result.author.nickname?.[0] || result.author.username?.[0] }}
                </el-avatar>
                <span>{{ result.author.nickname || result.author.username }}</span>
              </div>
              
              <div class="result-stats">
                <span v-if="result.publishedAt" class="publish-date">
                  {{ formatDate(result.publishedAt) }}
                </span>
                <span v-if="result.viewCount" class="view-count">
                  <el-icon><View /></el-icon>
                  {{ formatNumber(result.viewCount) }}
                </span>
              </div>
            </div>
          </div>
        </div>
        
        <div v-else-if="searchQuery" class="empty-results">
          <el-empty description="没有找到相关结果">
            <template #image>
              <el-icon size="60"><Search /></el-icon>
            </template>
            <el-button type="primary" @click="clearSearch">清空搜索</el-button>
          </el-empty>
        </div>
        
        <div v-else class="search-tips">
          <div class="tips-content">
            <h3>搜索技巧</h3>
            <ul>
              <li>使用关键词搜索相关文章和话题</li>
              <li>搜索作者用户名或昵称找到感兴趣的作者</li>
              <li>使用引号搜索精确短语，如 "React Hooks"</li>
              <li>尝试不同的关键词组合获得更好的结果</li>
            </ul>
          </div>
        </div>

        <!-- 分页 -->
        <div v-if="results.length > 0" class="pagination-section">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :total="totalResults"
            layout="total, prev, pager, next"
            @current-change="handlePageChange"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { Search, View } from '@element-plus/icons-vue'
import { debounce } from 'lodash-es'
import dayjs from 'dayjs'

const route = useRoute()
const router = useRouter()

// 响应式数据
const loading = ref(false)
const searchQuery = ref('')
const activeFilter = ref('all')
const results = ref<any[]>([])
const totalResults = ref(0)
const searchTime = ref(0)
const currentPage = ref(1)
const pageSize = ref(20)

// 格式化日期
const formatDate = (date: string) => {
  return dayjs(date).format('YYYY年MM月DD日')
}

// 格式化数字
const formatNumber = (num: number) => {
  if (num < 1000) return num.toString()
  if (num < 10000) return `${(num / 1000).toFixed(1)}k`
  return `${Math.floor(num / 1000)}k`
}

// 获取结果类型颜色
const getResultTypeColor = (type: string) => {
  switch (type) {
    case 'article': return 'primary'
    case 'author': return 'success'
    case 'topic': return 'warning'
    default: return 'info'
  }
}

// 获取结果类型标签
const getResultTypeLabel = (type: string) => {
  switch (type) {
    case 'article': return '文章'
    case 'author': return '作者'
    case 'topic': return '话题'
    default: return '其他'
  }
}

// 高亮搜索关键词
const highlightText = (text: string) => {
  if (!searchQuery.value) return text
  const regex = new RegExp(`(${searchQuery.value})`, 'gi')
  return text.replace(regex, '<mark>$1</mark>')
}

// 防抖搜索
const debouncedSearch = debounce(() => {
  performSearch()
}, 300)

// 处理搜索
const handleSearch = () => {
  debouncedSearch()
}

// 处理筛选变化
const handleFilterChange = () => {
  currentPage.value = 1
  performSearch()
}

// 处理页码变化
const handlePageChange = () => {
  performSearch()
}

// 清空搜索
const clearSearch = () => {
  searchQuery.value = ''
  results.value = []
  totalResults.value = 0
}

// 执行搜索
const performSearch = async () => {
  if (!searchQuery.value.trim()) {
    results.value = []
    totalResults.value = 0
    return
  }

  try {
    loading.value = true
    const startTime = Date.now()
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500))
    
    // 模拟搜索结果
    const mockResults = [
      {
        type: 'article',
        id: 1,
        title: `包含"${searchQuery.value}"的技术文章标题`,
        summary: `这是一篇关于${searchQuery.value}的详细技术文章，包含了最新的技术趋势和最佳实践...`,
        url: '/articles/1',
        author: {
          username: 'author1',
          nickname: '技术专家',
          avatar: 'https://avatars.githubusercontent.com/u/1?v=4'
        },
        publishedAt: '2024-01-15T10:30:00Z',
        viewCount: 1250
      },
      {
        type: 'author',
        id: 1,
        title: `${searchQuery.value} 专家`,
        summary: `专注于${searchQuery.value}技术领域的资深开发者，拥有丰富的实战经验...`,
        url: '/authors/expert',
        author: null,
        publishedAt: null,
        viewCount: null
      },
      {
        type: 'topic',
        id: 1,
        title: searchQuery.value,
        summary: `关于${searchQuery.value}的技术话题，包含相关文章和讨论...`,
        url: `/topics/${searchQuery.value.toLowerCase()}`,
        author: null,
        publishedAt: null,
        viewCount: null
      }
    ]
    
    results.value = mockResults
    totalResults.value = 156
    searchTime.value = Date.now() - startTime
    
    // 更新URL
    router.replace({
      query: {
        q: searchQuery.value,
        filter: activeFilter.value,
        page: currentPage.value
      }
    })
    
  } catch (error) {
    console.error('搜索失败:', error)
  } finally {
    loading.value = false
  }
}

// 监听搜索查询变化
watch(searchQuery, () => {
  currentPage.value = 1
  debouncedSearch()
})

// 初始化
onMounted(() => {
  // 从URL参数获取搜索查询
  const query = route.query.q as string
  if (query) {
    searchQuery.value = query
    performSearch()
  }
  
  // 从URL参数获取筛选器
  const filter = route.query.filter as string
  if (filter) {
    activeFilter.value = filter
  }
  
  // 从URL参数获取页码
  const page = route.query.page as string
  if (page) {
    currentPage.value = Number(page)
  }
  
  document.title = `搜索${query ? ` - ${query}` : ''} - Ideary`
})
</script>

<style lang="scss" scoped>
.search-page {
  min-height: 100vh;
  background: var(--el-bg-color-page);
}

.page-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem 1rem;
}

.search-header {
  margin-bottom: 2rem;
  
  .page-title {
    font-size: 2rem;
    font-weight: bold;
    color: var(--el-text-color-primary);
    margin-bottom: 1.5rem;
    text-align: center;
  }
  
  .search-box {
    max-width: 600px;
    margin: 0 auto 1rem;
  }
  
  .search-stats {
    text-align: center;
    color: var(--el-text-color-regular);
    font-size: 0.875rem;
  }
}

.search-filters {
  margin-bottom: 2rem;
  text-align: center;
}

.search-results {
  .results-list {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    margin-bottom: 2rem;
  }
  
  .result-item {
    background: var(--el-bg-color);
    border-radius: 0.75rem;
    padding: 1.5rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    
    &:hover {
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    }
    
    .result-type {
      margin-bottom: 0.75rem;
    }
    
    .result-title {
      font-size: 1.25rem;
      font-weight: 600;
      margin-bottom: 0.75rem;
      line-height: 1.4;
      
      a {
        color: var(--el-text-color-primary);
        text-decoration: none;
        
        &:hover {
          color: var(--el-color-primary);
        }
      }
      
      :deep(mark) {
        background: var(--el-color-primary-light-7);
        color: var(--el-color-primary);
        padding: 0.125rem 0.25rem;
        border-radius: 0.25rem;
      }
    }
    
    .result-summary {
      color: var(--el-text-color-regular);
      line-height: 1.6;
      margin-bottom: 1rem;
      
      :deep(mark) {
        background: var(--el-color-primary-light-7);
        color: var(--el-color-primary);
        padding: 0.125rem 0.25rem;
        border-radius: 0.25rem;
      }
    }
    
    .result-meta {
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .author-info {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        color: var(--el-text-color-regular);
        font-size: 0.875rem;
      }
      
      .result-stats {
        display: flex;
        gap: 1rem;
        color: var(--el-text-color-placeholder);
        font-size: 0.875rem;
        
        .view-count {
          display: flex;
          align-items: center;
          gap: 0.25rem;
        }
      }
    }
  }
  
  .empty-results {
    text-align: center;
    padding: 3rem 0;
  }
  
  .search-tips {
    text-align: center;
    padding: 3rem 0;
    
    .tips-content {
      max-width: 500px;
      margin: 0 auto;
      
      h3 {
        font-size: 1.25rem;
        font-weight: 600;
        color: var(--el-text-color-primary);
        margin-bottom: 1rem;
      }
      
      ul {
        text-align: left;
        color: var(--el-text-color-regular);
        line-height: 1.6;
        
        li {
          margin-bottom: 0.5rem;
        }
      }
    }
  }
  
  .pagination-section {
    display: flex;
    justify-content: center;
    margin-top: 2rem;
  }
}
</style>
