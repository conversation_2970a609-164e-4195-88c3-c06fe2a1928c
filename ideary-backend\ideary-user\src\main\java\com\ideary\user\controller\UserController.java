package com.ideary.user.controller;

import com.ideary.user.dto.*;
import com.ideary.user.service.UserService;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 用户信息管理控制器
 * 
 * 功能说明：
 * 1. 获取用户信息
 * 2. 更新用户信息
 * 3. 修改密码
 * 4. 用户关注管理
 * 5. 用户搜索
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 2024-01-01
 */
@RestController
@RequestMapping("/api/v1/users")
@CrossOrigin(origins = "*")
public class UserController {

    private static final Logger logger = LoggerFactory.getLogger(UserController.class);

    @Autowired
    private UserService userService;

    /**
     * 获取当前用户信息
     */
    @GetMapping("/profile")
    public ResponseEntity<Map<String, Object>> getCurrentUserProfile(@RequestHeader("X-User-Id") Long userId) {
        logger.debug("获取当前用户信息，用户ID: {}", userId);

        try {
            UserInfoResponse userInfo = userService.getUserInfo(userId);
            
            Map<String, Object> result = new HashMap<>();
            result.put("code", 200);
            result.put("message", "获取成功");
            result.put("data", userInfo);
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            logger.error("获取用户信息失败: {}", e.getMessage());
            
            Map<String, Object> result = new HashMap<>();
            result.put("code", 400);
            result.put("message", e.getMessage());
            result.put("data", null);
            
            return ResponseEntity.badRequest().body(result);
        }
    }

    /**
     * 根据用户名获取用户信息
     */
    @GetMapping("/{username}")
    public ResponseEntity<Map<String, Object>> getUserByUsername(@PathVariable String username) {
        logger.debug("根据用户名获取用户信息，用户名: {}", username);

        try {
            UserInfoResponse userInfo = userService.getUserInfoByUsername(username);
            
            Map<String, Object> result = new HashMap<>();
            result.put("code", 200);
            result.put("message", "获取成功");
            result.put("data", userInfo);
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            logger.error("获取用户信息失败: {}", e.getMessage());
            
            Map<String, Object> result = new HashMap<>();
            result.put("code", 404);
            result.put("message", e.getMessage());
            result.put("data", null);
            
            return ResponseEntity.status(404).body(result);
        }
    }

    /**
     * 更新用户基本信息
     */
    @PutMapping("/profile")
    public ResponseEntity<Map<String, Object>> updateUserInfo(@RequestHeader("X-User-Id") Long userId,
                                                              @Valid @RequestBody UserUpdateRequest request) {
        logger.info("更新用户基本信息，用户ID: {}", userId);

        try {
            UserInfoResponse userInfo = userService.updateUserInfo(userId, request);
            
            Map<String, Object> result = new HashMap<>();
            result.put("code", 200);
            result.put("message", "更新成功");
            result.put("data", userInfo);
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            logger.error("更新用户信息失败: {}", e.getMessage());
            
            Map<String, Object> result = new HashMap<>();
            result.put("code", 400);
            result.put("message", e.getMessage());
            result.put("data", null);
            
            return ResponseEntity.badRequest().body(result);
        }
    }

    /**
     * 更新用户详细资料
     */
    @PutMapping("/profile/detail")
    public ResponseEntity<Map<String, Object>> updateUserProfile(@RequestHeader("X-User-Id") Long userId,
                                                                 @Valid @RequestBody UserProfileUpdateRequest request) {
        logger.info("更新用户详细资料，用户ID: {}", userId);

        try {
            UserInfoResponse userInfo = userService.updateUserProfile(userId, request);
            
            Map<String, Object> result = new HashMap<>();
            result.put("code", 200);
            result.put("message", "更新成功");
            result.put("data", userInfo);
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            logger.error("更新用户详细资料失败: {}", e.getMessage());
            
            Map<String, Object> result = new HashMap<>();
            result.put("code", 400);
            result.put("message", e.getMessage());
            result.put("data", null);
            
            return ResponseEntity.badRequest().body(result);
        }
    }

    /**
     * 修改密码
     */
    @PutMapping("/password")
    public ResponseEntity<Map<String, Object>> changePassword(@RequestHeader("X-User-Id") Long userId,
                                                              @Valid @RequestBody PasswordChangeRequest request) {
        logger.info("修改密码，用户ID: {}", userId);

        try {
            userService.changePassword(userId, request);
            
            Map<String, Object> result = new HashMap<>();
            result.put("code", 200);
            result.put("message", "密码修改成功");
            result.put("data", null);
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            logger.error("修改密码失败: {}", e.getMessage());
            
            Map<String, Object> result = new HashMap<>();
            result.put("code", 400);
            result.put("message", e.getMessage());
            result.put("data", null);
            
            return ResponseEntity.badRequest().body(result);
        }
    }

    /**
     * 关注用户
     */
    @PostMapping("/{userId}/follow")
    public ResponseEntity<Map<String, Object>> followUser(@RequestHeader("X-User-Id") Long followerId,
                                                          @PathVariable Long userId) {
        logger.info("关注用户，关注者ID: {}, 被关注者ID: {}", followerId, userId);

        try {
            userService.followUser(followerId, userId);
            
            Map<String, Object> result = new HashMap<>();
            result.put("code", 200);
            result.put("message", "关注成功");
            result.put("data", null);
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            logger.error("关注用户失败: {}", e.getMessage());
            
            Map<String, Object> result = new HashMap<>();
            result.put("code", 400);
            result.put("message", e.getMessage());
            result.put("data", null);
            
            return ResponseEntity.badRequest().body(result);
        }
    }

    /**
     * 取消关注用户
     */
    @DeleteMapping("/{userId}/follow")
    public ResponseEntity<Map<String, Object>> unfollowUser(@RequestHeader("X-User-Id") Long followerId,
                                                            @PathVariable Long userId) {
        logger.info("取消关注用户，关注者ID: {}, 被关注者ID: {}", followerId, userId);

        try {
            userService.unfollowUser(followerId, userId);
            
            Map<String, Object> result = new HashMap<>();
            result.put("code", 200);
            result.put("message", "取消关注成功");
            result.put("data", null);
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            logger.error("取消关注用户失败: {}", e.getMessage());
            
            Map<String, Object> result = new HashMap<>();
            result.put("code", 400);
            result.put("message", e.getMessage());
            result.put("data", null);
            
            return ResponseEntity.badRequest().body(result);
        }
    }

    /**
     * 检查是否已关注
     */
    @GetMapping("/{userId}/follow/status")
    public ResponseEntity<Map<String, Object>> checkFollowStatus(@RequestHeader("X-User-Id") Long followerId,
                                                                 @PathVariable Long userId) {
        logger.debug("检查关注状态，关注者ID: {}, 被关注者ID: {}", followerId, userId);

        try {
            boolean isFollowing = userService.isFollowing(followerId, userId);
            
            Map<String, Object> result = new HashMap<>();
            result.put("code", 200);
            result.put("message", "检查完成");
            result.put("data", Map.of("isFollowing", isFollowing));
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            logger.error("检查关注状态失败: {}", e.getMessage());
            
            Map<String, Object> result = new HashMap<>();
            result.put("code", 400);
            result.put("message", e.getMessage());
            result.put("data", null);
            
            return ResponseEntity.badRequest().body(result);
        }
    }

    /**
     * 获取关注列表
     */
    @GetMapping("/{userId}/following")
    public ResponseEntity<Map<String, Object>> getFollowingList(@PathVariable Long userId,
                                                                @RequestParam(defaultValue = "1") int page,
                                                                @RequestParam(defaultValue = "20") int size) {
        logger.debug("获取关注列表，用户ID: {}, 页码: {}, 大小: {}", userId, page, size);

        try {
            List<UserInfoResponse> followingList = userService.getFollowingList(userId, page, size);
            
            Map<String, Object> result = new HashMap<>();
            result.put("code", 200);
            result.put("message", "获取成功");
            result.put("data", followingList);
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            logger.error("获取关注列表失败: {}", e.getMessage());
            
            Map<String, Object> result = new HashMap<>();
            result.put("code", 400);
            result.put("message", e.getMessage());
            result.put("data", null);
            
            return ResponseEntity.badRequest().body(result);
        }
    }

    /**
     * 获取粉丝列表
     */
    @GetMapping("/{userId}/followers")
    public ResponseEntity<Map<String, Object>> getFollowersList(@PathVariable Long userId,
                                                                @RequestParam(defaultValue = "1") int page,
                                                                @RequestParam(defaultValue = "20") int size) {
        logger.debug("获取粉丝列表，用户ID: {}, 页码: {}, 大小: {}", userId, page, size);

        try {
            List<UserInfoResponse> followersList = userService.getFollowersList(userId, page, size);
            
            Map<String, Object> result = new HashMap<>();
            result.put("code", 200);
            result.put("message", "获取成功");
            result.put("data", followersList);
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            logger.error("获取粉丝列表失败: {}", e.getMessage());
            
            Map<String, Object> result = new HashMap<>();
            result.put("code", 400);
            result.put("message", e.getMessage());
            result.put("data", null);
            
            return ResponseEntity.badRequest().body(result);
        }
    }

    /**
     * 搜索用户
     */
    @GetMapping("/search")
    public ResponseEntity<Map<String, Object>> searchUsers(@RequestParam String keyword,
                                                           @RequestParam(defaultValue = "1") int page,
                                                           @RequestParam(defaultValue = "20") int size) {
        logger.debug("搜索用户，关键词: {}, 页码: {}, 大小: {}", keyword, page, size);

        try {
            List<UserInfoResponse> users = userService.searchUsers(keyword, page, size);
            
            Map<String, Object> result = new HashMap<>();
            result.put("code", 200);
            result.put("message", "搜索完成");
            result.put("data", users);
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            logger.error("搜索用户失败: {}", e.getMessage());
            
            Map<String, Object> result = new HashMap<>();
            result.put("code", 400);
            result.put("message", e.getMessage());
            result.put("data", null);
            
            return ResponseEntity.badRequest().body(result);
        }
    }

    /**
     * 获取用户统计信息
     */
    @GetMapping("/{userId}/stats")
    public ResponseEntity<Map<String, Object>> getUserStats(@PathVariable Long userId) {
        logger.debug("获取用户统计信息，用户ID: {}", userId);

        try {
            UserStatsResponse stats = userService.getUserStats(userId);
            
            Map<String, Object> result = new HashMap<>();
            result.put("code", 200);
            result.put("message", "获取成功");
            result.put("data", stats);
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            logger.error("获取用户统计信息失败: {}", e.getMessage());
            
            Map<String, Object> result = new HashMap<>();
            result.put("code", 400);
            result.put("message", e.getMessage());
            result.put("data", null);
            
            return ResponseEntity.badRequest().body(result);
        }
    }
}
