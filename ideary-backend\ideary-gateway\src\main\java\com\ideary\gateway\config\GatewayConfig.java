package com.ideary.gateway.config;

import org.springframework.cloud.gateway.filter.ratelimit.KeyResolver;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import reactor.core.publisher.Mono;

import java.util.Objects;

/**
 * 网关配置类
 * 
 * 功能说明：
 * 1. 限流Key解析器配置
 * 2. 跨域配置
 * 3. 其他网关相关配置
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 2024-01-01
 */
@Configuration
public class GatewayConfig {

    /**
     * 基于IP的限流Key解析器
     * 用于根据客户端IP进行限流
     */
    @Bean
    @Primary
    public KeyResolver ipKeyResolver() {
        return exchange -> {
            // 获取真实IP地址
            String xForwardedFor = exchange.getRequest().getHeaders().getFirst("X-Forwarded-For");
            String xRealIp = exchange.getRequest().getHeaders().getFirst("X-Real-IP");
            String remoteAddress = Objects.requireNonNull(exchange.getRequest().getRemoteAddress()).getAddress().getHostAddress();
            
            String clientIp;
            if (xForwardedFor != null && !xForwardedFor.isEmpty()) {
                // X-Forwarded-For可能包含多个IP，取第一个
                clientIp = xForwardedFor.split(",")[0].trim();
            } else if (xRealIp != null && !xRealIp.isEmpty()) {
                clientIp = xRealIp;
            } else {
                clientIp = remoteAddress;
            }
            
            return Mono.just(clientIp);
        };
    }

    /**
     * 基于用户的限流Key解析器
     * 用于根据用户ID进行限流
     */
    @Bean
    public KeyResolver userKeyResolver() {
        return exchange -> {
            // 从请求头中获取用户ID
            String userId = exchange.getRequest().getHeaders().getFirst("X-User-Id");
            if (userId != null && !userId.isEmpty()) {
                return Mono.just("user:" + userId);
            }
            
            // 如果没有用户ID，则使用IP作为Key
            return ipKeyResolver().resolve(exchange);
        };
    }

    /**
     * 基于API路径的限流Key解析器
     * 用于根据API路径进行限流
     */
    @Bean
    public KeyResolver apiKeyResolver() {
        return exchange -> {
            String path = exchange.getRequest().getURI().getPath();
            return Mono.just("api:" + path);
        };
    }

    /**
     * 组合限流Key解析器
     * 结合用户ID和IP进行限流
     */
    @Bean
    public KeyResolver combinedKeyResolver() {
        return exchange -> {
            String userId = exchange.getRequest().getHeaders().getFirst("X-User-Id");
            String clientIp = Objects.requireNonNull(exchange.getRequest().getRemoteAddress())
                    .getAddress().getHostAddress();
            
            if (userId != null && !userId.isEmpty()) {
                return Mono.just("user:" + userId + ":ip:" + clientIp);
            } else {
                return Mono.just("ip:" + clientIp);
            }
        };
    }
}
