# 测试环境配置文件
server:
  port: 0  # 随机端口

spring:
  application:
    name: ideary-gateway-test
  
  # 配置中心（测试环境禁用）
  cloud:
    nacos:
      discovery:
        enabled: false
      config:
        enabled: false
    
    # 网关配置
    gateway:
      # 全局跨域配置
      globalcors:
        cors-configurations:
          '[/**]':
            allowed-origin-patterns: "*"
            allowed-methods: "*"
            allowed-headers: "*"
            allow-credentials: true
            max-age: 3600
      
      # 测试路由配置
      routes:
        - id: test-route
          uri: http://httpbin.org
          predicates:
            - Path=/test/**
          filters:
            - StripPrefix=1
  
  # Redis配置（测试环境使用内存数据库）
  data:
    redis:
      host: localhost
      port: 6379
      database: 15  # 使用测试数据库

# 管理端点配置
management:
  endpoints:
    web:
      exposure:
        include: health,info
  endpoint:
    health:
      show-details: always

# JWT配置
jwt:
  secret: test-jwt-secret-key
  expiration: 3600000  # 1小时
  refresh-expiration: 7200000  # 2小时
  header: Authorization
  prefix: "Bearer "

# 日志配置
logging:
  level:
    com.ideary.gateway: DEBUG
    org.springframework.cloud.gateway: INFO
    org.springframework.web.reactive: INFO
  pattern:
    console: "%d{HH:mm:ss.SSS} [%thread] %-5level [%logger{36}] - %msg%n"
