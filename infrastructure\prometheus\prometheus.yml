# Prometheus 配置文件

global:
  scrape_interval: 15s
  evaluation_interval: 15s

# 规则文件
rule_files:
  - "rules/*.yml"

# 告警管理器配置
alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

# 抓取配置
scrape_configs:
  # Prometheus 自身监控
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  # Spring Boot 应用监控
  - job_name: 'ideary-gateway'
    metrics_path: '/actuator/prometheus'
    static_configs:
      - targets: ['gateway:9080']
    scrape_interval: 10s

  - job_name: 'ideary-user'
    metrics_path: '/actuator/prometheus'
    static_configs:
      - targets: ['user-service:9081']
    scrape_interval: 10s

  # 基础设施监控
  - job_name: 'mysql'
    static_configs:
      - targets: ['mysql-exporter:9104']

  - job_name: 'redis'
    static_configs:
      - targets: ['redis-exporter:9121']

  - job_name: 'node-exporter'
    static_configs:
      - targets: ['node-exporter:9100']

  # 容器监控
  - job_name: 'cadvisor'
    static_configs:
      - targets: ['cadvisor:8080']

  # Elasticsearch 监控
  - job_name: 'elasticsearch'
    static_configs:
      - targets: ['elasticsearch-exporter:9114']

  # RabbitMQ 监控
  - job_name: 'rabbitmq'
    static_configs:
      - targets: ['rabbitmq:15692']

  # Nacos 监控
  - job_name: 'nacos'
    metrics_path: '/nacos/actuator/prometheus'
    static_configs:
      - targets: ['nacos:8848']

# 远程写入配置（可选）
# remote_write:
#   - url: "http://remote-storage:9201/write"

# 远程读取配置（可选）
# remote_read:
#   - url: "http://remote-storage:9201/read"
